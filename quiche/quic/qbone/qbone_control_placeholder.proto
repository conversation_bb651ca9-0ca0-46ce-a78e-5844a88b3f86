syntax = "proto2";

option optimize_for = LITE_RUNTIME;

package quic;

import "quiche/quic/qbone/qbone_control.proto";

// These provide fields for QboneServerRequest and QboneClientRequest that are
// used to test the control channel.  Once the control channel actually has real
// data to pass they can be removed.
// TODO(b/62139999): Remove this file in favor of testing actual configuration.

extend QboneServerRequest {
  optional string server_placeholder = *********;
}

extend QboneClientRequest {
  optional string client_placeholder = *********;
}
