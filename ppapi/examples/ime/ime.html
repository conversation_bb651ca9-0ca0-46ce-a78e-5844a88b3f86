<!DOCTYPE html>
<html>
<!--
Copyright 2011 The Chromium Authors
Use of this source code is governed by a BSD-style license that can be
found in the LICENSE file.
-->
<head>
<title>IME Example</title>
</head>
<body style="background-color:#eef">
  <p>Full IME Support</p>
  <object id="plugin1" type="application/x-ppapi-example-ime" width="400"
    height="150" border="2px"><param name="ime" value="full" /></object>

  <p>CaretMoveOnly</p>
  <object id="plugin2" type="application/x-ppapi-example-ime" width="400"
    height="150" border="2px"><param name="ime" value="caretmove" /></object>

  <p>IME-Unaware</p>
  <object id="plugin3" type="application/x-ppapi-example-ime" width="400"
    height="150" border="2px"><param name="ime" value="unaware" /></object>

  <p>No IME (explicitly turn IME off)</p>
  <object id="plugin4" type="application/x-ppapi-example-ime" width="400"
    height="150" border="2px"><param name="ime" value="no" /></object>

  <p>HTML Textarea</p>
  <textarea width="200" height="3"></textarea>
</body>
</html>
