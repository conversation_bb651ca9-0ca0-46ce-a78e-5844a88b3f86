# Copyright 2015 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//ppapi/examples/ppapi_example.gni")

group("video_decode") {
  testonly = true
  deps = [
    ":dev",
    ":stable",
  ]
}

ppapi_example("dev") {
  output_name = "ppapi_example_video_decode_dev"
  sources = [
    "testdata.h",
    "video_decode_dev.cc",
  ]
  deps = [
    "//ppapi/cpp",
    "//ppapi/lib/gl/gles2",
  ]
}

ppapi_example("stable") {
  output_name = "ppapi_example_video_decode"
  sources = [
    "testdata.h",
    "video_decode.cc",
  ]
  deps = [
    "//ppapi/cpp",
    "//ppapi/lib/gl/gles2",
  ]
}
