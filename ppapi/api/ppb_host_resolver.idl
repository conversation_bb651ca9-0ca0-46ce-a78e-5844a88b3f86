/* Copyright 2013 The Chromium Authors
 * Use of this source code is governed by a BSD-style license that can be
 * found in the LICENSE file.
 */

/**
 * This file defines the <code>PPB_HostResolver</code> interface.
 */

[generate_thunk]

label Chrome {
  M29 = 1.0
};

/**
 * <code>PP_HostResolver_Flag</code> is an enumeration of flags which can be
 * OR-ed and passed to the host resolver. Currently there is only one flag
 * defined.
 */
[assert_size(4)]
enum PP_HostResolver_Flag {
  /**
   * Hint to request the canonical name of the host, which can be retrieved by
   * <code>GetCanonicalName()</code>.
   */
  PP_HOSTRESOLVER_FLAG_CANONNAME = 1 << 0
};

/**
 * <code>PP_HostResolver_Hint</code> represents hints for host resolution.
 */
[assert_size(8)]
struct PP_HostResolver_Hint {
  /**
   * Network address family.
   */
  PP_NetAddress_Family family;
  /**
   * Combination of flags from <code>PP_HostResolver_Flag</code>.
   */
  int32_t flags;
};

/**
 * The <code>PPB_HostResolver</code> interface supports host name
 * resolution.
 *
 * Permissions: In order to run <code>Resolve()</code>, apps permission
 * <code>socket</code> with subrule <code>resolve-host</code> is required.
 * For more details about network communication permissions, please see:
 * http://developer.chrome.com/apps/app_network.html
 */
interface PPB_HostResolver {
  /**
   * Creates a host resolver resource.
   *
   * @param[in] instance A <code>PP_Instance</code> identifying one instance of
   * a module.
   *
   * @return A <code>PP_Resource</code> corresponding to a host reslover or 0
   * on failure.
   */
  PP_Resource Create([in] PP_Instance instance);

  /**
   * Determines if a given resource is a host resolver.
   *
   * @param[in] resource A <code>PP_Resource</code> to check.
   *
   * @return <code>PP_TRUE</code> if the input is a
   * <code>PPB_HostResolver</code> resource; <code>PP_FALSE</code> otherwise.
   */
  PP_Bool IsHostResolver([in] PP_Resource resource);

  /**
   * Requests resolution of a host name. If the call completes successfully, the
   * results can be retrieved by <code>GetCanonicalName()</code>,
   * <code>GetNetAddressCount()</code> and <code>GetNetAddress()</code>.
   *
   * @param[in] host_resolver A <code>PP_Resource</code> corresponding to a host
   * resolver.
   * @param[in] host The host name (or IP address literal) to resolve.
   * @param[in] port The port number to be set in the resulting network
   * addresses.
   * @param[in] hint A <code>PP_HostResolver_Hint</code> structure providing
   * hints for host resolution.
   * @param[in] callback A <code>PP_CompletionCallback</code> to be called upon
   * completion.
   *
   * @return An int32_t containing an error code from <code>pp_errors.h</code>.
   * <code>PP_ERROR_NOACCESS</code> will be returned if the caller doesn't have
   * required permissions. <code>PP_ERROR_NAME_NOT_RESOLVED</code> will be
   * returned if the host name couldn't be resolved.
   */
  int32_t Resolve([in] PP_Resource host_resolver,
                  [in] str_t host,
                  [in] uint16_t port,
                  [in] PP_HostResolver_Hint hint,
                  [in] PP_CompletionCallback callback);

  /**
   * Gets the canonical name of the host.
   *
   * @param[in] host_resolver A <code>PP_Resource</code> corresponding to a host
   * resolver.
   *
   * @return A string <code>PP_Var</code> on success, which is an empty string
   * if <code>PP_HOSTRESOLVER_FLAG_CANONNAME</code> is not set in the hint flags
   * when calling <code>Resolve()</code>; an undefined <code>PP_Var</code> if
   * there is a pending <code>Resolve()</code> call or the previous
   * <code>Resolve()</code> call failed.
   */
  PP_Var GetCanonicalName([in] PP_Resource host_resolver);

  /**
   * Gets the number of network addresses.
   *
   * @param[in] host_resolver A <code>PP_Resource</code> corresponding to a host
   * resolver.
   *
   * @return The number of available network addresses on success; 0 if there is
   * a pending <code>Resolve()</code> call or the previous
   * <code>Resolve()</code> call failed.
   */
  uint32_t GetNetAddressCount([in] PP_Resource host_resolver);

  /**
   * Gets a network address.
   *
   * @param[in] host_resolver A <code>PP_Resource</code> corresponding to a host
   * resolver.
   * @param[in] index An index indicating which address to return.
   *
   * @return A <code>PPB_NetAddress</code> resource on success; 0 if there is a
   * pending <code>Resolve()</code> call or the previous <code>Resolve()</code>
   * call failed, or the specified index is out of range.
   */
  PP_Resource GetNetAddress([in] PP_Resource host_resolver,
                            [in] uint32_t index);
};
