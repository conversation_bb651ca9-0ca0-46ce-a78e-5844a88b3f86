# Copyright 2015 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

assert(is_nacl,
       "These targets must only be built using the untrusted NaCl toolchains.")

source_set("irt_ppapi") {
  sources = [ "//ppapi/nacl_irt/irt_ppapi.h" ]
}

# We need to actually ship this library in the SDK.
static_library("aot") {
  output_name = "pnacl_irt_shim"
  sources = [
    "irt_shim_ppapi.c",
    "pnacl_shim.c",
    "shim_entry.c",
    "shim_ppapi.c",
  ]
  deps = [
    ":irt_ppapi",
    "//ppapi/c",
  ]

  # Indicate that this variant of the shim library should not depend on
  # the unstable/private IRT hook interface.
  defines = [ "PNACL_SHIM_AOT" ]
}

# We need to actually ship this library with component updater.
# TODO(jvoung): Also strip-debug to keep the size of this down,
# though it is only 14KB with debug info, so not a high priority.
static_library("browser") {
  # Use a different name from the AOT variant. Have the
  # pnacl_support_extension rename this to the canonical name when
  # copying to the final target directory.
  output_name = "pnacl_irt_shim_browser"
  sources = [
    "shim_entry.c",
    "shim_ppapi.c",
  ]
}

# This is an intermediate "library" that gets linked into the IRT
# executable, so this can just be a source_set.
source_set("irt") {
  sources = [
    "irt_shim_ppapi.c",
    "pnacl_shim.c",
  ]
  deps = [
    ":irt_ppapi",
    "//ppapi/c",
  ]
}
