Certificate:
    Data:
        Version: 3 (0x2)
        Serial Number:
            4c:00:36:1b:e5:08:2b:a9:aa:ce:74:0a:05:3e:fb:34
    Signature Algorithm: sha1WithRSAEncryption
        Issuer: C=US, O=VeriSign, Inc., OU=VeriSign Trust Network, OU=(c) 1999 VeriSign, Inc. - For authorized use only, CN=VeriSign Class 3 Public Primary Certification Authority - G3
        Validity
            Not Before: May 18 00:00:00 2008 GMT
            Not After : May 17 23:59:59 2018 GMT
        Subject: C=EG, O=Egypt Trust, OU=VeriSign Trust Network, OU=Terms of use at https://www.egypttrust.com/repository/rpa (c)08, CN=Egypt Trust Class 3 Managed PKI Enterprise Administrator CA
        Subject Public Key Info:
            Public Key Algorithm: rsaEncryption
                Public-Key: (2048 bit)
                Modulus:
                    00:c6:74:6f:42:ae:7c:47:f1:58:f8:79:ad:90:1d:
                    44:17:f3:84:34:9e:15:e2:2a:85:16:e9:4a:a4:c2:
                    95:a8:27:9b:3a:95:db:48:c0:3a:95:d7:79:bb:7c:
                    8b:a3:8e:d9:b7:89:bc:6e:7b:80:b3:ff:f3:3c:ce:
                    0f:b5:b8:05:f0:02:2e:8b:db:2d:0b:40:72:36:ab:
                    9e:2f:a6:fb:bc:cb:bb:ff:8f:09:e7:d8:15:6f:90:
                    77:0c:1a:61:47:67:f3:2a:c2:54:68:1a:1f:16:fc:
                    93:18:1d:79:98:cd:0d:57:10:33:42:df:4d:1f:e8:
                    6f:f8:36:f1:da:c0:d0:d0:52:56:d9:4f:9e:c3:88:
                    74:46:06:86:4b:5d:1e:0e:ba:f8:09:be:06:8e:77:
                    88:7c:a3:9e:5b:e6:2f:e5:ae:87:cf:b5:e2:27:cf:
                    1d:a7:81:c6:06:9d:2d:4d:49:c4:50:50:c7:b1:fb:
                    33:34:df:20:6d:a1:aa:73:11:35:b1:20:6f:43:d1:
                    d1:49:1c:19:12:aa:f4:af:c2:35:38:c8:4f:85:d9:
                    a6:a1:b0:bc:a2:14:f2:00:ba:ee:c5:a3:fe:c5:ae:
                    88:b2:f6:ee:1f:ad:dd:47:da:38:2b:59:5a:5d:c8:
                    fc:98:70:c1:a9:b4:37:d1:e6:e6:be:8f:82:9f:f4:
                    a1:7d
                Exponent: 65537 (0x10001)
        X509v3 extensions:
            X509v3 Basic Constraints: critical
                CA:TRUE, pathlen:0
            X509v3 Certificate Policies: 
                Policy: 2.16.840.1.113733.1.7.23.3
                  CPS: https://www.egypttrust.com/repository/cps
                  User Notice:
                    Explicit Text: https://www.egypttrust.com/repository/rpa

            X509v3 Key Usage: critical
                Certificate Sign, CRL Sign
            Netscape Cert Type: 
                SSL CA, S/MIME CA
            X509v3 Subject Key Identifier: 
                73:63:78:E5:C5:2E:46:04:03:EA:4E:82:08:0E:57:4C:FD:B9:EF:AB
            X509v3 Authority Key Identifier: 
                DirName:/C=US/O=VeriSign, Inc./OU=VeriSign Trust Network/OU=(c) 1999 VeriSign, Inc. - For authorized use only/CN=VeriSign Class 3 Public Primary Certification Authority - G3
                serial:9B:7E:06:49:A3:3E:62:B9:D5:EE:90:48:71:29:EF:57

    Signature Algorithm: sha1WithRSAEncryption
         c1:23:56:6d:c7:4d:44:9d:ef:aa:b9:2f:ca:63:3a:70:5f:7f:
         2e:86:95:c3:23:70:26:49:6c:12:bd:65:1a:37:08:f1:1b:89:
         02:9d:c7:7a:1d:26:56:6d:d8:eb:2c:0d:fd:0f:22:49:93:d3:
         21:39:ea:0b:bf:06:d9:44:59:5f:7f:5f:51:83:36:7f:0e:b2:
         da:9e:62:6d:d2:d3:ca:b9:d7:31:f9:b3:b5:7b:14:9e:f6:e2:
         c4:28:46:20:12:03:1d:2a:48:63:4f:23:ed:c8:8e:fd:95:c3:
         c9:a8:ff:32:fd:c9:d0:79:2f:d3:51:5c:a5:7a:44:c0:15:9d:
         49:2d:57:09:2c:6b:11:1a:f5:a1:6d:3a:66:bd:22:f7:9a:b0:
         4f:ea:a3:58:c0:e3:ef:a7:1e:49:2b:18:e0:8e:26:38:73:11:
         b4:0d:55:d4:78:87:28:da:5a:8e:d1:72:bc:66:9b:98:ad:15:
         c9:84:81:da:24:99:42:6d:0b:94:d3:a2:28:a0:61:09:95:ba:
         95:3b:2a:26:3e:17:5f:d9:25:cc:fe:8c:11:26:69:f5:0d:68:
         81:06:ce:90:38:52:93:19:36:74:6f:4e:ed:6f:37:bc:03:3c:
         4e:77:1f:59:d3:62:ab:6a:ff:eb:f2:90:2b:31:ce:18:d9:90:
         a1:d6:aa:0d
-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----
