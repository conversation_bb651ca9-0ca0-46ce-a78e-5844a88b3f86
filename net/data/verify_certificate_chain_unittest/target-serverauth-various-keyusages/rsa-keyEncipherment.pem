[Created by: generate-chains.py]

Certificate chain where the target certificate uses a RSA key and has the single key usage keyEncipherment

Certificate:
    Data:
        Version: 3 (0x2)
        Serial Number:
            72:41:46:e2:6f:47:ae:e6:ef:ca:22:1e:bc:61:9b:ed:27:e8:61:46
        Signature Algorithm: sha256WithRSAEncryption
        Issuer: CN=Intermediate
        Validity
            Not Before: Oct  5 12:00:00 2021 GMT
            Not After : Oct  5 12:00:00 2022 GMT
        Subject: CN=Target
        Subject Public Key Info:
            Public Key Algorithm: rsaEncryption
                RSA Public-Key: (2048 bit)
                Modulus:
                    00:c8:f4:c3:ff:ae:99:44:dc:07:19:62:59:1d:ba:
                    0e:13:d0:78:b6:a0:7f:ca:74:4f:a4:c2:5c:de:04:
                    e2:ea:22:d9:28:4d:45:54:14:4c:e8:56:d4:7b:8a:
                    64:fa:f9:e4:14:23:c0:ad:cc:25:63:38:59:55:00:
                    51:e2:82:ac:ae:47:3b:da:e2:95:79:1c:b5:96:ca:
                    d2:4a:3c:f7:b8:7a:cc:9e:e2:8d:d0:17:0b:bc:6e:
                    0e:3c:c2:05:3a:21:64:00:db:c3:08:b5:e6:cf:78:
                    5d:2b:3c:ba:54:a9:5a:64:4e:1b:d8:3a:eb:14:d3:
                    5a:ad:da:6f:d6:a2:e1:7e:0f:ff:ce:71:85:14:c7:
                    fe:54:65:f9:c6:47:73:ae:6e:9b:81:e7:55:cb:7e:
                    ea:76:a8:48:f9:62:25:40:0e:d1:cf:90:8f:68:f7:
                    94:20:69:a4:14:8a:14:24:53:3d:55:b8:5a:31:ae:
                    7c:36:74:d2:c2:2c:ce:7b:e9:83:69:56:16:ec:f9:
                    bb:7c:b0:14:6f:95:12:da:7c:b0:23:b8:5d:41:dc:
                    bc:ad:24:d9:b6:6a:3f:82:6c:74:a0:73:68:5a:d3:
                    e4:f2:0a:93:b9:1c:ee:bb:ce:e2:d8:86:86:8e:e0:
                    24:58:e3:ec:f5:e3:84:36:e9:a6:62:72:5c:4b:0c:
                    9c:15
                Exponent: 65537 (0x10001)
        X509v3 extensions:
            X509v3 Subject Key Identifier: 
                F1:A9:E2:E9:58:C3:31:AD:E2:60:9B:55:C0:A2:0A:80:7B:15:10:2D
            X509v3 Authority Key Identifier: 
                keyid:80:14:1E:04:C6:A0:C4:28:25:30:28:59:BD:40:1A:65:07:05:18:38

            Authority Information Access: 
                CA Issuers - URI:http://url-for-aia/Intermediate.cer

            X509v3 CRL Distribution Points: 

                Full Name:
                  URI:http://url-for-crl/Intermediate.crl

            X509v3 Key Usage: critical
                Key Encipherment
            X509v3 Extended Key Usage: 
                TLS Web Server Authentication
    Signature Algorithm: sha256WithRSAEncryption
         17:3c:bc:3f:74:4f:8e:91:91:64:c4:e6:54:94:84:2d:a9:64:
         0f:fc:2c:42:da:2e:19:bd:8b:f0:a7:51:eb:d3:74:04:17:f9:
         89:c1:d5:04:33:67:3a:d1:c9:eb:c4:61:68:ef:6a:e3:b3:95:
         36:5f:4f:72:02:81:b9:4e:7a:68:65:3e:bb:19:ad:d9:ac:e4:
         96:45:55:07:ae:94:d5:55:14:4a:c4:b2:9e:df:a6:8d:22:c4:
         ce:56:4b:92:da:3d:4a:7d:23:53:a4:6c:61:60:f4:f7:68:6d:
         22:79:3c:5f:28:75:69:15:7c:86:55:81:5e:67:b1:fd:40:4a:
         5a:3e:c9:9a:54:8a:ff:17:f6:80:10:fb:8f:e4:8f:ef:5d:0f:
         00:94:3d:c1:67:f1:e7:a5:b0:b8:e2:b7:9b:1f:f1:4c:3b:7d:
         92:ca:d4:57:16:f5:1b:37:6a:49:48:58:da:ec:2b:8c:e2:d8:
         42:7a:a9:c1:b0:a9:94:cd:5e:4d:35:ca:85:44:3b:36:64:76:
         e1:85:c4:c8:a1:d9:77:54:ae:73:b8:2e:47:18:ba:b1:34:34:
         9b:21:42:64:9f:26:b3:8b:d7:72:55:6e:43:70:cb:b2:79:5b:
         34:80:52:ac:d0:d7:04:0d:64:5f:70:d1:02:ba:f5:ec:80:e8:
         10:d6:8a:ff
-----BEGIN CERTIFICATE-----
MIIDljCCAn6gAwIBAgIUckFG4m9HrubvyiIevGGb7SfoYUYwDQYJKoZIhvcNAQEL
BQAwFzEVMBMGA1UEAwwMSW50ZXJtZWRpYXRlMB4XDTIxMTAwNTEyMDAwMFoXDTIy
MTAwNTEyMDAwMFowETEPMA0GA1UEAwwGVGFyZ2V0MIIBIjANBgkqhkiG9w0BAQEF
AAOCAQ8AMIIBCgKCAQEAyPTD/66ZRNwHGWJZHboOE9B4tqB/ynRPpMJc3gTi6iLZ
KE1FVBRM6FbUe4pk+vnkFCPArcwlYzhZVQBR4oKsrkc72uKVeRy1lsrSSjz3uHrM
nuKN0BcLvG4OPMIFOiFkANvDCLXmz3hdKzy6VKlaZE4b2DrrFNNardpv1qLhfg//
znGFFMf+VGX5xkdzrm6bgedVy37qdqhI+WIlQA7Rz5CPaPeUIGmkFIoUJFM9Vbha
Ma58NnTSwizOe+mDaVYW7Pm7fLAUb5US2nywI7hdQdy8rSTZtmo/gmx0oHNoWtPk
8gqTuRzuu87i2IaGjuAkWOPs9eOENummYnJcSwycFQIDAQABo4HfMIHcMB0GA1Ud
DgQWBBTxqeLpWMMxreJgm1XAogqAexUQLTAfBgNVHSMEGDAWgBSAFB4ExqDEKCUw
KFm9QBplBwUYODA/BggrBgEFBQcBAQQzMDEwLwYIKwYBBQUHMAKGI2h0dHA6Ly91
cmwtZm9yLWFpYS9JbnRlcm1lZGlhdGUuY2VyMDQGA1UdHwQtMCswKaAnoCWGI2h0
dHA6Ly91cmwtZm9yLWNybC9JbnRlcm1lZGlhdGUuY3JsMA4GA1UdDwEB/wQEAwIF
IDATBgNVHSUEDDAKBggrBgEFBQcDATANBgkqhkiG9w0BAQsFAAOCAQEAFzy8P3RP
jpGRZMTmVJSELalkD/wsQtouGb2L8KdR69N0BBf5icHVBDNnOtHJ68RhaO9q47OV
Nl9PcgKBuU56aGU+uxmt2azklkVVB66U1VUUSsSynt+mjSLEzlZLkto9Sn0jU6Rs
YWD092htInk8Xyh1aRV8hlWBXmex/UBKWj7JmlSK/xf2gBD7j+SP710PAJQ9wWfx
56WwuOK3mx/xTDt9ksrUVxb1GzdqSUhY2uwrjOLYQnqpwbCplM1eTTXKhUQ7NmR2
4YXEyKHZd1Suc7guRxi6sTQ0myFCZJ8ms4vXclVuQ3DLsnlbNIBSrNDXBA1kX3DR
Arr17IDoENaK/w==
-----END CERTIFICATE-----

Certificate:
    Data:
        Version: 3 (0x2)
        Serial Number:
            31:2c:98:11:ad:7d:7c:47:ab:8a:21:e3:43:a7:12:a0:c9:a9:32:4f
        Signature Algorithm: sha256WithRSAEncryption
        Issuer: CN=Root
        Validity
            Not Before: Oct  5 12:00:00 2021 GMT
            Not After : Oct  5 12:00:00 2022 GMT
        Subject: CN=Intermediate
        Subject Public Key Info:
            Public Key Algorithm: rsaEncryption
                RSA Public-Key: (2048 bit)
                Modulus:
                    00:ad:ea:23:0c:dc:31:55:3d:23:7a:a3:0f:5e:16:
                    9e:62:44:e4:28:bf:0b:b0:76:a3:5e:71:e7:a9:85:
                    52:1e:ae:3b:2b:21:0d:95:35:73:c2:c0:a0:78:13:
                    d3:fc:27:57:8a:98:de:83:e3:8f:a3:13:a5:20:a7:
                    a9:bf:c8:f5:02:1b:08:1a:2e:a5:04:7a:11:24:a1:
                    59:11:3d:c0:3b:57:35:62:cd:2a:41:cd:5c:15:35:
                    ac:c8:66:f8:a9:f2:d4:1c:0c:b4:dc:9d:7b:99:f9:
                    07:1b:c9:f0:03:6a:6c:71:d8:e6:cb:7a:ec:c7:a7:
                    b7:36:0b:f0:49:22:37:22:f4:77:f3:1a:a6:57:59:
                    0b:6c:6a:3b:b8:d1:47:df:48:ac:54:ee:82:1e:39:
                    14:dc:a6:9c:ab:33:b7:87:4f:3b:70:55:af:db:40:
                    e5:26:a9:0f:bc:59:8f:61:92:47:0a:34:73:de:fb:
                    25:fa:f1:bd:a4:03:35:93:5e:57:a3:a3:3f:13:e6:
                    09:68:c5:51:29:20:53:b8:5e:fa:8a:ce:e8:0a:ef:
                    9e:07:6e:55:f7:f6:c1:e7:12:9f:22:c1:72:b8:2a:
                    af:25:34:eb:08:28:36:73:8e:f6:80:70:21:16:8a:
                    81:2b:69:47:62:c5:ef:7e:dd:80:7c:09:4e:6d:57:
                    86:4f
                Exponent: 65537 (0x10001)
        X509v3 extensions:
            X509v3 Subject Key Identifier: 
                80:14:1E:04:C6:A0:C4:28:25:30:28:59:BD:40:1A:65:07:05:18:38
            X509v3 Authority Key Identifier: 
                keyid:71:50:52:35:92:69:F7:54:B1:8A:0C:C1:93:87:3E:84:E9:BE:5C:80

            Authority Information Access: 
                CA Issuers - URI:http://url-for-aia/Root.cer

            X509v3 CRL Distribution Points: 

                Full Name:
                  URI:http://url-for-crl/Root.crl

            X509v3 Key Usage: critical
                Certificate Sign, CRL Sign
            X509v3 Basic Constraints: critical
                CA:TRUE
    Signature Algorithm: sha256WithRSAEncryption
         9b:a2:b5:c1:e1:98:7e:0f:1d:2a:0e:eb:b8:65:57:0f:4d:78:
         85:7e:81:cb:0f:8c:04:5c:1a:40:6b:f1:00:71:61:59:0b:19:
         3a:f0:97:a3:94:38:55:35:65:9b:b7:0c:a3:a0:99:eb:1d:f7:
         68:ed:3c:21:90:99:80:69:52:00:71:3d:b1:1d:64:66:62:c3:
         37:4e:f4:38:1e:c9:14:3f:50:66:2f:b9:f5:11:31:2c:87:30:
         1e:f1:1b:14:c3:90:05:ba:bc:57:06:39:6a:e9:46:f1:67:6a:
         a9:9b:16:04:b9:bd:93:bb:df:48:5a:77:9d:03:07:bb:8b:2e:
         2d:88:4d:aa:de:d3:73:2e:d8:04:e8:25:8b:ea:9b:e0:c5:87:
         5f:64:56:ac:b4:4e:74:ac:21:ac:ec:6a:a9:54:cd:77:24:2e:
         bd:56:b6:70:26:a8:81:b6:1a:69:31:4a:53:5e:93:46:f4:28:
         40:90:6b:65:44:94:3b:61:f8:5f:8c:b7:31:e7:46:a6:79:e0:
         33:38:69:27:6b:b3:6e:5c:e4:f7:5a:2f:f9:bc:67:57:3e:e3:
         dd:3a:99:5f:3a:ac:c7:5f:b7:5b:38:4b:ab:e1:1a:12:ec:e1:
         95:0a:2d:54:a4:99:30:d0:93:78:27:5b:a4:d5:69:5b:c8:c1:
         ef:ff:84:02
-----BEGIN CERTIFICATE-----
MIIDgDCCAmigAwIBAgIUMSyYEa19fEeriiHjQ6cSoMmpMk8wDQYJKoZIhvcNAQEL
BQAwDzENMAsGA1UEAwwEUm9vdDAeFw0yMTEwMDUxMjAwMDBaFw0yMjEwMDUxMjAw
MDBaMBcxFTATBgNVBAMMDEludGVybWVkaWF0ZTCCASIwDQYJKoZIhvcNAQEBBQAD
ggEPADCCAQoCggEBAK3qIwzcMVU9I3qjD14WnmJE5Ci/C7B2o15x56mFUh6uOysh
DZU1c8LAoHgT0/wnV4qY3oPjj6MTpSCnqb/I9QIbCBoupQR6ESShWRE9wDtXNWLN
KkHNXBU1rMhm+Kny1BwMtNyde5n5BxvJ8ANqbHHY5st67MentzYL8EkiNyL0d/Ma
pldZC2xqO7jRR99IrFTugh45FNymnKszt4dPO3BVr9tA5SapD7xZj2GSRwo0c977
JfrxvaQDNZNeV6OjPxPmCWjFUSkgU7he+orO6ArvngduVff2wecSnyLBcrgqryU0
6wgoNnOO9oBwIRaKgStpR2LF737dgHwJTm1Xhk8CAwEAAaOByzCByDAdBgNVHQ4E
FgQUgBQeBMagxCglMChZvUAaZQcFGDgwHwYDVR0jBBgwFoAUcVBSNZJp91SxigzB
k4c+hOm+XIAwNwYIKwYBBQUHAQEEKzApMCcGCCsGAQUFBzAChhtodHRwOi8vdXJs
LWZvci1haWEvUm9vdC5jZXIwLAYDVR0fBCUwIzAhoB+gHYYbaHR0cDovL3VybC1m
b3ItY3JsL1Jvb3QuY3JsMA4GA1UdDwEB/wQEAwIBBjAPBgNVHRMBAf8EBTADAQH/
MA0GCSqGSIb3DQEBCwUAA4IBAQCborXB4Zh+Dx0qDuu4ZVcPTXiFfoHLD4wEXBpA
a/EAcWFZCxk68JejlDhVNWWbtwyjoJnrHfdo7TwhkJmAaVIAcT2xHWRmYsM3TvQ4
HskUP1BmL7n1ETEshzAe8RsUw5AFurxXBjlq6UbxZ2qpmxYEub2Tu99IWnedAwe7
iy4tiE2q3tNzLtgE6CWL6pvgxYdfZFastE50rCGs7GqpVM13JC69VrZwJqiBthpp
MUpTXpNG9ChAkGtlRJQ7YfhfjLcx50ameeAzOGkna7NuXOT3Wi/5vGdXPuPdOplf
OqzHX7dbOEur4RoS7OGVCi1UpJkw0JN4J1uk1WlbyMHv/4QC
-----END CERTIFICATE-----

Certificate:
    Data:
        Version: 3 (0x2)
        Serial Number:
            31:2c:98:11:ad:7d:7c:47:ab:8a:21:e3:43:a7:12:a0:c9:a9:32:4e
        Signature Algorithm: sha256WithRSAEncryption
        Issuer: CN=Root
        Validity
            Not Before: Oct  5 12:00:00 2021 GMT
            Not After : Oct  5 12:00:00 2022 GMT
        Subject: CN=Root
        Subject Public Key Info:
            Public Key Algorithm: rsaEncryption
                RSA Public-Key: (2048 bit)
                Modulus:
                    00:b2:df:0b:0b:a2:8a:9c:9c:68:ad:24:f9:f9:e6:
                    95:ef:e4:71:1e:00:fb:ec:bf:b1:cb:6e:d6:40:30:
                    42:7a:38:87:9e:5b:cd:58:3e:b7:31:5c:8d:0d:84:
                    7a:be:ef:e6:98:ba:4e:c5:ae:2b:2f:66:b2:b2:3f:
                    e7:c0:a8:8b:75:60:61:2a:67:8a:d2:55:72:83:e2:
                    ff:de:76:d6:63:58:79:ba:91:c1:80:e1:06:1b:35:
                    9c:b3:e9:19:f8:c7:21:80:dd:9e:04:18:35:de:df:
                    00:80:21:09:06:3c:6c:9a:de:10:d1:fd:4d:2f:27:
                    5e:56:24:44:67:88:48:e4:25:52:cc:c7:17:3f:69:
                    1f:76:fc:b1:83:45:48:5d:37:da:80:3e:3d:7f:f6:
                    8d:ec:83:00:d6:2f:c4:41:5d:79:ae:ad:14:4c:24:
                    b3:5e:1e:fe:ed:e8:39:17:4a:4b:ca:eb:10:5d:6e:
                    af:38:58:b1:12:a5:69:bd:bd:63:dd:f5:21:71:2c:
                    36:37:72:51:3b:3a:de:8f:1e:a0:e2:6b:45:da:02:
                    ad:7e:04:b7:d3:c8:64:34:c6:f4:08:a0:e5:c9:02:
                    1b:6f:42:22:be:d3:8b:7b:63:6e:39:c4:97:17:c6:
                    9a:ba:9e:26:9b:e1:65:a6:4d:4a:93:5e:b2:4e:18:
                    9d:c1
                Exponent: 65537 (0x10001)
        X509v3 extensions:
            X509v3 Subject Key Identifier: 
                71:50:52:35:92:69:F7:54:B1:8A:0C:C1:93:87:3E:84:E9:BE:5C:80
            X509v3 Authority Key Identifier: 
                keyid:71:50:52:35:92:69:F7:54:B1:8A:0C:C1:93:87:3E:84:E9:BE:5C:80

            Authority Information Access: 
                CA Issuers - URI:http://url-for-aia/Root.cer

            X509v3 CRL Distribution Points: 

                Full Name:
                  URI:http://url-for-crl/Root.crl

            X509v3 Key Usage: critical
                Certificate Sign, CRL Sign
            X509v3 Basic Constraints: critical
                CA:TRUE
    Signature Algorithm: sha256WithRSAEncryption
         6f:c2:f5:0d:cc:f7:a8:c4:03:50:ff:33:02:72:38:ac:72:19:
         da:fe:1e:d0:20:79:44:d9:ce:cf:22:b1:09:78:16:5d:12:12:
         aa:bd:9b:27:08:6f:18:08:08:7d:d2:06:52:a1:ae:19:35:61:
         be:27:8f:20:e4:59:d5:f3:4e:56:55:77:28:2b:f8:a4:9c:8d:
         75:d8:4f:99:92:75:0f:a1:79:c5:47:6b:32:c7:86:a2:ce:17:
         55:ab:78:b0:ac:b4:15:04:19:0f:55:f3:09:ca:48:84:bf:32:
         01:58:04:33:75:84:57:8e:67:32:53:a7:09:1b:13:c0:3a:9f:
         65:2e:e2:82:26:1a:9c:47:c2:4f:bb:b9:c4:d2:b9:b3:cc:86:
         bc:25:03:3c:6a:66:e2:82:58:03:fb:3e:dd:f5:a3:cc:d5:b9:
         1f:53:d5:63:6a:2c:a2:36:0a:8a:a8:15:bf:63:50:ad:db:98:
         e8:40:97:24:2c:80:27:35:e1:53:27:da:26:86:72:67:ea:b7:
         a1:30:5a:67:3f:ff:2c:3e:a4:7f:8a:6e:ff:e2:41:f2:02:34:
         8e:12:59:c0:c6:80:3b:78:d9:fd:1a:b8:e1:b5:b7:7f:c8:63:
         27:30:4d:00:7a:96:f1:4d:6e:a0:e9:da:9f:11:a2:b4:69:2f:
         8d:d4:9c:1a
-----BEGIN CERTIFICATE-----
MIIDeDCCAmCgAwIBAgIUMSyYEa19fEeriiHjQ6cSoMmpMk4wDQYJKoZIhvcNAQEL
BQAwDzENMAsGA1UEAwwEUm9vdDAeFw0yMTEwMDUxMjAwMDBaFw0yMjEwMDUxMjAw
MDBaMA8xDTALBgNVBAMMBFJvb3QwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEK
AoIBAQCy3wsLooqcnGitJPn55pXv5HEeAPvsv7HLbtZAMEJ6OIeeW81YPrcxXI0N
hHq+7+aYuk7FrisvZrKyP+fAqIt1YGEqZ4rSVXKD4v/edtZjWHm6kcGA4QYbNZyz
6Rn4xyGA3Z4EGDXe3wCAIQkGPGya3hDR/U0vJ15WJERniEjkJVLMxxc/aR92/LGD
RUhdN9qAPj1/9o3sgwDWL8RBXXmurRRMJLNeHv7t6DkXSkvK6xBdbq84WLESpWm9
vWPd9SFxLDY3clE7Ot6PHqDia0XaAq1+BLfTyGQ0xvQIoOXJAhtvQiK+04t7Y245
xJcXxpq6niab4WWmTUqTXrJOGJ3BAgMBAAGjgcswgcgwHQYDVR0OBBYEFHFQUjWS
afdUsYoMwZOHPoTpvlyAMB8GA1UdIwQYMBaAFHFQUjWSafdUsYoMwZOHPoTpvlyA
MDcGCCsGAQUFBwEBBCswKTAnBggrBgEFBQcwAoYbaHR0cDovL3VybC1mb3ItYWlh
L1Jvb3QuY2VyMCwGA1UdHwQlMCMwIaAfoB2GG2h0dHA6Ly91cmwtZm9yLWNybC9S
b290LmNybDAOBgNVHQ8BAf8EBAMCAQYwDwYDVR0TAQH/BAUwAwEB/zANBgkqhkiG
9w0BAQsFAAOCAQEAb8L1Dcz3qMQDUP8zAnI4rHIZ2v4e0CB5RNnOzyKxCXgWXRIS
qr2bJwhvGAgIfdIGUqGuGTVhviePIORZ1fNOVlV3KCv4pJyNddhPmZJ1D6F5xUdr
MseGos4XVat4sKy0FQQZD1XzCcpIhL8yAVgEM3WEV45nMlOnCRsTwDqfZS7igiYa
nEfCT7u5xNK5s8yGvCUDPGpm4oJYA/s+3fWjzNW5H1PVY2osojYKiqgVv2NQrduY
6ECXJCyAJzXhUyfaJoZyZ+q3oTBaZz//LD6kf4pu/+JB8gI0jhJZwMaAO3jZ/Rq4
4bW3f8hjJzBNAHqW8U1uoOnanxGitGkvjdScGg==
-----END CERTIFICATE-----
