# Copyright 2016 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

# Fuzzer dictionary targetting HTTP/1.x responses.

# Entries that are generally useful in headers
":"
"\x0A"
"\x0D"
"0"
"50"
"500"
# Horizontal whitespace. Matters mostly in status line.
" "
"\x09"
# Header continuation
"\x0D\x0A\x09"
# Used in a lot of individual headers
";"
"="
","
"\""
"-"

# Status line components
"HTTP"
"/1.1"
"/1.0"
# More interesting status codes.  Leading space so can be inserted into
# other status lines.
" 100"
" 200"
" 206"
" 301"
" 302"
" 303"
" 304"
" 307"
" 308"
" 401"
" 403"
" 404"
" 500"
" 501"
" 403"

# Full status lines (Some with relevant following headers)
"HTTP/1.1 200 OK\x0A\x0A"
"HTTP/1.1 100 Continue\x0A\x0A"
"HTTP/1.1 401 Unauthorized\x0AWWW-Authenticate: Basic realm=\"Middle-Earth\"\x0A\xA0"
"HTTP/1.1 407 Proxy Authentication Required\x0AProxy-Authenticate: Digest realm=\"Middle-Earth\", nonce=\"aaaaaaaaaa\"\x0A\x0A"
"HTTP/1.0 301 Moved Permanently\x0ALocation: /a\x0A\x0A"
"HTTP/1.1 302 Found\x0ALocation: http://lost/\x0A\x0A"

# Proxy authentication headers. Note that fuzzers don't support NTLM or
# negotiate.
"WWW-Authenticate:"
"Proxy-Authenticate:"
"Basic"
"Digest"
"realm"
"nonce"

"Connection:"
"Proxy-Connection:"
"Keep-Alive"
"Close"
"Upgrade"
"\x0AConnection: Keep-Alive"
"\x0AConnection: Close"
"\x0AProxy-Connection: Keep-Alive"
"\x0AProxy-Connection: Close"

"Content-Length:"
"Transfer-Encoding:"
"chunked"
"\x0AContent-Length: 0"
"\x0AContent-Length: 500"
"\x0ATransfer-Encoding: chunked\x0A\x0A5\x0A12345\x0A0\x0A\x0A"

"Location:"
"\x0ALocation: http://foo/"
"\x0ALocation: http://bar/"
"\x0ALocation: https://foo/"
"\x0ALocation: https://bar/"

"Accept-Ranges:"
"bytes"
"\x0AAccept-Ranges: bytes"

"Content-Range:"

"Age:"
"\x0AAge: 0"
"\x0AAge: 3153600000"

"Cache-Control:"
"max-age"
"no-cache"
"no-store"
"must-revalidate"
"\x0ACache-Control: max-age=3153600000"
"\x0ACache-Control: max-age=0"
"\x0ACache-Control: no-cache"
"\x0ACache-Control: no-store"
"\x0ACache-Control: must-revalidate"

"Content-Disposition:"
"attachment"
"filename"

"Content-Encoding:"
"gzip"
"deflate"
"sdch"
"br"
"\x0AContent-Encoding: gzip"
"\x0AContent-Encoding: deflate"
"\x0AContent-Encoding: sdch"
"\x0AContent-Encoding: br"

"Date:"
"Fri, 01 Apr, 2050 14:14:14 GMT"
"Mon, 28 Mar, 2016 04:04:04 GMT"
"\x0ADate: Fri, 01 Apr, 2050 14:14:14 GMT"
"\x0ADate: Mon, 28 Mar, 2016 04:04:04 GMT"

"Last-Modified:"
"\x0ALast-Modified: Fri, 01 Apr, 2050 14:14:14 GMT"
"\x0ALast-Modified: Mon, 28 Mar, 2016 04:04:04 GMT"

"Expires:"
"\x0AExpires: Fri, 01 Apr, 2050 14:14:14 GMT"
"\x0AExpires: Mon, 28 Mar, 2016 04:04:04 GMT"

"Set-Cookie:"
"Expires"
"Max-Age"
"Domain"
"Path"
"Secure"
"HttpOnly"
"Priority"
"Low"
"Medium"
"High"
"SameSite"
"Strict"
"Lax"
"\x0ASet-Cookie: foo=bar"
"\x0ASet-Cookie: foo2=bar2;HttpOnly;Priority=Low;SameSite=Strict;Path=/"
"\x0ASet-Cookie: foo=chicken;SameSite=Lax"

"Strict-Transport-Security:"
"includeSubDomains"

"Vary:"
"\x0AVary: Cookie"
"\x0AVary: Age"

"ETag:"
"\x0AETag: jumboshrimp"


# This part has been generated with testing/libfuzzer/dictionary_generator.py
# using net_http_proxy_client_socket_fuzzer binary and RFC 2616.
"all"
"code"
"maximum"
"Transfer-Encoding"
"D.,"
"results"
"follow"
"(LZW)."
"provided."
"(which"
"ISDN"
"\"TE\""
"LF>"
"FORCE"
"calculate"
"\"IETF"
"UNIX,"
"ARPA"
"\"OPTIONAL\""
"environment"
"Host"
"program"
"USENET"
"TEXT"
"Not"
"Nov"
"include"
"resources"
"CONNECT"
"digit"
"supported"
"string"
"returning"
"ALL"
"HTTP/1.1;"
"SP,"
"SP."
"entries"
"HTTP/1.1,"
"HTTP/1.1."
"difference"
"(URI):"
"--"
"[CRLF]"
"EXPRESS"
"list"
"HTTP/1.0\","
"(RFC"
"large"
"ONLY"
"Tag"
"(LWS"
"enclosing"
"\"SHOULD\","
"(URL)\","
"\"A\"..\"Z\">"
"unexpected"
"GET)"
"\"HEAD\""
"direct"
"Failed"
"second"
"Version"
"\"A\""
"allowed."
"pass"
"GET,"
"tag."
"implemented"
"\"HTTP/1.0\""
"INFRINGE"
"errors"
"ISO-8859-4,"
"appear"
"opaque"
"section"
"CPU"
"current"
"waiting"
"version"
"above"
"TTL"
"shared"
"CRLF)"
"public"
"FTP"
"NNTP."
"WWW-"
"never"
"equals"
"\"HTTP/1.1"
"reported"
"objects"
"address"
"active"
"path"
"["
"\"POST\""
"HTTP."
"change"
"MA"
"\"AS"
"broken"
"BACK)"
"NOT"
"NNTP"
"named"
"useful"
"secure"
"family"
"case."
"detected."
"\"HTTP\""
"private"
"CERN/3.0"
"CTE"
"(CTE)"
"Too"
"CTL"
"PUT,"
"user-agent"
"PUT)"
"byte"
"select"
"use"
"TASK"
"from"
"exception."
"working"
"to"
"value."
"WARRANTIES"
"two"
"URI;"
"User-Agent"
"few"
"--THIS_STRING_SEPARATES"
"POST,"
"call"
"6"
"MUST,"
"scope"
"type"
"authorization"
"more"
"ISO-8859-9,"
"(GMT),"
"(TE)"
"name."
"initial"
"Required"
"RFC-850"
"warn"
"bytes,"
"Found"
"cases"
"MHTML"
"name:"
"must"
"parse"
"lowercase"
"MHTML,"
"RIGHTS"
"this"
"NTP"
"work"
"--THIS_STRING_SEPARATES--"
"Syntax"
"paragraph"
"can"
"tracing"
"following"
"\"I"
"closing"
"modifier"
"root"
"example"
"requested,"
"J.,"
"control"
"type."
"reserved"
"links"
"process"
"attribute"
"allowed"
"high"
"currency"
"numbers"
"want"
"type:"
"native"
"LF"
"class,"
"end"
"Missing"
"HTTP-"
"HTTP,"
"charset"
"1"
"line."
"2*N"
"H."
"1XX"
"WARRANTIES,"
"HTTP:"
"A"
"badly"
"HEAD"
"may"
"insecure"
"after"
"variant"
"different"
"wrong"
"[SP"
"ANSI,"
"date"
"such"
"data"
"parallel"
"repeat"
"a"
"FTP,"
"All"
"short"
"\"GET\""
"Y."
"UA"
"(2**N),"
"element"
"so"
"cases."
"File"
"(LWS)"
"\"DEFLATE"
"order"
"\"SHOULD"
"don't"
"MIC"
"move"
"vary"
"satisfied"
"CD-ROM,"
"ended"
"HTTP-WG."
"LINK,"
"pointer"
"its"
"digest"
"before"
"HTML"
"(OK)"
"using:"
"MAY,"
"fix"
"ISO-3166"
"actually"
"407"
"(GNU"
"\"HTTP/1.1\","
"P.,"
"401"
"MERCHANTABILITY"
"DNS."
"into"
"\"HTTP"
"it."
"it,"
"return"
"combination"
"URL"
"URI"
"number"
"Bad"
"not"
"However,"
"SSL"
"name"
"always"
"decimal"
"expectation."
"did"
"ISO-639"
"]URI,"
"found"
"trailer"
"mean"
"breakdown"
"domain"
"From"
"UTC"
"(via"
"(URI)"
"UNLINK"
"used"
"expect"
"exceeded"
"(MIC)"
"event"
"out"
"is:"
"by"
"E."
"space"
"\"MUST/MAY/SHOULD\""
"REQUIRED"
"ALPHA"
"HTTP/2.4"
"4DIGIT"
"increase"
"L."
"time."
"PATCH,"
"supports"
"2DIGIT"
"K.,"
"(A,"
"This"
"free"
"\"B\""
"RFC"
"base"
"proxy"
"IMPLIED,"
"POST"
"received."
"generate"
"text/plain"
"ISO-8859-7,"
"\"HTTP/1.1\""
"Partial"
"could"
"transition"
"DISCLAIMS"
"times"
"filter"
"HTML\","
"length"
"HEAD."
"HEAD,"
"S.,"
"first"
"origin"
"\"E\""
"already"
"UPALPHA"
"3DIGIT"
"*"
"Cache"
"Please"
"token."
"one"
"CHAR"
"ISI"
"another"
"FITNESS"
"message"
"CSS1,"
"open"
"size"
"doesn't"
"\""
"script"
"unknown"
"top"
"header)"
"system"
"construct"
"image/gif"
"2"
"ignored."
"listed"
"Date"
"LOALPHA"
"scheme"
"final"
"store"
"too"
"M."
"Success"
"that"
"completed"
"OPTIONAL;"
"task"
"tokens"
"R"
"pragma"
"(IANA"
"WAIS"
"F.,"
"than"
"(A"
"K."
"target"
"16"
"require"
"Only"
"WWW-Authenticate"
"HTTP/2.13,"
"headers"
"See"
"GMT."
"HTTP/2.0,"
"were"
"1)"
"IS\""
"stale"
"1*8ALPHA"
"are"
"and"
"IRC/6.9,"
"false"
"URL)."
"turned"
"ANSI"
"B"
"(IANA)"
"(LWS)."
"have"
"MIME,"
"need"
"HTTP/1.1.)"
"null"
"any"
"contents"
"conversion"
"data)"
"(LZ77)"
"(MIME"
"mechanism"
"internal"
"(C)"
"take"
"which"
"With"
"UCI"
"HTTP/0.9,"
"content-"
"200"
"begin"
"headers)"
"unless"
"TCP/IP"
"Content-Disposition"
"206"
"buffer"
"object"
"\"MUST\","
"regular"
"letter"
"entry"
"The"
"]"
"the"
"D."
"(STD"
"incompatible"
"L.,"
"(URL)"
"left"
"+"
"\"MIME"
"Note:"
"particularly"
"WA"
"text"
"labels"
"\"C\""
"Authentication"
"Unrecognized"
"CRLF."
"PARTICULAR"
"CRLF,"
"SP"
"find"
"MUST"
"true,"
"cache."
"upgrade"
"cache)"
"implementation"
"("
"[RFC"
"cache"
"3"
"should"
"failed"
"only"
"unable"
"LDAP)"
"USA"
"US-ASCII"
"(UA)"
"get"
"E.,"
"HEREIN"
"\"HTTP\"."
"cannot"
"new"
"THE"
"BNF"
"DIGIT,"
"closure"
"PUT"
"0)"
"resource"
"A.,"
"W."
"Content-Type:"
"ISO-8859."
"calling"
"J."
"INCLUDING"
"common"
"INTERNET"
"release"
"ISI/RR-98-463,"
"\"CONNECT\""
"where"
"set"
"IANA"
"For"
"\"F\""
"configured"
"C"
"this,"
"multipart"
"close"
"end."
"detect"
"GET"
"WWW\","
"1*DIGIT"
"BUT"
"MIT"
"outside"
"Proxy-Authorization"
"closed"
"between"
"probably"
"boundary"
"reading"
"\"SHALL"
"\"RECOMMENDED\","
"available"
"we"
"FOR"
"missing"
"importance"
"fetchpriority"
"screen"
"connection."
"ISO-8859-1"
"UNIX"
"STD"
"key"
"(MIME)"
"P."
"\"HTTP/1.1\"."
"HTTP/1.0),"
"AND"
"received"
"WWW"
"TRACE"
"\"MAY\","
"many"
"*TEXT"
"Unsupported"
"Rules"
"connection"
"Unicode"
"*OCTET"
"exceeds"
"(URN)"
"safely"
"finds"
"can't"
"WARRANTY"
"ISO-8859-8,"
"Content-Length"
"consume"
"stream"
"simple"
"header"
"DNS)"
"colon"
"adding"
"spans"
"1*HEX"
"table"
"allocated"
"BCP"
"application/pdf"
"LWS:"
"\"REQUIRED\","
"Wed,"
"C."
"C,"
"Proxy-Authenticate"
"encryption"
"create"
"(MHTML)\","
"been"
"."
"HTTP/12.3."
"\"OPTIONS\""
"\"PUT\""
"context."
"LWS,"
"basic"
"expected"
"prototype"
"GMT,"
"empty"
">"
"URL."
"PNG,\""
"\"D\""
"CA"
"HEX"
"N"
"0*3DIGIT"
"\"W/\""
"CR"
"\"DELETE\""
"unnecessarily"
"case"
"exception"
"save"
"(HTTP)"
"value"
"Assigned"
"while"
"\"GZIP"
"\"SHALL\","
"error"
"\"GMT\""
"\"TRACE\""
"resident"
"is"
"thus"
"it"
"encountered"
"Content"
"MIME"
"in"
"SIGCOMM"
"You"
"if"
"result"
"binary"
"containing"
"\"A"
")"
"CREATE"
"expired"
"1DIGIT"
"same"
"OPTIONS"
"read"
"BNF,"
"unrecognized"
"units"
"UST"
"status"
"\"%"
"extended"
"http"
"context"
"I"
"IP"
"(O)."
"allocation"
"running"
"*LWS"
"user"
"SMTP"
"stack"
"tracking"
"IETF"
"CR."
"failing"
"ANY"
"patterns"
"M.,"
"Names"
"In"
"position"
"model"
"audio"
"If"
"US-ASCII."
"MAY"
"THAT"
"being"
"(OK)."
"actions"
"invalid"
"HTTP/1.0)"
"CRC."
"previous"
"tables"
"TO"
"<US-ASCII"
"character"
"source"
"ISO-8859-2,"
"valid"
"location"
"HTTP/1.0"
"HTTP/1.1"
"size,"
"has"
"match"
"build"
"URI."
"tests"
"format"
"transfer-encoding"
"H.,"
"T"
"using"
"LIMITED"
"OK"
"success"
"text/html"
"ISO-8859-5,"
"B,"
"signal"
"MIME:"
"(HTCPCP/1.0)\","
"server"
"discarded"
"true"
"OF"
"output"
"page"
"S."
"right"
"old"
"sequence"
"uppercase"
"B.,"
"some"
"back"
"HT"
"Last-Modified"
"growth"
"equivalent"
"specified"
"multiple"
"H.F.,"
"HTTP/1.0."
"(BNF)"
"happens"
"ignore"
"PUT."
"INDEX."
"trace"
"for"
"avoid"
"CR,"
"does"
"Authorization"
"assuming"
"be"
"run"
"GET."
"deleted"
"302"
"X3.4-1986"
"<URL:"
"O"
"ISO-8859-1."
"last-modified"
"host"
"HTTP/1.0,"
"LWS>"
"INFORMATION"
"X3.4-1986,"
"properties"
"ALPHA,"
"Location"
"on"
"DIGIT"
"ENGINEERING"
"actual"
"extension"
"of"
"R.,"
"\"UTF-8,"
"*<TEXT,"
"OR"
"range"
"3ALPHA"
"URI,"
"positive"
"Message"
"DELETE"
"content-type"
"or"
"UC"
"No"
"ISO-"
"image"
"ACM"
"HEX\""
"URL,"
"because"
"ISO-8859-6,"
"T.,"
"operator"
"T/TCP"
"mark"
"file."
"area"
"GET\""
"transfer"
"support"
"there"
"long"
"class"
"start"
"HT."
"forward"
"was"
"function"
"HT,"
"N."
"HTTP/1.1\","
"memory"
"OCTET"
"but"
"failure"
"TE:"
"IMPLIED"
"CRLF"
"DNS"
"Error"
"\"ZLIB"
"line"
"trying"
"with"
"GMT"
"count"
"algorithm"
"default"
"B."
"ISO-8859-1,"
"up"
"ISO-8859-1)"
"SHOULD"
"PURPOSE."
"limit"
"used."
"WILL"
"DEL"
"define"
"called"
"delete"
"DELETE,"
"storing"
"USE"
"image/jpeg"
"defined"
"LWS"
"combining"
"unsafe"
"an"
"To"
"as"
"warning"
"exist"
"at"
"file"
"NOT\""
"NOT,"
"W3C/MIT"
"ISO-8859-1:1987."
"SHTTP/1.3,"
"no"
"when"
"A,"
"virtual"
"A."
"details."
"application"
"other"
"OPTIONAL"
"Proxy"
"LF,"
"test"
"MD5"
"you"
"TE"
"ISO-8859-3,"
"requested"
"elements"
"C)"
"symbol"
"T."
"code)"
"variable"
"SOCIETY"
"\"MUST"
"TCP"
"ISO-10646\","
"NOT\","
"R."
"lead"
"audio/basic"
"IANA."
"\"WAIS"
"persistent"
"Its"
"As"
"time"
"failures"
"\"ISO-8859-1\""
"once"

