This is a very basic TBSCertificate. It is valid from the perspective of
ParseTbsCertificate(), however its sub-fields are mainly bogus. This
TBSCertificate contains extensions.


$ openssl asn1parse -i < [TBS CERTIFICATE]
    0:d=0  hl=2 l=  67 cons: SEQUENCE          
    2:d=1  hl=2 l=   3 cons:  cont [ 0 ]        
    4:d=2  hl=2 l=   1 prim:   INTEGER           :02
    7:d=1  hl=2 l=   1 prim:  INTEGER           :01
   10:d=1  hl=2 l=   3 cons:  SEQUENCE          
   12:d=2  hl=2 l=   1 prim:   OCTET STRING      [HEX DUMP]:01
   15:d=1  hl=2 l=   3 cons:  SEQUENCE          
   17:d=2  hl=2 l=   1 prim:   OCTET STRING      [HEX DUMP]:05
   20:d=1  hl=2 l=  30 cons:  SEQUENCE          
   22:d=2  hl=2 l=  13 prim:   UTCTIME           :121018031200Z
   37:d=2  hl=2 l=  13 prim:   UTCTIME           :131018145959Z
   52:d=1  hl=2 l=   3 cons:  SEQUENCE          
   54:d=2  hl=2 l=   1 prim:   OCTET STRING      [HEX DUMP]:83
   57:d=1  hl=2 l=   3 cons:  SEQUENCE          
   59:d=2  hl=2 l=   1 prim:   OCTET STRING      [HEX DUMP]:F3
   62:d=1  hl=2 l=   5 cons:  cont [ 3 ]        
   64:d=2  hl=2 l=   3 cons:   SEQUENCE          
   66:d=3  hl=2 l=   1 prim:    OCTET STRING      [HEX DUMP]:DD
-----BEGIN TBS CERTIFICATE-----
MEOgAwIBAgIBATADBAEBMAMEAQUwHhcNMTIxMDE4MDMxMjAwWhcNMTMxMDE4MTQ1OTU5WjADBAG
DMAMEAfOjBTADBAHd
-----END TBS CERTIFICATE-----

-----BEGIN SERIAL NUMBER-----
AQ==
-----END SERIAL NUMBER-----

$ openssl asn1parse -i < [SIGNATURE ALGORITHM]
    0:d=0  hl=2 l=   3 cons: SEQUENCE          
    2:d=1  hl=2 l=   1 prim:  OCTET STRING      [HEX DUMP]:01
-----BEGIN SIGNATURE ALGORITHM-----
MAMEAQE=
-----END SIGNATURE ALGORITHM-----

$ openssl asn1parse -i < [ISSUER]
    0:d=0  hl=2 l=   3 cons: SEQUENCE          
    2:d=1  hl=2 l=   1 prim:  OCTET STRING      [HEX DUMP]:05
-----BEGIN ISSUER-----
MAMEAQU=
-----END ISSUER-----

VALIDITY NOTBEFORE: year=2012, month=10, day=18, hours=3, minutes=12, seconds=0
-----BEGIN VALIDITY NOTBEFORE-----
eWVhcj0yMDEyLCBtb250aD0xMCwgZGF5PTE4LCBob3Vycz0zLCBtaW51dGVzPTEyLCBzZWNvbmR
zPTA=
-----END VALIDITY NOTBEFORE-----

VALIDITY NOTAFTER: year=2013, month=10, day=18, hours=14, minutes=59, seconds=59
-----BEGIN VALIDITY NOTAFTER-----
eWVhcj0yMDEzLCBtb250aD0xMCwgZGF5PTE4LCBob3Vycz0xNCwgbWludXRlcz01OSwgc2Vjb25
kcz01OQ==
-----END VALIDITY NOTAFTER-----

$ openssl asn1parse -i < [SUBJECT]
    0:d=0  hl=2 l=   3 cons: SEQUENCE          
    2:d=1  hl=2 l=   1 prim:  OCTET STRING      [HEX DUMP]:83
-----BEGIN SUBJECT-----
MAMEAYM=
-----END SUBJECT-----

$ openssl asn1parse -i < [SPKI]
    0:d=0  hl=2 l=   3 cons: SEQUENCE          
    2:d=1  hl=2 l=   1 prim:  OCTET STRING      [HEX DUMP]:F3
-----BEGIN SPKI-----
MAMEAfM=
-----END SPKI-----

$ openssl asn1parse -i < [EXTENSIONS]
    0:d=0  hl=2 l=   3 cons: SEQUENCE          
    2:d=1  hl=2 l=   1 prim:  OCTET STRING      [HEX DUMP]:DD
-----BEGIN EXTENSIONS-----
MAMEAd0=
-----END EXTENSIONS-----
