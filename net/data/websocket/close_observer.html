<!doctype html>
<title>Observe the connection from a closed tab "Going Away"</title>
<script>
'use strict';

let protocol = location.protocol.replace('http', 'ws');
let url = protocol + '//' + location.host + '/close-observer?role=observer';

// Do connection test.
let ws = new WebSocket(url);

const id = setTimeout(() => {
  console.log('close_observer.html had timeout');
  document.title = 'FAIL';
}, 3000);

ws.onmessage = e => {
  clearTimeout(id);
  console.log('close_observer.html got message: ' + e.data);
  document.title = (e.data === 'OK' ? 'PASS' : 'FAIL');
  ws.onclose = null;
}

ws.onclose = () => {
  clearTimeout(id);
  console.log('close_observer.html saw close with no message');
  document.title = 'FAIL';
}

</script>
