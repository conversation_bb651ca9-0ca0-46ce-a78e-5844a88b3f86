// Copyright 2012 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

[
  {
    "namespace": "dependencyTester",
    "description": "An API for testing dependencies.",
    "dependencies": [ "browserAction", "fontSettings" ],
    "types": [],
    "functions": [
      {
        "name": "setTitle",
        "type": "function",
        "description": "hi",
        "parameters": [
          {
            "name": "details",
            "type": "object",
            "properties": {
              "color": {
                "$ref": "browserAction.ColorArray"
              },
              "scriptCode": {
                "$ref": "fontSettings.FakeStringType"
              }
            }
          }
        ]
      }
    ]
  }
]
