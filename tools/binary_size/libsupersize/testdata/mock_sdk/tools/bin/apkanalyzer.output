P d 8	14	689	<TOTAL>
P r 0	6	48	java
P r 0	6	48	java.lang
C r 0	3	24	java.lang.StringBuilder
M r 0	1	8	java.lang.StringBuilder <init>()
M r 0	1	8	java.lang.StringBuilder java.lang.StringBuilder append(java.lang.String)
M r 0	1	8	java.lang.StringBuilder java.lang.String toString()
C r 0	1	8	java.lang.Object
M r 0	1	8	java.lang.Object <init>()
C r 0	1	8	java.lang.IllegalArgumentException
M r 0	1	8	java.lang.IllegalArgumentException <init>(java.lang.String)
C r 0	1	8	java.lang.Math
M r 0	1	8	java.lang.Math double sqrt(double)
C d 5	5	397	TestClass
M d 1	1	38	TestClass <init>()
M d 1	1	62	TestClass long gcd(long,long)
M d 1	1	58	TestClass long lcm(long,long)
M d 1	1	120	TestClass double quadHi(double,double,double)
M d 1	1	50	TestClass java.lang.String repeat(java.lang.String)
F d 0	0	12	TestClass int REPEAT
C d 3	3	244	AuxClass
M d 1	1	42	AuxClass <init>(int)
M d 1	1	62	AuxClass int[] reverse(int[])
M d 1	1	90	AuxClass java.lang.String repeatString(java.lang.String)
F d 0	0	10	AuxClass int mRepeat
