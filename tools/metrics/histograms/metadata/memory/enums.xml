<!--
Copyright 2023 The Chromium Authors
Use of this source code is governed by a BSD-style license that can be
found in the LICENSE file.
-->

<!--

This file describes the enumerations referenced by entries in histograms.xml for
this directory. Some enums may instead be listed in the central enums.xml file
at src/tools/metrics/histograms/enums.xml when multiple files use them.

For best practices on writing enumerations descriptions, see
https://chromium.googlesource.com/chromium/src.git/+/HEAD/tools/metrics/histograms/README.md#Enum-Histograms

Please follow the instructions in the OWNERS file in this directory to find a
reviewer. If no OWNERS file exists, please consider signing up at
go/reviewing-metrics (Googlers only), as all subdirectories are expected to
have an OWNERS file. As a last resort you can send the CL to
<EMAIL>.
-->

<histogram-configuration>

<!-- Enum types -->

<enums>

<enum name="BooleanAreAllPagesFrozen">
  <int value="0" label="Not all pages are frozen"/>
  <int value="1" label="All pages are frozen"/>
</enum>

<enum name="BooleanGreaterOrEqualThan200MB">
  <int value="0" label="lt; 200MB"/>
  <int value="1" label="gt;= 200MB"/>
</enum>

<enum name="BooleanLargeAllocationFromFreelist">
  <int value="0" label="Allocated via IPC"/>
  <int value="1" label="Allocated via Freelist"/>
</enum>

<enum name="MappedAndResidentPagesDumpState">
  <int value="0" label="kAccessPagemapDenied"/>
  <int value="1" label="kFailure"/>
  <int value="2" label="kSuccess"/>
</enum>

<enum name="MemoryPressureLevel">
  <int value="0" label="No memory pressure"/>
  <int value="1" label="Moderate memory pressure"/>
  <int value="2" label="Critical memory pressure"/>
</enum>

<enum name="SelfCompactionCancellationReason">
  <int value="0" label="Frozen by App Freezer"/>
  <int value="1" label="Page resumed"/>
  <int value="2" label="Timeout"/>
</enum>

<enum name="VmmmsRequestPriority">
<!-- This must be kept in sync with enum UmaResizePriority in ChromeOS
src/platform2/vm_tools/concierge/mm/resize_priority.h -->

  <int value="0" label="Invalid"/>
  <int value="1" label="Balloon Stall"/>
  <int value="2" label="No Kill Candidates Host"/>
  <int value="3" label="No Kill Candidates Guest"/>
  <int value="4" label="Focused Tab"/>
  <int value="5" label="Focused App"/>
  <int value="6" label="Perceptible Tab"/>
  <int value="7" label="Perceptible App"/>
  <int value="8" label="Cached Tab"/>
  <int value="9" label="Aggressive Balloon"/>
  <int value="10" label="Cached App"/>
  <int value="11" label="MGLRU Reclaim"/>
  <int value="12" label="Stale Cached Tab"/>
  <int value="13" label="Stale Cached App"/>
</enum>

<enum name="VmmSwapDisableReason">
  <int value="0" label="VM shutdown while active"/>
  <int value="1" label="VM shutdown while inactive"/>
  <int value="2" label="Low disk space while active"/>
  <int value="3" label="Low disk space while inactive"/>
  <int value="4" label="Disable request while active"/>
  <int value="5" label="Disable request while inactive"/>
</enum>

<enum name="VmmSwapPolicyResult">
  <int value="0" label="Approve enable request"/>
  <int value="1" label="Cool down reject on enable request"/>
  <int value="2" label="Usage reject on enable request"/>
  <int value="3" label="TBW limit on enable request"/>
  <int value="4" label="Low disk on enable request"/>
  <int value="5" label="Approve maintenance request"/>
  <int value="6" label="Cool down reject on maintenance request"/>
  <int value="7" label="Usage reject on maintenance request"/>
  <int value="8" label="TBW limit on maintenance request"/>
  <int value="9" label="Low disk on maintenance request"/>
</enum>

<enum name="VmmSwapState">
  <int value="0" label="Enabled"/>
  <int value="1" label="Disabled"/>
</enum>

</enums>

</histogram-configuration>
