<!--
Copyright 2020 The Chromium Authors
Use of this source code is governed by a BSD-style license that can be
found in the LICENSE file.
-->

<!--
This file is used to generate a comprehensive list of Geolocation histograms
along with a detailed description for each histogram.

For best practices on writing histogram descriptions, see
https://chromium.googlesource.com/chromium/src.git/+/HEAD/tools/metrics/histograms/README.md

Please follow the instructions in the OWNERS file in this directory to find a
reviewer. If no OWNERS file exists, please consider signing up at
go/reviewing-metrics (Googlers only), as all subdirectories are expected to
have an OWNERS file. As a last resort you can send the CL to
<EMAIL>.
-->

<histogram-configuration>

<histograms>

<histogram name="Geolocation.Android.LocationPermissionState"
    enum="AndroidLocationPermissionState" expires_after="2025-10-12">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Android location permission state, recorded when the geolocation permission
    context is initialized.
  </summary>
</histogram>

<histogram name="Geolocation.CoreLocationProvider.Accuracy" units="meter"
    expires_after="2025-10-12">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the accuracy value (in meters) of a valid Geoposition. Values above
    10000 meters are considered very inaccurate and are categorized into the
    overflow bucket. This cap prioritizes accuracy resolution in the lower
    range.
  </summary>
</histogram>

<histogram name="Geolocation.CoreLocationProvider.ErrorCode"
    enum="CoreLocationErrorCode" expires_after="2025-12-31">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records an enumeration value corresponding to the specific error encountered
    by the CoreLocationProvider while obtaining the device's location.
  </summary>
</histogram>

<histogram name="Geolocation.CoreLocationProvider.SessionResult"
    enum="CoreLocationSessionResult" expires_after="2025-11-02">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the overall result of a geolocation session using the
    CoreLocationProvider. This metric captures the CoreLocationSessionResult
    representing the final outcome of the session, which could be a success
    result or any error encountered that prevented the provider from obtaining a
    valid position. If the session result is kCoreLocationError then the
    specific error reported by the platform is recorded in the
    Geolocation.CoreLocationProvider.ErrorCode metric.
  </summary>
</histogram>

<histogram name="Geolocation.CoreLocationProvider.TimeToFirstPosition"
    units="ms" expires_after="2025-11-02">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the time duration for getting the first valid Geoposition.
  </summary>
</histogram>

<histogram name="Geolocation.GeolocationImpl.ClientId"
    enum="GeolocationClientId" expires_after="2025-09-28">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records an enumeration value when a GeolocationImpl requests the next
    location estimate. The recorded value identifies the client that created the
    GeolocationImpl.
  </summary>
</histogram>

<histogram name="Geolocation.IOS.ChangedAuthorizationState"
    enum="GeolocationIOSAuthorizationStatus" expires_after="2025-07-27">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    iOS location permission state, recorded when geolocation permission state
    changes while the app is running.
  </summary>
</histogram>

<histogram name="Geolocation.IOS.InitialAuthorizationState"
    enum="GeolocationIOSAuthorizationStatus" expires_after="2025-07-27">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>iOS location permission state, recorded at startup.</summary>
</histogram>

<histogram name="Geolocation.LocationProviderManager.Mode"
    enum="LocationProviderManagerMode" expires_after="2026-09-10">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the mode of the LocationProviderManager that acquires a valid
    Geoposition.
  </summary>
</histogram>

<histogram name="Geolocation.LocationProviderManager.Source"
    enum="LocationProviderManagerSource" expires_after="2026-09-10">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the location provider source of the LocationProviderManager that
    acquires a valid Geoposition.
  </summary>
</histogram>

<histogram name="Geolocation.LocationProviderWinrt.Accuracy" units="meter"
    expires_after="2026-09-15">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>Records the accuracy value of a valid Geoposition.</summary>
</histogram>

<histogram name="Geolocation.LocationProviderWinrt.CreateGeoposition"
    enum="Hresult" expires_after="2026-08-05">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the HRESULT error code encountered when creating a mojom Geoposition
    object.
  </summary>
</histogram>

<histogram name="Geolocation.LocationProviderWinrt.ErrorStatus"
    enum="WindowsDevicesGeolocationPositionStatus" expires_after="2026-08-05">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records an enumeration value representing the system position status that is
    considered an error status.
  </summary>
</histogram>

<histogram name="Geolocation.LocationProviderWinrt.OnPositionChanged"
    enum="Hresult" expires_after="2026-08-05">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the HRESULT error code encountered when processing a position
    changed event.
  </summary>
</histogram>

<histogram name="Geolocation.LocationProviderWinrt.OnStatusChanged"
    enum="Hresult" expires_after="2026-08-05">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the HRESULT error code encountered when processing a status changed
    event.
  </summary>
</histogram>

<histogram name="Geolocation.LocationProviderWinrt.PositionSource"
    enum="WindowsDevicesGeolocationPositionSource" expires_after="2026-09-15">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records an enumeration value representing the position source for generating
    a valid Geoposition.
  </summary>
</histogram>

<histogram name="Geolocation.LocationProviderWinrt.RegisterCallbacks"
    enum="Hresult" expires_after="2026-08-05">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the HRESULT error code encountered when registering callbacks for
    position and stauts changed events.
  </summary>
</histogram>

<histogram name="Geolocation.LocationProviderWinrt.SessionResult"
    enum="Hresult" expires_after="2026-09-15">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the overall result of a geolocation session using the
    LocationProviderWinrt. This metric captures the HRESULT representing the
    final outcome of the session, which could be a success (S_OK) or any error
    encountered that prevented the provider from obtaining a valid position.
  </summary>
</histogram>

<histogram name="Geolocation.LocationProviderWinrt.StartProvider"
    enum="Hresult" expires_after="2026-08-05">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the HRESULT error code encountered when starting the Windows
    platform geolocation provider.
  </summary>
</histogram>

<histogram name="Geolocation.LocationProviderWinrt.TimeToFirstPosition"
    units="ms" expires_after="2026-08-05">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the time duration for getting the first valid Geoposition.
  </summary>
</histogram>

<histogram name="Geolocation.NetworkLocationProvider.SessionResult"
    enum="NetworkLocationRequestResult" expires_after="2025-10-26">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the overall result of a geolocation session using the
    NetworkLocationProvider. This metric captures the
    NetworkLocationRequestResult representing the final outcome of the session,
    which could be a success result or first error encountered that prevented
    the provider from obtaining a valid position. Noted that a short-lived
    session without a position or error updated is not recorded.
  </summary>
</histogram>

<histogram name="Geolocation.NetworkLocationProvider.TimeToFirstPosition"
    units="ms" expires_after="2025-10-26">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the time duration between starting the provider and getting the
    first valid Geoposition for NetworkLocationProvider.
  </summary>
</histogram>

<histogram name="Geolocation.NetworkLocationRequest.Accuracy" units="meter"
    expires_after="2025-10-15">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the accuracy value of a valid Geoposition from the
    NetworkLocationProvider.
  </summary>
</histogram>

<histogram name="Geolocation.NetworkLocationRequest.RequestInterval"
    units="mins" expires_after="2025-11-09">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    For recording the time gap between each HTTP request to geolocation service.
    The maximum bucket is 10 minutes because that is the maxium interval between
    wifi data polls.
  </summary>
</histogram>

<histogram name="Geolocation.NetworkLocationRequest.ResponseCode"
    enum="HttpResponseCode" expires_after="2025-11-16">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    HTTP response codes in NetworkLocationRequest.

    Warning: this histogram was expired after M85 and brought back 2021-05-21;
    data may be missing.
  </summary>
</histogram>

<histogram name="Geolocation.NetworkLocationRequest.Result"
    enum="NetworkLocationRequestResult" expires_after="2025-09-28">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the outcome of a network location request, indicating whether the
    request succeeded, was canceled, or failed due to various reasons such as
    network errors, invalid responses, or malformed data. This histogram helps
    track the distribution of different outcomes for network location requests.

    Note: Before M131, this histogram was originally named
    Geolocation.NetworkLocationRequest.Event.
  </summary>
</histogram>

<histogram name="Geolocation.NetworkLocationRequest.Source"
    enum="NetworkLocationRequestSource" expires_after="2025-11-02">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records an enumeration value when a network request has been sent to the
    network location service. The recorded value identifies the provider that
    initiated the request.
  </summary>
</histogram>

<histogram name="Geolocation.PublicIpAddressGeolocator.ClientId"
    enum="GeolocationClientId" expires_after="2025-09-28">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records an enumeration value when a PublicIpAddressGeolocator requests the
    next location estimate. The recorded value identifies the client that
    created the PublicIpAddressGeolocator.
  </summary>
</histogram>

<histogram name="Geolocation.SystemGeolocationSourceWin.CheckAccessError"
    enum="Hresult" expires_after="2025-09-28">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the HRESULT error code encountered while checking location access
    status on Windows.
  </summary>
</histogram>

<histogram
    name="Geolocation.SystemGeolocationSourceWin.CreateAppCapabilityError"
    enum="Hresult" expires_after="2025-09-28">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the HRESULT error code encountered while creating the location app
    capability object on Windows.
  </summary>
</histogram>

<histogram
    name="Geolocation.SystemGeolocationSourceWin.InitialPermissionStatus"
    enum="LocationSystemPermissionStatus" expires_after="2025-09-28">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records an enumeration value representing the current system location
    permission state when SystemGeolocationSourceWin is initialized at browser
    startup on Windows.
  </summary>
</histogram>

<histogram name="Geolocation.SystemGeolocationSourceWin.LaunchSettingsResult"
    enum="Hresult" expires_after="2025-09-28">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the HRESULT error code encountered while launching the Windows
    settings page, or S_OK if the settings page was launched without errors.
  </summary>
</histogram>

<histogram
    name="Geolocation.SystemGeolocationSourceWin.PermissionStatusChanged"
    enum="LocationSystemPermissionStatus" expires_after="2025-09-28">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records an enumeration value representing the current system location
    permission state when SystemGeolocationSourceWin detects that the system
    location permission has changed state. Not recorded if the permission
    changed after showing the system permission prompt.
  </summary>
</histogram>

<histogram
    name="Geolocation.SystemGeolocationSourceWin.PermissionStatusChangedAfterPrompt"
    enum="LocationSystemPermissionStatus" expires_after="2025-09-28">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records an enumeration value representing the system location permission
    state following the first location permission state change after the Windows
    system permission prompt was shown.
  </summary>
</histogram>

<histogram name="Geolocation.SystemGeolocationSourceWin.RequestAccessResult"
    enum="Hresult" expires_after="2025-09-28">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the HRESULT error code encountered while requesting location access,
    or S_OK if location access was requested without errors.
  </summary>
</histogram>

</histograms>

</histogram-configuration>
