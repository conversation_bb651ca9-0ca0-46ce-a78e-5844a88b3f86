<!--
Copyright 2020 The Chromium Authors
Use of this source code is governed by a BSD-style license that can be
found in the LICENSE file.
-->

<!--
This file is used to generate a comprehensive list of Accessibility histograms
along with a detailed description for each histogram.

For best practices on writing histogram descriptions, see
https://chromium.googlesource.com/chromium/src.git/+/HEAD/tools/metrics/histograms/README.md

Please follow the instructions in the OWNERS file in this directory to find a
reviewer. If no OWNERS file exists, please consider signing up at
go/reviewing-metrics (Googlers only), as all subdirectories are expected to
have an OWNERS file. As a last resort you can send the CL to
<EMAIL>.
-->

<histogram-configuration>

<histograms>

<variants name="BaseCrosAccessibilityFeatures">
  <variant name="CaretHighlight"/>
  <variant name="ColorCorrection"/>
  <variant name="CursorHighlight"/>
  <variant name="Dictation"/>
  <variant name="DockedMagnifier"/>
  <variant name="FaceGaze"/>
  <variant name="FocusHighlight"/>
  <variant name="HighContrast"/>
  <variant name="LargeCursor"/>
  <variant name="ReducedAnimations"/>
  <variant name="ScreenMagnifier"/>
  <variant name="SelectToSpeak"/>
  <variant name="StickyKeys"/>
  <variant name="SwitchAccess"/>
  <variant name="VirtualKeyboard"/>
</variants>

<variants name="ScreenAIServices">
  <variant name="MainContentExtraction" summary="Main Content Extraction"/>
  <variant name="OCR" summary="OCR"/>
</variants>

<variants name="SodaLanguageCode">
  <variant name="de-DE" summary="German language code"/>
  <variant name="en-US" summary="English language code"/>
  <variant name="es-ES" summary="Spanish language code"/>
  <variant name="fr-FR" summary="French language code"/>
  <variant name="hi-IN" summary="Hindi language code"/>
  <variant name="id-ID" summary="Indonesian language code"/>
  <variant name="it-IT" summary="Italian language code"/>
  <variant name="ja-JP" summary="Japanese language code"/>
  <variant name="ko-KR" summary="Korean language code"/>
  <variant name="pl-PL" summary="Polish language code"/>
  <variant name="pt-BR" summary="Portuguese (Brazil) language code"/>
  <variant name="th-TH" summary="Thai language code"/>
  <variant name="tr-TR" summary="Turkish language code"/>
  <variant name="zh-CN" summary="Chinese (Simplified) language code"/>
  <variant name="zh-TW" summary="Chinese (Traditional) code"/>
</variants>

<variants name="TerminationStatus">
  <variant name="Crash" summary="crashed"/>
  <variant name="Shutdown" summary="shut down"/>
</variants>

<histogram name="Accessibility.ActiveTime" units="ms"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    The amount of time accessibility was enabled and actively used. Reported
    when we switch states from active to inactive.
  </summary>
</histogram>

<histogram
    name="Accessibility.Android.AutoDisableV2.{CurrentState}Time.{CallType}"
    units="ms" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks the amount of time (in ms) that the accessibility engine was
    {CurrentState} before {CallType}. This is recorded either at the start of
    the onDisabled callback, or immediately before the call to
    reEnableRendererAccessibility.
  </summary>
  <token key="CurrentState">
    <variant name="Disabled" summary="disabled"/>
    <variant name="Enabled" summary="enabled"/>
  </token>
  <token key="CallType">
    <variant name="Initial"
        summary="the initial call to the onDisable or
                 reEnableRendererAccessibility methods. Can only be recorded
                 once per instance."/>
    <variant name="Successive"
        summary="a subsequent call to the onDisable or
                 reEnableRendererAccessibility methods. Can be recorded up to
                 3 times per instance."/>
  </token>
</histogram>

<histogram
    name="Accessibility.Android.AutoDisableV2.{MethodCall}Called.{CallType}"
    units="count" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks when the Auto-disable Accessibility feature changes the state of the
    accessibility engine by calling {MethodCall}. This is {CallType}.
  </summary>
  <token key="MethodCall">
    <variant name="Disable" summary="the onDisabled callback"/>
    <variant name="ReEnable" summary="reEnableRendererAccessibility"/>
  </token>
  <token key="CallType">
    <variant name="Initial"
        summary="the initial call to the onDisable or
                 reEnableRendererAccessibility methods. Can only be recorded
                 once per instance."/>
    <variant name="Successive"
        summary="a subsequent call to the onDisable or
                 reEnableRendererAccessibility methods. Can be recorded up to
                 3 times per instance."/>
  </token>
</histogram>

<histogram name="Accessibility.Android.Cache.MaxNodesInCache" units="count"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks the maximum number of AccessibilityNodeInfo objects that were stored
    in the Java-side cache during a single session.
  </summary>
</histogram>

<histogram name="Accessibility.Android.Cache.PercentageRetrievedFromCache"
    units="%" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks the percentage of calls from the Android framework to create a new
    AccessibilityNodeInfo object that were serviced from the cache rather than
    constructing the object from scratch during a single session.
  </summary>
</histogram>

<histogram name="Accessibility.Android.FindElementType.Usage2.{RunningATs}"
    enum="AccessibilityPredicateType" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks the type of element that a user navigates by in the web contents when
    the Android Framework calls performAction with the ACTION_NEXT_HTML_ELEMENT
    action. This is tracked during the tree search for the next/previous
    element. This occured with: {RunningATs}
  </summary>
  <token key="RunningATs">
    <variant name="NoTalkBack.Basic"
        summary="Some AT(s) running without TalkBack running, in AXMode:
                 kAXModeBasic."/>
    <variant name="NoTalkBack.FormControls"
        summary="Some AT(s) running without TalkBack running, in AXMode:
                 kAXModeFormControls."/>
    <variant name="NoTalkBack.Unnamed"
        summary="Some AT(s) running without TalkBack running in some unnamed
                 AXMode combination."/>
    <variant name="NoTalkBack.WebContentsOnly"
        summary="Some AT(s) running without TalkBack running, in AXMode:
                 kAXModeWebContentsOnly."/>
    <variant name="TalkBack" summary="TalkBack as the only running AT."/>
    <variant name="TalkBackWithOtherAT"
        summary="TalkBack running along with another AT."/>
  </token>
</histogram>

<histogram name="Accessibility.Android.InlineTextBoxes.Bundle.{CallLocation}"
    enum="AccessibilityModeBundleEnum" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks the combinations of mode flags that are set when there is a request
    to load inline text boxes on Android. This occurs when {CallLocation}.
  </summary>
  <token key="CallLocation">
    <variant name="ExtraData"
        summary="an AT explicitly requests in-line text boxes via
                 addExtraDataToAccessibilityNodeInfo"/>
    <variant name="FromFocus"
        summary="a node first receives accessibility focus via
                 MoveAccessibilityFocus"/>
  </token>
</histogram>

<histogram name="Accessibility.Android.InlineTextBoxes.DuplicateRequest"
    enum="Boolean" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks if the inline text boxes for a node have already been requested and
    generated when an AT calls the addExtraDataToAccessibilityNodeInfo method to
    explicitly request that we add the boxes for that node.
  </summary>
</histogram>

<!-- LINT.IfChange(KeyboardShortcutsSemanticMeaning) -->

<histogram name="Accessibility.Android.KeyboardShortcut.{ScreenReaderState}3"
    enum="KeyboardShortcutsSemanticMeaning" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    When a keyboard shortcut is used on Android, tracks the action of the
    shortcut, and splits based on whether or not a screen reader is running.
  </summary>
  <token key="ScreenReaderState">
    <variant name="NoScreenReader" summary="User is not using a screen reader"/>
    <variant name="ScreenReaderRunning"
        summary="User is using a screen reader"/>
  </token>
</histogram>

<!-- LINT.ThenChange(//chrome/android/java/src/org/chromium/chrome/browser/KeyboardShortcuts.java:KeyboardShortcutsSemanticMeaning, //tools/metrics/histograms/metadata/accessibility/enums.xml:KeyboardShortcutsSemanticMeaning) -->

<histogram name="Accessibility.Android.OnDemand.EventsDropped" units="count"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks the number of AccessibilityEvents dropped when the OnDemand
    accessibility services feature is enabled. Recorded at the end of a session.
  </summary>
</histogram>

<histogram name="Accessibility.Android.OnDemand.OneHundredPercentEventsDropped"
    units="count" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks the number of AccessibilityEvents dropped when the OnDemand
    accessibility services feature is enabled, and when the total percentage of
    dropped events is 100%. Recorded at the end of a session.
  </summary>
</histogram>

<histogram
    name="Accessibility.Android.OnDemand.OneHundredPercentEventsDropped.{AXMode}"
    units="count" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks the number of AccessibilityEvents dropped when the OnDemand
    accessibility services feature is enabled, and the
    AccessibilityPerformanceFiltering feature is enabled, and the number of
    events dropped is 100%. Tracks for when the AXMode is set to {AXMode}.
    Recorded at the end of a session. Updated in M114 to add FormControls
    variant.
  </summary>
  <token key="AXMode">
    <variant name="Basic"
        summary="kAXModeBasic - Includes only NativeAPIs and WebContents"/>
    <variant name="Complete"
        summary="kAXModeComplete - Includes NativeAPIs, WebContents,
                 InlineTextBoxes, ScreenReader, and HTML"/>
    <variant name="FormControls"
        summary="kAXModeFormControls - Includes only NativeAPIs, WebContents,
                 HTML, and ExperimentalFormControls"/>
  </token>
</histogram>

<histogram name="Accessibility.Android.OnDemand.PercentageDropped" units="%"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks the percentage of AccessibilityEvents dropped when the OnDemand
    accessibility services feature is enabled. Recorded at the end of a session.
  </summary>
</histogram>

<histogram name="Accessibility.Android.OnDemand.PercentageDropped.{AXMode}"
    units="%" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks the percentage of AccessibilityEvents dropped when the OnDemand
    accessibility services feature is enabled, and the
    AccessibilityPerformanceFiltering feature is enabled. Tracks for when the
    AXMode is set to {AXMode}. Recorded at the end of a session. Updated in M114
    to add FormControls variant.
  </summary>
  <token key="AXMode">
    <variant name="Basic"
        summary="kAXModeBasic - Includes only NativeAPIs and WebContents"/>
    <variant name="Complete"
        summary="kAXModeComplete - Includes NativeAPIs, WebContents,
                 InlineTextBoxes, ScreenReader, and HTML"/>
    <variant name="FormControls"
        summary="kAXModeFormControls - Includes only NativeAPIs, WebContents,
                 HTML, and ExperimentalFormControls"/>
  </token>
</histogram>

<histogram name="Accessibility.Android.PageZoom.AppMenuEnabledState"
    enum="AccessibilityPageZoomAppMenuEnabledState" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks the enabled state for the Page Zoom option on the main app menu. The
    state will be one of the following enumerated states: 0) not enabled, 1)
    enabled explicitly by the user on the Accessibility Page Zoom setting, 2)
    enabled automatically if the user has an Android OS-wide default font size,
    3) disabled explicitly by the user on the Accessibility Page Zoom setting.
    Recorded each time the app menu is opened.
  </summary>
</histogram>

<histogram name="Accessibility.Android.PageZoom.AppMenuSliderOpened"
    enum="Boolean" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks whether the user opened the Page Zoom slider from the app menu
    option. Recorded each time the slider is opened from the app menu.
  </summary>
</histogram>

<histogram name="Accessibility.Android.PageZoom.AppMenuSliderZoomLevelChanged"
    enum="Boolean" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks whether the user changed the individual page zoom level on at least
    one webpage from the app menu slider view. Recorded each time the slider is
    dismissed if the user changed the zoom level before dismissal.
  </summary>
</histogram>

<histogram name="Accessibility.Android.PageZoom.AppMenuSliderZoomLevelValue"
    units="%" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    For users who changed the individual page zoom level on at least one webpage
    from the app menu slider view, tracks what zoom level has been set on the
    page. Recorded each time the slider is dismissed if the user changed the
    zoom level before dismissal.
  </summary>
</histogram>

<histogram name="Accessibility.Android.PageZoom.MainFrameZoomFactor"
    units="zoom factor %" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks the zoom factor when visual properties of the main frame are updated.
    This will be recorded each time the properties change. The value can be
    recorded multiple times for a single frame, for example as a user is
    changing zoom value with the slider on Android.
  </summary>
</histogram>

<histogram
    name="Accessibility.Android.PageZoom.SettingsDefaultZoomLevelChanged"
    enum="Boolean" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks whether the user changed the default page zoom level from the
    Accessibility Page Zoom settings page. Recorded each time the user closes
    the Accessibility Page Zoom settings page if the user changed the zoom level
    while the settings page was showing.
  </summary>
</histogram>

<histogram name="Accessibility.Android.PageZoom.SettingsDefaultZoomLevelValue"
    units="%" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    For users who changed the default page zoom level from the Accessibility
    Page Zoom settings page, tracks what default zoom level has been set in the
    settings. Recorded each time the user closes the Accessibility Page Zoom
    settings page if the user changed the zoom level while the settings page was
    showing.
  </summary>
</histogram>

<histogram name="Accessibility.Android.PageZoom.Usage"
    enum="AccessibilityPageZoomUsageType" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks what type of user the current user is considered for the
    Accessibility Page Zoom feature. The user can be in one of four buckets: a
    user that has saved a custom zoom level on at least one site; a user that
    has a default zoom level choice for all sites; a user that has both; a user
    that has none. Any of the former three is considered an active user of the
    feature. This is tracked once on browser start up.

    Note: Users can move between these groups, although they can only be in one
    group at a time.
  </summary>
</histogram>

<histogram
    name="Accessibility.Android.Performance.CreateAccessibilityNodeInfo.TotalTime"
    units="ms" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks the total amount of time that an instance of
    WebContentsAccessibilityImpl spends in the createAccessibilityNodeInfo
    method. This timing only begins after the native code is initialized, which
    happens when an AT interacts with the Android framework while Chrome is
    open, and tracks the net time until the WebContentsAccessibilityImpl
    instance for this tab is destroyed. The metric is recorded when the
    WebContentsAccessibilityImpl object loses foreground focus (e.g. switching
    tabs, closing tabs, sending Chrome to the background, etc).
  </summary>
</histogram>

<histogram name="Accessibility.Android.Performance.OneShotTreeSearch"
    units="microseconds" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks the time it takes for a single one-shot tree search to find an
    element. This is done during the
    WebContentsAccessibilityAndroid#FindElementType method, which searches for
    the next element in a tree for a move by granularity action. We only track
    this for a completed tree search (i.e. one that finds a node successfully),
    ignoring early-exits, to get an upper bound on the method cost. This metric
    has a range of 1 to 1000 microseconds, with the expectation that we may
    change this after initial data.

    Note: This metric is only recorded on devices with high-resolution clocks.
  </summary>
</histogram>

<histogram name="Accessibility.Android.Performance.SpannableCreationTime2"
    units="microseconds" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks the time it takes to computeText, i.e. create Spannables, for a
    single AccessibilityNodeInfo on Android. This is used to track potential
    performance impact of the AccessibilityTextFormatting feature and is meant
    to be temporary. The histogram is recorded immediately before the
    constructed Spannable is set on the node.

    Note: This metric is only recorded on devices with high-resolution clocks.
  </summary>
</histogram>

<histogram
    name="Accessibility.Android.Performance.TimeUntilFirstAccessibilityFocus"
    units="ms" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks the time it takes for the WebContentsAccessibilityImpl to start
    native initialization, until a node on the page is given accessibility focus
    for the first time. We restrict this to TalkBack, which we know always
    focuses the first item on a page. The metric is recorded when the
    AccessibilityEvent is dispatched to the view.
  </summary>
</histogram>

<histogram name="Accessibility.Android.UpdateAccessibilityServices.DidPoll"
    enum="BooleanHit" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Recorded every time the AccessibilityState.updateAccessibilityServices
    method hit the criteria to poll. Lower number of hits is better.
  </summary>
</histogram>

<histogram name="Accessibility.Android.UpdateAccessibilityServices.PollCount"
    units="count" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks the number of times this sequence of calls to
    AccessibilityState.updateAccessibilityServices required polling to get
    consistent enabled and running services. Lower count is better.
  </summary>
</histogram>

<histogram name="Accessibility.Android.UpdateAccessibilityServices.PollTimeout"
    enum="BooleanHit" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks the number of times the
    AccessibilityState.updateAccessibilityServices did not complete polling
    before the max timeout. This should be rare. Any instances of this are bad.
    Lower number of hits is better, should (almost always) be zero.
  </summary>
</histogram>

<histogram name="Accessibility.Android.UpdateAccessibilityServices.Runtime"
    units="microseconds" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks the amount of time the AccessibilityState.updateAccessibilityServices
    method took from start to finish (not counting async wait time). Lower time
    is better.
  </summary>
</histogram>

<histogram name="Accessibility.Android.Usage.A11yAlwaysOn" units="ms"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks the total amount of time that this WebContents spent in the
    foreground for usages with the native accessibility engine always
    initialized. That is, this is a measure of time spent on the web for users
    that always have an accessibility service running. Recorded when an instance
    is hidden per WebContentsObserver#wasHidden (e.g. backgrounded Chrome,
    opened a new Tab, went to Settings etc), and is equal to the time since the
    last call to WebContentsObserver#wasShown. This can be recorded multiple
    times for a single WebContents.
  </summary>
</histogram>

<histogram name="Accessibility.Android.Usage.{UsageType}" units="ms"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks the total amount of time that this WebContents spent {UsageType}.
    Recorded when an instance is hidden per WebContentsObserver#wasHidden (e.g.
    backgrounded Chrome, opened a new Tab, went to Settings etc), and is equal
    to the time since the last call to WebContentsObserver#wasShown. This can be
    recorded multiple times for a single WebContents.
  </summary>
  <token key="UsageType">
    <variant name="Foreground" summary="in the foreground of the app"/>
    <variant name="NativeInit"
        summary="with the native accessibility code initialized"/>
  </token>
</histogram>

<histogram name="Accessibility.AndroidServiceInfo.{RunningApps}"
    enum="AccessibilityAndroidServiceInfoEnum" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks events, flags, feedback and capabilities of enabled accessibility
    services separated into variants based on the types of running accessibility
    services. This information would be a subset of the (now deprecated)
    Accessibility.AndroidServiceInfo histogram. It will allow us to look at the
    same histograms but only for a subset of users (e.g. what are the requested
    flags etc of enabled accessibility services for clients running only a
    password manager).
  </summary>
  <token key="RunningApps">
    <variant name="ALL_VARIANTS"/>
    <variant name="ASSISTIVE_TECH"/>
    <variant name="ASSISTIVE_TECH_WITH_PASSWORD_MANAGER"/>
    <variant name="ASSISTIVE_TECH_WITH_UNKNOWN"/>
    <variant name="PASSWORD_MANAGER"/>
    <variant name="PASSWORD_MANAGER_WITH_UNKNOWN"/>
    <variant name="UNKNOWN"/>
  </token>
</histogram>

<histogram name="Accessibility.ATK-APIs" enum="AccessibilityATKAPIEnum"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks usage of ATK APIs on Linux Desktop. Recorded when ATK APIs that are
    supposed to trigger AX mode enabled is called.
  </summary>
</histogram>

<histogram name="Accessibility.AutoDisabled.BlockedAfter.{EventType}"
    enum="AssistiveTech" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    After an auto-disable heuristic was triggered without matching A11y API
    calls, this is the reason it was blocked (e.g., the presence of a known
    assistive technology).
  </summary>
  <token key="EventType">
    <variant name="Focus"/>
    <variant name="LoadComplete"/>
    <variant name="UserInput"/>
  </token>
</histogram>

<histogram name="Accessibility.AutoDisabled.DisabledTime" units="ms"
    expires_after="2025-05-04">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    The total amount of time accessibility was disabled due to being
    auto-disabled, before being enabled again.
  </summary>
</histogram>

<histogram name="Accessibility.AutoDisabled.EnabledTime" units="ms"
    expires_after="2025-05-04">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    The total amount of time accessibility was enabled before being
    auto-disabled.
  </summary>
</histogram>

<histogram name="Accessibility.AutoDisabled.EventCount" units="count"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    The number of user input events that were received within one time window,
    without any accompanying accessibility API usage, that triggered
    accessibility to be auto-disabled.
  </summary>
</histogram>

<histogram
    name="Accessibility.AXTreeFixing.ScreenAI.Disconnect.{DisconnectCount}"
    enum="BooleanOccurred" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    This histogram measures the amount of times that the ScreenAI service
    disconnects {DisconnectCount} from the AXTreeFixing service after it had
    previously been initialized and connected.
  </summary>
  <token key="DisconnectCount">
    <variant name="First" summary="the first time"/>
    <variant name="Multiple" summary="more than once in a single session"/>
  </token>
</histogram>

<histogram name="Accessibility.AXTreeFixing.ScreenAI.FoundMainNode"
    enum="BooleanFound" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    This histogram measures whether or not a main node was identified after the
    AXTreeFixing service made a request to the ScreenAI service and successfully
    received a response from the service.
  </summary>
</histogram>

<histogram name="Accessibility.AXTreeFixing.ScreenAI.InitializationAttempt"
    enum="BooleanAttempted" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    This histogram measures the amount of times that the AXTreeFixing service
    attempts to initialize the ScreenAI service.
  </summary>
</histogram>

<histogram name="Accessibility.AXTreeFixing.ScreenAI.InitializedFailed"
    enum="BooleanOccurred" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    This histogram measures the amount of times that the AXTreeFixing service
    failed to initialize the ScreenAI service after 3 failed attempts.
  </summary>
</histogram>

<histogram name="Accessibility.AXTreeFixing.ScreenAI.InitializedOnAttempt"
    units="count" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    This histogram measures the amount of times that it took for the ScreenAI
    service to successfully initialize for the AXTreeFixing service. A value of
    1 means that it initialized on the first try (happy-path). We attempt to
    initialize 3 times before stopping.
  </summary>
</histogram>

<histogram
    name="Accessibility.AXTreeFixing.ScreenAI.MainNodeIdentification.ClientRequestType"
    enum="AXTreeFixingClientScreenAIRequestType" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    This histogram records the type of request that clients made to the
    AXTreeFixing service for ScreenAI specifically for main node identification.
    Requests can come before initialization, or with an invalid tree that
    already contains a main node, etc.
  </summary>
</histogram>

<histogram
    name="Accessibility.AXTreeFixing.ScreenAI.MainNodeIdentification.RoundTripTime"
    units="ms" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    This histogram measures the total time (in ms) that a request for main node
    identification from the AXTreeFixing service to the ScreenAI service took to
    complete.
  </summary>
</histogram>

<histogram
    name="Accessibility.AXTreeFixing.ScreenAI.MainNodeIdentification.{RequestResponseType}"
    enum="BooleanOccurred" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    This histogram records the number of times that the AXTreeFixing service
    {RequestResponseType} to identify the main node of a tree.
  </summary>
  <token key="RequestResponseType">
    <variant name="Request" summary="made a request to the ScreenAI service"/>
    <variant name="Response"
        summary="gets a response from the ScreenAI service after making a
                 request"/>
  </token>
</histogram>

<histogram name="Accessibility.AXTreeFixing.ScreenAI.MainNodeInitialRole"
    enum="AccessibilityRole" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    This histogram measures the initial Role of an identified main node after
    the AXTreeFixing service made a request to the ScreenAI service for main
    node identification.
  </summary>
</histogram>

<histogram name="Accessibility.AXTreeSnapshotter.Snapshot.Error"
    enum="AXTreeSnapshotErrorReason" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    The number of times that an AXTreeSnapshot request was not properly
    fulfilled, split by error reason. Each individual snapshot request can only
    report one failure reason. A page with multiple render frames can have some
    snapshots succeed, and others fail. On repeat calls for snapshots, a
    previously succeeding call may fail, or vice versa.
  </summary>
</histogram>

<histogram name="Accessibility.AXTreeSnapshotter.Snapshot.Request"
    enum="BooleanSent" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    The number of times that an AXTreeSnapshot has been requested by any client.
    Each request is per render frame, so this can be counted multiple times on a
    page, and a snapshot can be requested multiple times per page.
  </summary>
</histogram>

<histogram name="Accessibility.Bundle" enum="AccessibilityModeBundleEnum"
    expires_after="2025-05-04">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks when combinations of mode flags are set. Records when the mode flag
    changes and aligns with one of the common (named) flag bundles.
  </summary>
</histogram>

<histogram name="Accessibility.CaptionSettingsLoadedFromPrefs"
    enum="BooleanEnabled" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Whether custom caption settings were loaded from the user preferences.
    Recorded when WebKit preferences are overridden.
  </summary>
</histogram>

<histogram name="Accessibility.CaptionSettingsLoadedFromSystemSettings"
    enum="BooleanEnabled" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Whether custom caption settings were loaded from the system preferences.
    Recorded when WebKit preferences are overridden.
  </summary>
</histogram>

<histogram name="Accessibility.ChromeVox.PerformCommand"
    enum="ChromeVoxCommand" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>Records when a user performs a ChromeVox command.</summary>
</histogram>

<histogram name="Accessibility.ChromeVox.PerformGestureType"
    enum="ChromeVoxGestureType" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    A user can control ChromeVox via a variety of gestures on the touch screen.
    For example, swiping right with one finger causes ChromeVox to navigate to
    the next object. Track all possible gestures here. Warning: this histogram
    was expired 2023-11-30 to 2024-02-29, data might be missing.
  </summary>
</histogram>

<histogram name="Accessibility.ChromeVox.StartUpSpeechDelay" units="ms"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    The duration of time from when ChromeVox was first loaded to when the
    ChromeVox welcome message was spoken.
  </summary>
</histogram>

<histogram name="Accessibility.CrosAutoclick.TrayMenu.ChangeAction"
    enum="AutoclickActionType" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Recorded when a user has picked a new autoclick action type from the bubble
    menu.
  </summary>
</histogram>

<histogram name="Accessibility.CrosBounceKeysDelay" units="ms"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    The bounce keys delay duration in milliseconds set by the user if Bounce
    Keys is turned on. Logged once after startup.
  </summary>
</histogram>

<histogram name="Accessibility.CrosCaretBlinkInterval" units="ms"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    The caret blink interval rate, in milliseconds, chosen by the user, from 12
    possible values in chrome os settings. 0ms means &quot;do not blink&quot;,
    and the default value is 500ms. Logged once after startup. For how users
    adjust the setting from settings, see
    ChromeOS.Settings.Accessibility.CaretBlinkInterval, which logs each time the
    setting changes.
  </summary>
</histogram>

<histogram name="Accessibility.CrosColorCorrection.FilterAmount" units="%"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    The color correction filter intensity chosen by the user. Logged once after
    startup. For how users adjust the setting from settings, see
    ChromeOS.Settings.Accessibility.ColorCorrection.FilterAmount, which logs
    each time the setting changes.
  </summary>
</histogram>

<histogram name="Accessibility.CrosColorCorrection.FilterType"
    enum="ColorCorrectionFilterTypes" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    The color correction filter type chosen by the user. Logged once seconds
    after startup. For how users adjust the setting from settings, see
    ChromeOS.Settings.Accessibility.ColorCorrection.FilterType, which logs each
    time the setting changes.
  </summary>
</histogram>

<histogram name="Accessibility.CrosDictation.Language" enum="LocaleCodeBCP47"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    The language used for speech recognition in dictation on ChromeOS. This is
    logged each time Dictation is toggled on. From M99 to M104 this was logged
    using base::PersistantHash instead of base::HashMetricName; data from that
    period can be decoded using a map between the two hashing functions. See
    crbug.com/1342966.
  </summary>
</histogram>

<histogram
    name="Accessibility.CrosDictation.ListeningDuration.NetworkRecognition"
    units="ms" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Duration that the network speech recognition service was listening for
    dictation. Recorded each time a user toggles dictation on until dictation is
    stopped, either by the user action, error, or timeout.
  </summary>
</histogram>

<histogram
    name="Accessibility.CrosDictation.ListeningDuration.OnDeviceRecognition"
    units="ms" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Duration that the on-device speech recognition service was listening for
    dictation. Recorded each time a user toggles dictation on until dictation is
    stopped, either by the user action, error, or timeout.
  </summary>
</histogram>

<histogram name="Accessibility.CrosDictation.MacroFailed"
    enum="CrosDictationMacroName" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Recorded whenever Dictation fails to perform a macro. Warning: this
    histogram was expired 2023-11-30 to 2024-02-29, data might be missing.
  </summary>
</histogram>

<histogram name="Accessibility.CrosDictation.MacroRecognized"
    enum="CrosDictationMacroName" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>Recorded whenever Dictation recognizes a macro.</summary>
</histogram>

<histogram name="Accessibility.CrosDictation.MacroSucceeded"
    enum="CrosDictationMacroName" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Recorded whenever Dictation successfully performs a macro. Warning: this
    histogram was expired 2023-11-30 to 2024-02-29, data might be missing.
  </summary>
</histogram>

<histogram name="Accessibility.CrosDictation.PumpkinSucceeded"
    enum="BooleanUsage" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records whether Pumpkin successfully parsed a command. This histogram is
    recorded each time Pumpkin is used to parse a command.
  </summary>
</histogram>

<histogram name="Accessibility.CrosDictation.ToggleDictationMethod"
    enum="CrosDictationToggleDictationMethod" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>Records how users invoke Dictation.</summary>
</histogram>

<histogram name="Accessibility.CrosDictation.UsedOnDeviceSpeech"
    enum="BooleanUsage" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    When Dictation was toggled on, records whether on-device speech recognition
    was used. If on-device recognition was not used, network speech was used.
  </summary>
</histogram>

<histogram name="Accessibility.CrosDictation.UsedPumpkin" enum="BooleanUsage"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records whether Pumpkin was used to parse a command. If Pumpkin was not
    used, then regex parsing was used. This histogram is recorded each time
    Dictation parses a command.
  </summary>
</histogram>

<histogram name="Accessibility.CrosDisableTouchpad" enum="BooleanEnabled"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Whether the built-in touchpad is enabled or disabled. It logs when the
    touchpad is disabled, i.e. setting the internal touchpad mode to
    &quot;Always disabled&quot; or &quot;Disabled on mouse connected&quot;, and
    when the touchpad is enabled, which occurs when the internal touchpad mode
    is set to &quot;Never disabled&quot;.
  </summary>
</histogram>

<histogram name="Accessibility.CrosLargeCursorSize2" units="dip"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Cursor size of the Chrome OS Large Cursor (logged once after startup). This
    replaced Accessibility.CrosLargeCursorSize in M124.
  </summary>
</histogram>

<histogram name="Accessibility.CrosSelectToSpeak.BackgroundShading"
    enum="BooleanEnabled" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Whether Select-to-Speak had background shading enabled when activated.
  </summary>
</histogram>

<histogram name="Accessibility.CrosSelectToSpeak.BubbleButtonPress"
    enum="CrosSelectToSpeakAction" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records each action the user selects in the Select-to-Speak bubble.
  </summary>
</histogram>

<histogram name="Accessibility.CrosSelectToSpeak.BubbleDismissMethod"
    enum="CrosSelectToSpeakActivationMethod" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    When Select-to-speak is active, the user can dismiss the bubble menu in
    multiple ways: by clicking the cancel button on the Select-to-speak menu or
    using keyboard shortcuts. Track the methods here. Warning: this histogram
    was expired 2023-11-30 to 2024-02-29, data might be missing.
  </summary>
</histogram>

<histogram name="Accessibility.CrosSelectToSpeak.EnhancedNetworkVoices"
    enum="BooleanEnabled" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Whether enhanced network TTS voices were enabled when Select-to-Speak was
    activated.
  </summary>
</histogram>

<histogram name="Accessibility.CrosSelectToSpeak.NavigationControls"
    enum="BooleanEnabled" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Whether Select-to-Speak's navigation controls were on when activated.
  </summary>
</histogram>

<histogram name="Accessibility.CrosSelectToSpeak.OverrideSpeechRateMultiplier"
    enum="CrosSelectToSpeakOverrideSpeechRateMultiplier"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    The multiplier with which the user overrode the speech rate setting in
    Select-to-Speak. By default, the global Text-to-Speech setting is used,
    which the user can temporarily override. The override multiplier is stored
    as a sparse histogram with values (100 * multiple). For example, a speech
    rate multiplier of 1.0 (default) will be seen as 100. Emitted once every
    time playback starts, including on resume after pause or change of
    selection. Warning: this histogram was expired 2023-11-30 to 2024-02-29,
    data might be missing.
  </summary>
</histogram>

<histogram name="Accessibility.CrosSelectToSpeak.ParagraphNavigationMethod"
    enum="CrosSelectToSpeakActivationMethod" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    When Select-to-speak is active, the user can navigate between paragraphs in
    multiple ways: by clicking the navigation buttons on the Select-to-speak
    menu or using keyboard shortcuts. Track the methods here. Warning: this
    histogram was expired 2023-11-30 to 2024-02-29, data might be missing.
  </summary>
</histogram>

<histogram name="Accessibility.CrosSelectToSpeak.SentenceNavigationMethod"
    enum="CrosSelectToSpeakActivationMethod" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    When Select-to-speak is active, the user can navigate between sentences in
    multiple ways: by clicking the navigation buttons on the Select-to-speak
    menu or using keyboard shortcuts. Track the methods here.
  </summary>
</histogram>

<histogram name="Accessibility.CrosSelectToSpeak.SpeechDuration"
    units="seconds" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the duration that Select-to-Speak is continuously talking. This is
    recorded every time a new speech request is completed in Select-to-Speak,
    and does not reflect total talking time during a user session.
  </summary>
</histogram>

<histogram name="Accessibility.CrosSelectToSpeak.SpeedSetFromBubble"
    enum="CrosSelectToSpeakOverrideSpeechRateMultiplier"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the speed the user selects from the Select-to-Speak speed bubble.
  </summary>
</histogram>

<histogram name="Accessibility.CrosSelectToSpeak.StartSpeechMethod"
    enum="CrosSelectToSpeakStartSpeechMethod" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    A user may activate Select-to-Speak by holding down 'search' and clicking or
    dragging a region with the mouse, or by highlighting an area and using
    search + s to read just the highlighted area. Track the methods here.
  </summary>
</histogram>

<histogram name="Accessibility.CrosSelectToSpeak.StateChangeEvent"
    enum="CrosSelectToSpeakStateChangeEvent" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    A user has tapped a button in the tray to change Select-to-Speak's state.
    The tap was interpreted by Select-to-Speak as a request to start selection,
    to cancel speech, or to cancel selection, depending on Select-to-Speak's
    internal state when the tap occured. This tracks when the button was tapped
    and the event that it generated.
  </summary>
</histogram>

<histogram name="Accessibility.CrosSelectToSpeak.TtsEngineUsed"
    enum="CrosSelectToSpeakTtsEngineUsed" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Which TTS engine was used for making TTS requests from Select-to-speak. This
    is recorded every time a new speech request is made in Select-to-speak. Note
    that this is only recorded on the start of speech, so it is not recorded,
    for example, when the user resumes speech after pausing it.
  </summary>
</histogram>

<histogram
    name="Accessibility.CrosSelectToSpeak.{BubbleType}BubbleVisibleDuration"
    units="ms" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks how long the Select-to-Speak {BubbleType} bubble is displayed
    onscreen.
  </summary>
  <token key="BubbleType">
    <variant name="Menu"/>
    <variant name="Speed"/>
  </token>
</histogram>

<histogram
    name="Accessibility.CrosShelfNavigationButtonsInTabletModeChanged.OOBE"
    enum="BooleanEnabled" expires_after="2021-07-27">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    The value for shelf navigation buttons setting set by the user during OOBE.
    The metric will be reported after the setting has been stable for 10
    seconds, or the OOBE screen in which the value can be set is closed (if the
    setting changes multiple times in quick succession, only the final value
    will be reported).
  </summary>
</histogram>

<histogram
    name="Accessibility.CrosShelfNavigationButtonsInTabletModeChanged.OsSettings"
    enum="BooleanEnabled" expires_after="2021-10-25">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    The value for shelf navigation buttons setting set by the user in Chrome OS
    Settings page. The metric will be reported after the setting change has been
    stable for 10 seconds, or the settings window in which the value is set gets
    closed (if the setting changes multiple times in quick succession, only the
    final value will be reported).
  </summary>
</histogram>

<histogram name="Accessibility.CrosSlowKeysDelay" units="ms"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    The slow keys delay duration in milliseconds set by the user if Slow Keys is
    turned on. Logged once after startup.
  </summary>
</histogram>

<histogram name="Accessibility.CrosSpokenFeedback" enum="BooleanEnabled"
    expires_after="never">
<!-- expires-never: usage drives a11y prioritization in browser and content. -->

  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>Tracks whether the Chrome OS Spoken Feedback feature is on.</summary>
</histogram>

<histogram
    name="Accessibility.CrosSpokenFeedback.BrailleDisplayConnected.ConnectionChanged"
    enum="BooleanConnected" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Logged whenever the connection state of a braille display changes between
    disconnected and connected.
  </summary>
</histogram>

<histogram
    name="Accessibility.CrosSpokenFeedback.BrailleDisplayConnected.ConnectionDuration"
    units="seconds" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the duration of time that a single Braille display was connected.
    This starts when a Braille display connects at startup or due to user input,
    and may stop when the user ends their session by turning off the machine, or
    when they disconnect or turn off the Braille display.
  </summary>
</histogram>

<histogram name="Accessibility.CrosStatusArea.{FeatureName}"
    enum="BooleanToggled" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records when the ChromeOS feature, {FeatureName}, is toggled from the status
    area, also known as &quot;Quick Settings&quot;.
  </summary>
  <token key="FeatureName" variants="BaseCrosAccessibilityFeatures">
    <variant name="Autoclick"/>
    <variant name="LiveCaption"/>
    <variant name="MonoAudio"/>
    <variant name="SpokenFeedback"/>
  </token>
</histogram>

<histogram name="Accessibility.CrosSwitchAccess.AutoScan" enum="BooleanEnabled"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    The value of the Switch Access &quot;Auto Scan&quot; setting, logged
    immediately after toggling. This will show us how often users are turning
    the feature on, and how often they are turning it back off again.
  </summary>
</histogram>

<histogram name="Accessibility.CrosSwitchAccess.AutoScan.KeyboardSpeedMs"
    units="ms" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Measures the user-set speed for scanning between keyboard keys in Switch
    Access, logged immediately after changing the setting. Values range from 1ms
    to 10000ms, in 100ms buckets.
  </summary>
</histogram>

<histogram name="Accessibility.CrosSwitchAccess.AutoScan.SpeedMs" units="ms"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Measures the user-set speed for auto scanning between items in Switch
    Access, logged immediately after changing the setting. Values range from 1ms
    to 10000ms, in 100ms buckets.
  </summary>
</histogram>

<histogram name="Accessibility.CrosSwitchAccess.Error"
    enum="CrosSwitchAccessError" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Switch Access error occurred. See CrosSwitchAccessError enum for error
    types.
  </summary>
</histogram>

<histogram name="Accessibility.CrosSwitchAccess.NextKeyCode" enum="KeyCode"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Which key code user has assigned to the Next Action (e.g. 32 for the Space
    key)
  </summary>
</histogram>

<histogram name="Accessibility.CrosSwitchAccess.PreviousKeyCode" enum="KeyCode"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Which key code user has assigned to the Previous Action (e.g. 32 for the
    Space key)
  </summary>
</histogram>

<histogram name="Accessibility.CrosSwitchAccess.SelectKeyCode" enum="KeyCode"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Which key code user has assigned to the Select Action (e.g. 32 for the Space
    key)
  </summary>
</histogram>

<histogram name="Accessibility.Cros{FeatureName}.SessionDuration"
    units="seconds" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the duration of time that the {FeatureName} feature was enabled
    within a login session. This may start when the user logs in or when they
    enable the feature, and may stop when the user ends their session by logging
    out or turning off the machine, or when they disable the feature.
  </summary>
  <token key="FeatureName" variants="BaseCrosAccessibilityFeatures">
    <variant name="AlwaysShowScrollbar"/>
    <variant name="Autoclick"/>
    <variant name="BounceKeys"/>
    <variant name="CursorColor"/>
    <variant name="DisableTouchpad"/>
    <variant name="FlashNotifications"/>
    <variant name="LiveCaption"/>
    <variant name="MonoAudio"/>
    <variant name="MouseKeys"/>
    <variant name="SlowKeys"/>
    <variant name="SpokenFeedback"/>
  </token>
</histogram>

<!-- This set of histograms is recorded once after startup.
Do not add new variants to this histogram unless they are recorded in the same way,
even if they fit the naming pattern. -->

<histogram name="Accessibility.Cros{SettingType}" enum="BooleanEnabled"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Note: This metric is logged once after startup.

    Whether the user has enabled the Chrome OS feature: {SettingType}
  </summary>
  <token key="SettingType" variants="BaseCrosAccessibilityFeatures">
    <variant name="AlwaysShowA11yMenu"
        summary="to always show the Chrome OS Accessibility Menu, regardless
                 of the state of a11y features. Warning: this histogram was
                 expired 2023-11-30 to 2024-02-29, data might be missing."/>
    <variant name="AlwaysShowScrollbar"
        summary="to always show the scrollbar."/>
    <variant name="Autoclick"
        summary="- Warning: this histogram was expired 2021-12-31 to
                 2022-07-07, and 2023-11-30 to 2024-02-29, data might be
                 missing."/>
    <variant name="BounceKeys"
        summary="Bounce Keys, which ignores quickly repeated presses of the
                 same keyboard key."/>
    <variant name="CursorColor" summary="colorize the cursor"/>
    <variant name="FlashNotifications"
        summary="flash the screen for notifications"/>
    <variant name="MonoAudio.Enabled" summary="MonoAudio"/>
    <variant name="SlowKeys"
        summary="Slow Keys, which adds a delay between when you press a key
                 and when it activates."/>
    <variant name="SpokenFeedback.BrailleDisplayConnected"
        summary="braille display. An enabled (true) value means that a
                 braille display is connected to the device."/>
  </token>
</histogram>

<histogram name="Accessibility.DependencyParserModel.Create.Duration"
    units="ms" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    The time takes to load the file and create the TFLite dependency parser
    model. This histogram is recorded once for each successful creation of the
    model.
  </summary>
</histogram>

<histogram
    name="Accessibility.DependencyParserModel.DependencyParserModelState"
    enum="DependencyParserModelState" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    The state of the dependency parser model used by Reading Mode. Recorded once
    per model load attempt for each renderer process.
  </summary>
</histogram>

<histogram name="Accessibility.DependencyParserModel.Inference.Duration"
    units="ms" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    The time taken to prepare the input and run the TFLite dependency parser
    model.

    This histogram is recorded once for each invocation of the model. The model
    may be invoked multiple times after being loaded, running against a sequence
    of sentences.
  </summary>
</histogram>

<histogram name="Accessibility.DependencyParserModel.Inference.LengthInTokens"
    units="characters" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    The size of the input to the TFLite dependency parser model, in number of
    tokens.

    This histogram is recorded once for each invocation of the model. The model
    may be invoked multiple times after being loaded, running against a sequence
    of sentences.
  </summary>
</histogram>

<histogram name="Accessibility.DependencyParserModel.Inference.Succeed"
    enum="BooleanDetected" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Whether the dependency graph was successfully generated by the TFLite
    dependency parser model.

    This histogram is recorded once for each invocation of the model. The model
    may be invoked multiple times after being loaded, running against a sequence
    of sentences.
  </summary>
</histogram>

<histogram
    name="Accessibility.DependencyParserModelLoader.DependencyParserModel.WasLoaded"
    enum="BooleanLoaded" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records whether the dependency parser model file provided by the
    OptimizationGuide was valid and was successfully loaded by the
    DependencyParserModelLoader. Recorded once per model update by the
    OptimizationGuide.
  </summary>
</histogram>

<histogram name="Accessibility.DisabledAfterHide" enum="BooleanHit"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Recorded each time accessibility for a single WebContents is disabled due to
    the ProgressiveAccessibility feature.
  </summary>
</histogram>

<histogram name="Accessibility.DlcInstallerFaceGazeAssetsInstallationDuration"
    units="ms" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    The FaceGaze assets DLC installation is triggered when one of the features
    using the DLC (currently the only feature using FaceGaze assets is FaceGaze)
    is enabled. This histogram records the time taken to successfully install
    the FaceGaze assets DLC. Note that this histogram is not recorded if the
    installation fails.
  </summary>
</histogram>

<histogram name="Accessibility.DlcInstallerFaceGazeAssetsSuccess"
    enum="BooleanSuccess" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    The FaceGaze assets DLC installation is triggered when one of the features
    using the DLC (currently the only feature using FaceGaze assets is FaceGaze)
    is enabled. This histogram is recorded once when FaceGaze assets
    installation finishes with either success or failure.
  </summary>
</histogram>

<histogram name="Accessibility.DlcInstallerPumpkinInstallationDuration"
    units="ms" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    The Pumpkin DLC installation is triggered when one of the features using the
    DLC (currently the only feature using Pumpkin is Dictation) is enabled. This
    histogram records the time taken to successfully install the Pumpkin DLC.
    Note that this histogram is not recorded if the installation fails.
  </summary>
</histogram>

<histogram name="Accessibility.EngineUse.PageNavsUntilStart" units="count"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    This histogram tracks the number of page navigations from start-up until the
    kWebContents AXMode is first used, up until 10000. This is tracked only one
    per instance, and is recorded as the accessibility code is enable.
  </summary>
</histogram>

<histogram name="Accessibility.EngineUse.TimeUntilStart" units="ms"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    This histogram tracks the time from start-up until the kWebContents AXMode
    is first used. This is tracked only one per instance, and is recorded as the
    accessibility code is enable.
  </summary>
</histogram>

<histogram name="Accessibility.EventProcessingTime3{Variation}" units="ms"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    The round-trip time to send a set of accessibility mode flags from the
    browser process to a renderer and receive and process the first set of
    updates and events. Recorded once each time the browser process receives the
    first set of updates and events from a renderer following {Variation} call
    to RenderAccessibility::SetMode.
  </summary>
  <token key="Variation">
    <variant name=".First" summary="the very first"/>
    <variant name=".NotFirst" summary="all but the first"/>
  </token>
</histogram>

<histogram name="Accessibility.ExperimentalModeFlag.FormControls"
    enum="BooleanEnabled" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records whether the experimental form controls mode flag is set for the
    AXMode. Tracked when the experimental form controls mode flag is flipped to
    true. This flag is flipped automatically when software communicates with
    Chrome via accessibility APIs.

    As of M115, this flag should only be flipped on when the
    AccessibilityPerformanceFiltering feature is enabled.

    This is currently only used on Android OS.
  </summary>
</histogram>

<histogram name="Accessibility.FaceGaze.AverageFaceLandmarkerLatency"
    units="ms" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    The time it takes for the FaceLandmarker API to process a single video
    frame. This histogram is recorded once every 100 frames and represents the
    average latency of the FaceLandmarker API on those frames. The reason we use
    the average is because there could be potentially thousands of calls to the
    FaceLandmarker API during a FaceGaze session and we don't want to clutter
    the histograms. Each call to record a histogram also takes work and we don't
    want to spam the histograms API multiple times per second.
  </summary>
</histogram>

<histogram name="Accessibility.FocusHighlight.ToggleEnabled"
    enum="BooleanEnabled" expires_after="never">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    The value of the &quot;show a quick focus highlight&quot; setting, logged
    immediately after toggling. This will show us how often users are turning
    the feature on, and how often they are turning it back off again.
  </summary>
</histogram>

<histogram name="Accessibility.ImageLabels.Android" enum="BooleanEnabled"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Whether the Chrome accessibility image label setting is on for Android.
    Logged when the profile opens via browser startup, and any time an image
    labelling setting changes.
  </summary>
</histogram>

<histogram name="Accessibility.ImageLabels.Android.DialogOption"
    enum="AccessibilityImageLabelModeAndroid" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    When a user opens the accessibility image label dialog on Android, records
    what option they select on user interaction.
  </summary>
</histogram>

<histogram name="Accessibility.ImageLabels.Android.OnlyOnWifi"
    enum="BooleanEnabled" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Whether the Chrome accessibility image label setting for Android is set to
    only be enabled on a Wi-Fi connection. Logged when the profile opens via
    browser startup, and any time an image labelling setting changes.
  </summary>
</histogram>

<histogram name="Accessibility.ImageLabels.ModalDialogAccepted"
    enum="BooleanAccepted" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Whether a user accepts or does not accept a modal dialog enabling the image
    labels option. If the user does not accept it this does not track whether
    they closed it with the negative button, the close button, or keyboard
    escape.
  </summary>
</histogram>

<histogram name="Accessibility.ImageLabels.PageLanguage" enum="LanguageName"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    The language of a web page where image labels were requested, as detected by
    Chrome (not necessarily what language the page claims to be in).
  </summary>
</histogram>

<histogram name="Accessibility.ImageLabels.RequestLanguage" enum="LanguageName"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    The language that was requested for image descriptions, based on the page
    language, the user's accept languages and top languages, and the server
    languages.
  </summary>
</histogram>

<histogram name="Accessibility.ImageLabels.{Result}By{Dimension}"
    units="pixels" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Counts the number of images that got a certain result from automatic image
    labeling (empty, error, or success), as a function of the function of one of
    the image's dimensions - either the min or the max dimension.
  </summary>
  <token key="Result">
    <variant name="Empty"/>
    <variant name="Error"/>
    <variant name="Success"/>
  </token>
  <token key="Dimension">
    <variant name="MaxDimension"/>
    <variant name="MinDimension"/>
  </token>
</histogram>

<histogram name="Accessibility.ImageLabels.{Result}By{Length}"
    units="characters" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Counts the number of images that got a certain result from automatic image
    labeling (empty, error, or success), as a function of the function of the
    length of the characters in the image's name - either the original name, or
    the name after removing stopwords.
  </summary>
  <token key="Result">
    <variant name="Empty"/>
    <variant name="Error"/>
    <variant name="Success"/>
  </token>
  <token key="Length">
    <variant name="NameLength"/>
    <variant name="NonStopNameLength"/>
  </token>
</histogram>

<histogram name="Accessibility.ImageLabels2" enum="BooleanEnabled"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Whether the Chrome accessibility image label setting is on. Logged when the
    profile opens via browser startup, and any time an image labelling setting
    changes. Only logged for regular profiles, Incognito profiles, and guest
    off-the-record profiles, as these are the only profiles that can display web
    content.
  </summary>
</histogram>

<histogram name="Accessibility.InactiveTime" units="ms"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    The amount of time accessibility was enabled but not actively used. Reported
    when we switch states from inactive to active. This histogram will not be
    logged if the auto-disable accessibility feature is enabled.
  </summary>
</histogram>

<histogram name="Accessibility.InlineTextBoxes.Count" units="count"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks the number of nodes that have inline text boxes loaded per
    AXTreeUpdate, if any are present. We track this to get a sense of the number
    of nodes that we are loading text boxes for, since they have such a large
    performance cost. We track this while computing pending changes of an
    AXTreeUpdate in AXTree::Unserialize.
  </summary>
</histogram>

<histogram name="Accessibility.InlineTextBoxes.PresentInUpdate" enum="Boolean"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks whether or not a given AXTreeUpdate contains any inline text boxes.
    We track this while computing pending changes of an AXTreeUpdate in
    AXTree::Unserialize.
  </summary>
</histogram>

<histogram name="Accessibility.iOS.NewLargerTextCategory" enum="BooleanHit"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    This metric is related to the Preferred Content Size chosen by the user. It
    is only recorded once per session. This is hit if the chosen category is not
    listed in the map defined in
    /ios/chrome/browser/shared/ui/util/dynamic_type_util.mm. In that case, we
    should update the code by adding an entry for the new category in that map.
    This is logged when the helper to returning the multiplier associated with
    the current preferred content size is called.
  </summary>
</histogram>

<histogram name="Accessibility.LanguageDetection.CountDetectionAttempted"
    units="count" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    The number of nodes on this page for which language detection was attempted.
    Warning: this histogram was expired 2023-11-30 to 2024-02-29, data might be
    missing.
  </summary>
</histogram>

<histogram name="Accessibility.LanguageDetection.CountLabelled" units="count"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    The number of nodes on this page for which a detected language was
    successfully assigned. Warning: this histogram was expired 2023-11-30 to
    2024-02-29, data might be missing.
  </summary>
</histogram>

<histogram name="Accessibility.LanguageDetection.LangsPerPage" units="count"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    The number of unique languages which were detected to be the most probable
    language for at least one node on the page, regardless of whether a detected
    language was assigned for that node. Warning: this histogram was expired
    2023-11-30 to 2024-02-29, data might be missing.
  </summary>
</histogram>

<histogram name="Accessibility.LanguageDetection.PercentageLabelledWithTop"
    units="%" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Of the nodes which had a detected language assigned, the percentage for
    which the assigned language was the language detected as having the highest
    probability. Warning: this histogram was expired 2023-11-30 to 2024-02-29,
    data might be missing.
  </summary>
</histogram>

<histogram name="Accessibility.LanguageDetection.PercentageLanguageDetected"
    units="%" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Of the nodes for which language detection was attempted, the percentage for
    which a language was successfully assigned.
  </summary>
</histogram>

<histogram name="Accessibility.LanguageDetection.PercentageOverridden"
    units="%" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Of the nodes for which a detected language was successfully assigned, the
    percentage where the assigned language differs from the author-provided
    language. Warning: this histogram was expired 2023-11-30 to 2024-02-29, data
    might be missing.
  </summary>
</histogram>

<histogram name="Accessibility.LiveCaption.AudioPropertyChanged"
    enum="BooleanEnabled" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Whether the sample rate or channel count of a Live Caption audio stream
    changed midstream. This is logged once per audio stream on the destruction
    of the Cloud speech recognition client.
  </summary>
</histogram>

<histogram name="Accessibility.LiveCaption.CaptionBubbleError"
    enum="CaptionBubbleErrorType" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records when the caption bubble displays an error. This is logged once each
    time the caption bubble model encounters an error.
  </summary>
</histogram>

<histogram name="Accessibility.LiveCaption.Duration.CaptionBubble{Visibility}3"
    units="ms" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Measures how long SODA was running while the Live Caption UI was
    {Visibility}. Logged once on the destruction of the
    SpeechRecognitionRecognizerImpl.
  </summary>
  <token key="Visibility">
    <variant name="Hidden"
        summary="hidden. This might be because it was closed by user or
                 because there was an error passing transcriptions to the UI"/>
    <variant name="Visible" summary="visible and showing transcriptions"/>
  </token>
</histogram>

<histogram name="Accessibility.LiveCaption.EnableFrom{Entrypoint}"
    enum="BooleanEnabled" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records when a user enables or disables the Live Caption feature from
    {Entrypoint}.
  </summary>
  <token key="Entrypoint">
    <variant name="GlobalMediaControls"
        summary="global media controls (Zenith)"/>
    <variant name="Settings" summary="chrome://settings"/>
    <variant name="VideoPictureInPicture" summary="video picture-in-picture"/>
  </token>
</histogram>

<histogram name="Accessibility.LiveCaption.ExpandBubble"
    enum="LiveCaptionExpandBubbleEvent" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records when a user expands or collapses the Live Caption bubble.
  </summary>
</histogram>

<histogram name="Accessibility.LiveCaption.FeatureEnabled2"
    enum="BooleanEnabled" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Whether the Live Caption feature flag is enabled. This is logged once when
    the Live Caption controller is initialized.
  </summary>
</histogram>

<histogram name="Accessibility.LiveCaption.LanguageCount" units="count"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    The number of languages used to initialize the Speech On-Device API (SODA).
    This is logged each time SODA is reset.
  </summary>
</histogram>

<histogram name="Accessibility.LiveCaption.LoadSodaErrorCode"
    enum="WinGetLastError" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    The error code of a failed attempt to load the Speech On-Device API (SODA)
    binary. This is logged once for each media stream if the SODA binary failed
    to load on Windows.
  </summary>
</histogram>

<histogram name="Accessibility.LiveCaption.LoadSodaResult"
    enum="LoadSodaResult" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    The result of an attempt to load the Speech On-Device API (SODA) binary.
    This is logged once for each media stream when the SODA binary is loaded.
  </summary>
</histogram>

<histogram name="Accessibility.LiveCaption.MaskOffensiveWords" enum="Boolean"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records when a user toggles the &quot;hide profanity&quot; option in
    settings.
  </summary>
</histogram>

<histogram name="Accessibility.LiveCaption.PinBubble" enum="Boolean"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>Records when a user pins or unpins the Live Caption bubble.</summary>
</histogram>

<histogram name="Accessibility.LiveCaption.Session2"
    enum="LiveCaptionSessionEvent" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Logged when the Live Caption bubble appears and disappears. Stream start
    indicates when the bubble appears due to an audio session starting. Stream
    end indicates when the bubble disappears due to a tab change, a navigation,
    or the audio session ending and the bubble fading out due to inactivity.
    Close button clicked indicates when the bubble disappears due to a user
    clicking the close button on the caption bubble.
  </summary>
</histogram>

<histogram
    name="Accessibility.LiveCaption.SodaVerificationFailureMissingIndicatorFile"
    enum="Boolean" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Whether the verification of the Speech On-Device API (SODA) component failed
    due to a mismatch of the component architecture. Logged once during the
    verification step of the SODA component installation on Windows only.
  </summary>
</histogram>

<histogram name="Accessibility.LiveCaption.SpeechRecognitionServiceLanguage"
    enum="SodaLanguageCode" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    The language code used to initialize the speech recognition service. Logged
    once when getting the Speech On-Device API (SODA) config path to use with
    the speech recognition service.
  </summary>
</histogram>

<histogram
    name="Accessibility.LiveCaption.{SodaLanguageCode}.SessionContainsRecognizedSpeech"
    enum="BooleanEnabled" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Whether the speech recognition session contains any recognized speech. This
    is logged once per media stream upon the destruction of the
    SpeechRecognitionRecognizerImpl.
  </summary>
  <token key="SodaLanguageCode" variants="SodaLanguageCode"/>
</histogram>

<histogram name="Accessibility.LiveCaption2" enum="BooleanEnabled"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Whether the Live Caption feature is enabled. This is logged once when the
    Live Caption controller is initialized on platforms that support Live
    Caption.
  </summary>
</histogram>

<histogram name="Accessibility.LiveTranslate.Ash.Boca.Babelorca.TargetLanguage"
    enum="LocaleCodeBCP47" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the target language of a translation of live captions from the
    student in School Tools app. Records once per translation requested for live
    captions from the student in School Tools app.
  </summary>
</histogram>

<histogram name="Accessibility.LiveTranslate.CharactersTranslated"
    units="count" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the number of characters sent to the translate service. Records once
    per session upon the destruction of the LiveCaptionSpeechRecognitionHost.
  </summary>
</histogram>

<histogram name="Accessibility.LiveTranslate.EnableFrom{Entrypoint}"
    enum="BooleanEnabled" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records when a user enables or disables the Live Translate feature from
    {Entrypoint}. Warning: this histogram was expired from 2024-06-13 to
    2024-08-15; data may be missing.
  </summary>
  <token key="Entrypoint">
    <variant name="GlobalMediaControls"
        summary="global media controls (Zenith)"/>
    <variant name="Settings" summary="chrome://settings"/>
    <variant name="VideoPictureInPicture" summary="video picture-in-picture"/>
  </token>
</histogram>

<histogram name="Accessibility.LiveTranslate.SourceLanguage"
    enum="LocaleCodeBCP47" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the source language of a request to the Cloud Translate service.
    Recorded once per call to the Cloud Translate service.
  </summary>
</histogram>

<histogram name="Accessibility.LiveTranslate.TargetLanguage"
    enum="LocaleCodeBCP47" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the target language of a request to the Cloud Translate service.
    Recorded once per call to the Cloud Translate service.
  </summary>
</histogram>

<histogram name="Accessibility.MainNodeAnnotations.AnnotationResult"
    enum="MainNodeAnnotationResult" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    The result of the Screen2x run to identify a main node in the accessibility
    tree. The result could successfully identify a main node; could identify an
    invalid node; or could return a result after a previous had already labeled
    a main node.
  </summary>
</histogram>

<histogram name="Accessibility.ManuallyEnabled" enum="BooleanEnabled"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Whether Chrome has enabled accessibility support because the user passed the
    --force-renderer-accessibility flag on the command-line (logged once after
    startup).
  </summary>
</histogram>

<histogram name="Accessibility.ModeFlag" enum="AccessibilityModeFlagEnum"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Whether individual accessibility mode flags are set. Tracked when each mode
    flag is flipped from false to true. These flags are flipped automatically
    when software communicates with Chrome via accessibility APIs. Flags can
    only be flipped off by advanced users or for debugging using
    chrome://accessibility/ - and that isn't tracked in this histogram.
  </summary>
</histogram>

<histogram name="Accessibility.OOBEStartupSoundDelay" units="ms"
    expires_after="never">
<!-- expires-never: Core metric for monitoring OOBE accessibility status. -->

  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Delay between login_prompt_visible and Chrome OS OOBE startup sound
    playback. Depends on sound subsystem initialization time.
  </summary>
</histogram>

<histogram name="Accessibility.OOBEStartupSoundEnabled" enum="BooleanEnabled"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Whether the Chrome OS OOBE startup sound is enabled (logged once after
    startup).
  </summary>
</histogram>

<histogram name="Accessibility.PDF.IsPDFTagged" enum="BooleanExists"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    When a PDF is constructed for accessibility, true if the PDF is tagged as
    specified by ISO 14289-1 (PDF/UA).
  </summary>
</histogram>

<histogram name="Accessibility.PDF.OpenedWith{A11yFeature}.PdfOcr"
    enum="BooleanExists" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Record when PDF is opened with {A11yFeature} with or without PDF OCR.
  </summary>
  <token key="A11yFeature">
    <variant name="ScreenReader"/>
    <variant name="SelectToSpeak"
        summary="Select-to-speak enabed (on ChromeOS only)"/>
  </token>
</histogram>

<histogram name="Accessibility.PdfOcr.CrosSelectToSpeak.PagesOcred"
    units="count" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the number of OCRed pages in the PDF file opened by Select-to-Speak
    users on ChromeOS.
  </summary>
</histogram>

<histogram name="Accessibility.PdfOcr.InaccessiblePdfPageCount" units="count"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the number of pages in an inaccessible PDF when a PDF accessibility
    tree gets created for the PDF. This metric is only recorded when
    accessibility features are enabled.
  </summary>
</histogram>

<histogram name="Accessibility.PdfOcr.MediaApp.ActiveTime" units="seconds"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the time difference between when a ChromeVox user starts reading
    OCRed content in MediaApp and when the user reads it most recently.
  </summary>
</histogram>

<histogram name="Accessibility.PdfOcr.MediaApp.PdfLoaded" enum="BooleanExists"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks how many PDFs were opened in the Media App while the ChromeVox screen
    reader is enabled, which causes the PDF to be OCRed.
  </summary>
</histogram>

<histogram name="Accessibility.PdfOcr.MediaApp.PercentageReadingProgression"
    units="%" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the reading progression of a11y users in OCRed content in MediaApp
    in percentage. The reading progression (RP) is calculated with the following
    formula: RP = greatest visited page number / page count * 100.
  </summary>
</histogram>

<histogram
    name="Accessibility.Performance.AccessibilityTreeSnapshotCombiner::~AccessibilityTreeSnapshotCombiner"
    units="microseconds" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks time spent in
    AccessibilityTreeSnapshotCombiner::~AccessibilityTreeSnapshotCombiner.

    The primary use is to measure the impact of https://crrev.com/c/5410696,
    which updates the class to eliminate potential copies.
  </summary>
</histogram>

<histogram name="Accessibility.Performance.AXObjectCacheImpl.Incremental"
    units="bytes" expires_after="2025-04-13">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks the size of dirty tree serialization from the renderer to the browser
    process.

    Logged every time the document layout is clean and we need to update the
    accessibility tree. Low priority updates may be batched and sent every 150ms
    if a change was detected.
  </summary>
</histogram>

<histogram
    name="Accessibility.Performance.AXObjectCacheImpl.Incremental.{DataType}"
    units="bytes" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks the contribution of {DataType} valued properties to the overall size
    of dirty tree serialization from the renderer to the browser process.

    Logged every time the document layout is clean and we need to update the
    accessibility tree. Low priority updates may be batched and sent every 150ms
    if a change was detected.
  </summary>
  <token key="DataType">
    <variant name="Bool"/>
    <variant name="ChildIds"/>
    <variant name="Float"/>
    <variant name="HTML"/>
    <variant name="Int"/>
    <variant name="IntList"/>
    <variant name="String"/>
    <variant name="StringList"/>
  </token>
</histogram>

<histogram name="Accessibility.Performance.AXObjectCacheImpl.Snapshot"
    units="bytes" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks the size of entire tree (snapshot) serialization from the renderer to
    the browser process. Snapshots are on demand when the entire a11y tree needs
    to be send from the renderer to the browser process.
  </summary>
</histogram>

<histogram name="Accessibility.Performance.AXTree.Destroy2"
    units="microseconds" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks time spent in AXTree::Destroy.

    The primary use is to measure the impact of https://crrev.com/c/3937946,
    which updates AXTree to internally use a std::map instead of a
    base::flat_map. Please see the CL description for more context and a
    performance profile.

    Warning: This metric may include reports from clients with low-resolution
    clocks (i.e. on Windows, ref. |TimeTicks::IsHighResolution()|). Such reports
    will cause this metric to have an abnormal distribution. When considering
    revising this histogram, see UMA_HISTOGRAM_CUSTOM_MICROSECONDS_TIMES for the
    solution.
  </summary>
</histogram>

<histogram name="Accessibility.Performance.AXTreeCombiner::Combine"
    units="microseconds" expires_after="2025-05-04">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks time spent in AXTreeCombiner::Combine.

    The primary use is to measure the impact of https://crrev.com/c/5410696,
    which updates the class to eliminate potential copies.
  </summary>
</histogram>

<histogram
    name="Accessibility.Performance.BrowserAccessibilityCocoa::childrenChanged"
    units="microseconds" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks time spent on the browser main thread handling requests for children
    on Mac.

    Warning: This metric may include reports from clients with low-resolution
    clocks (i.e. on Windows, ref. |TimeTicks::IsHighResolution()|). Such reports
    will cause this metric to have an abnormal distribution. When considering
    revising this histogram, see UMA_HISTOGRAM_CUSTOM_MICROSECONDS_TIMES for the
    solution.
  </summary>
</histogram>

<histogram
    name="Accessibility.Performance.BrowserAccessibilityCocoa::needsToUpdateChildren"
    enum="Boolean" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records if children need to be re-built on a request for children in the Mac
    accessibility tree.
  </summary>
</histogram>

<histogram
    name="Accessibility.Performance.BrowserAccessibilityManager::OnAccessibilityEvents2"
    units="microseconds" expires_after="never">
<!-- expires-never: vital accessibility performance metric -->

  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks time spent on the browser main thread handling accessibility events
    sent from the renderer process and processed by BrowserAccessibilityManager.

    Accessibility.Performance.HandleAXEvents can still often record values when
    processing is relatively cheap because we exit before handing off the nodes
    to BrowserAccessibilityManager for various reasons (e.g. BFCache). This
    histogram only measures events which are processed by
    BrowserAccessibilityManager and thus should have a better histogram to
    understand the cost of doing so, and be used to meaure optimizations.

    Warning: This metric may include reports from clients with low-resolution
    clocks (i.e. on Windows, ref. |TimeTicks::IsHighResolution()|). Such reports
    will cause this metric to have an abnormal distribution. When considering
    revising this histogram, see UMA_HISTOGRAM_CUSTOM_MICROSECONDS_TIMES for the
    solution.
  </summary>
</histogram>

<histogram
    name="Accessibility.Performance.BrowserAccessibilityManager::OnLocationChanges"
    units="microseconds" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks time spent on the browser main thread handling location changes sent
    from the renderer process and processed by BrowserAccessibilityManager.

    Warning: This metric may include reports from clients with low-resolution
    clocks (i.e. on Windows, ref. |TimeTicks::IsHighResolution()|). Such reports
    will cause this metric to have an abnormal distribution. When considering
    revising this histogram, see UMA_HISTOGRAM_CUSTOM_MICROSECONDS_TIMES for the
    solution.
  </summary>
</histogram>

<histogram name="Accessibility.Performance.FinalizingTreeLifecycleStage"
    units="microseconds" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks time spent on the render process executing the lifecycle stage
    FinalizingTree.

    Warning: This metric may include reports from clients with low-resolution
    clocks (i.e. on Windows, ref. |TimeTicks::IsHighResolution()|). Such reports
    will cause this metric to have an abnormal distribution. When considering
    revising this histogram, see UMA_HISTOGRAM_CUSTOM_MICROSECONDS_TIMES for the
    solution.
  </summary>
</histogram>

<histogram name="Accessibility.Performance.HandleAXEvents2"
    units="microseconds" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks time spent on the browser main thread handling accessibility events
    sent from the renderer process. Logged each time the method is run without
    returning early.

    Warning: This metric may include reports from clients with low-resolution
    clocks (i.e. on Windows, ref. |TimeTicks::IsHighResolution()|). Such reports
    will cause this metric to have an abnormal distribution. When considering
    revising this histogram, see UMA_HISTOGRAM_CUSTOM_MICROSECONDS_TIMES for the
    solution.
  </summary>
</histogram>

<histogram name="Accessibility.Performance.MergeAXTreeUpdates"
    units="microseconds" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks time spent in ui::MergeAXTreeUpdates.

    The primary use is to measure the impact of https://crrev.com/c/5413915,
    which updates the function to eliminate potential copies.
  </summary>
</histogram>

<histogram
    name="Accessibility.Performance.ProcessDeferredUpdatesLifecycleStage"
    units="microseconds" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks time spent on the render process executing the accessibility stage
    ProcessDeferredUpdates.

    Warning: This metric may include reports from clients with low-resolution
    clocks (i.e. on Windows, ref. |TimeTicks::IsHighResolution()|). Such reports
    will cause this metric to have an abnormal distribution. When considering
    revising this histogram, see UMA_HISTOGRAM_CUSTOM_MICROSECONDS_TIMES for the
    solution.
  </summary>
</histogram>

<histogram
    name="Accessibility.Performance.SendPendingAccessibilityEvents.PostLoad2"
    units="microseconds" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks time spent on the render process sending pending accessibility
    events. Logging commences after loading is complete and triggered each time
    the method is run without returning early.

    Warning: This metric may include reports from clients with low-resolution
    clocks (i.e. on Windows, ref. |TimeTicks::IsHighResolution()|). Such reports
    will cause this metric to have an abnormal distribution. When considering
    revising this histogram, see UMA_HISTOGRAM_CUSTOM_MICROSECONDS_TIMES for the
    solution.
  </summary>
</histogram>

<histogram name="Accessibility.Performance.SendPendingAccessibilityEvents2"
    units="microseconds" expires_after="never">
<!-- expires-never: vital accessibility performance metric -->

  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks time spent on the render process sending pending accessibility
    events. Logged each time the method is run without returning early.

    Warning: This metric may include reports from clients with low-resolution
    clocks (i.e. on Windows, ref. |TimeTicks::IsHighResolution()|). Such reports
    will cause this metric to have an abnormal distribution. When considering
    revising this histogram, see UMA_HISTOGRAM_CUSTOM_MICROSECONDS_TIMES for the
    solution.
  </summary>
</histogram>

<histogram name="Accessibility.Performance.SerializeLifecycleStage"
    units="microseconds" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks time spent on the render process executing the lifecycle stage
    Serialize.

    Warning: This metric may include reports from clients with low-resolution
    clocks (i.e. on Windows, ref. |TimeTicks::IsHighResolution()|). Such reports
    will cause this metric to have an abnormal distribution. When considering
    revising this histogram, see UMA_HISTOGRAM_CUSTOM_MICROSECONDS_TIMES for the
    solution.
  </summary>
</histogram>

<histogram name="Accessibility.Performance.SerializeLocationChanges"
    units="microseconds" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks time spent on the render process sending pending accessibility
    location changes. Logged each time the method is run without returning
    early.

    Warning: This metric may include reports from clients with low-resolution
    clocks (i.e. on Windows, ref. |TimeTicks::IsHighResolution()|). Such reports
    will cause this metric to have an abnormal distribution. When considering
    revising this histogram, see UMA_HISTOGRAM_CUSTOM_MICROSECONDS_TIMES for the
    solution.
  </summary>
</histogram>

<histogram
    name="Accessibility.Performance.TotalAccessibilityCleanLayoutLifecycleStages"
    units="microseconds" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks time spent on the render process executing all the accessibility
    lifecycle stages that run when layout is clean.

    Warning: This metric may include reports from clients with low-resolution
    clocks (i.e. on Windows, ref. |TimeTicks::IsHighResolution()|). Such reports
    will cause this metric to have an abnormal distribution. When considering
    revising this histogram, see UMA_HISTOGRAM_CUSTOM_MICROSECONDS_TIMES for the
    solution.
  </summary>
</histogram>

<histogram name="Accessibility.Performance.Tree.Unserialize2"
    units="microseconds" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks time spent on the browser process unserializing accessibility
    updates. Logged each time the method is run without returning early.

    Warning: This metric may include reports from clients with low-resolution
    clocks (i.e. on Windows, ref. |TimeTicks::IsHighResolution()|). Such reports
    will cause this metric to have an abnormal distribution. When considering
    revising this histogram, see UMA_HISTOGRAM_CUSTOM_MICROSECONDS_TIMES for the
    solution.
  </summary>
</histogram>

<histogram name="Accessibility.Performance.WinAPIs.{API}" units="microseconds"
    expires_after="2025-05-04">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks time taken to execute the public Windows API that {API}. This is
    recorded for all Windows users that call into our public APIs.

    Warning: This metric may include reports from clients with low-resolution
    clocks (i.e. on Windows, ref. |TimeTicks::IsHighResolution()|). Such reports
    will cause this metric to have an abnormal distribution. When considering
    revising this histogram, see UMA_HISTOGRAM_CUSTOM_MICROSECONDS_TIMES for the
    solution.
  </summary>
  <token key="API">
    <variant name="UMA_API_ELEMENT_PROVIDER_FROM_POINT"
        summary="finds a node given a point."/>
    <variant name="UMA_API_GET_BOUNDINGRECTANGLE"
        summary="gets the bounding rectangle of a node."/>
    <variant name="UMA_API_GET_PATTERN_PROVIDER"
        summary="retrieves an object that provides support for the given
                 control pattern."/>
    <variant name="UMA_API_GET_PROPERTY_VALUE"
        summary="gets the value of the specified property."/>
    <variant name="UMA_API_NAVIGATE"
        summary="navigates by one node in the given direction."/>
    <variant name="UMA_API_TEXT_RANGEFROMPOINT"
        summary="gets the text range under the given point."/>
    <variant name="UMA_API_TEXTRANGE_COMPARE"
        summary="compares two text ranges"/>
    <variant name="UMA_API_TEXTRANGE_COMPAREENDPOINTS"
        summary="compares two endpoints"/>
    <variant name="UMA_API_TEXTRANGE_EXPANDTOENCLOSINGUNIT"
        summary="expands or contracts the text range to given unit."/>
    <variant name="UMA_API_TEXTRANGE_FINDATTRIBUTE"
        summary="searches for an attribute within text range."/>
    <variant name="UMA_API_TEXTRANGE_FINDTEXT"
        summary="searches for given text within the text range."/>
    <variant name="UMA_API_TEXTRANGE_GETATTRIBUTEVALUE"
        summary="gets the value of the given attribute."/>
    <variant name="UMA_API_TEXTRANGE_GETBOUNDINGRECTANGLES"
        summary="gets the bounding rectanges of a text range."/>
    <variant name="UMA_API_TEXTRANGE_GETCHILDREN"
        summary="gets all children within a text range."/>
    <variant name="UMA_API_TEXTRANGE_GETENCLOSINGELEMENT"
        summary="gets the element that encloses the text range."/>
    <variant name="UMA_API_TEXTRANGE_GETTEXT"
        summary="gets the text within a text range."/>
    <variant name="UMA_API_TEXTRANGE_MOVE"
        summary="moves the text range by the given amount."/>
    <variant name="UMA_API_TEXTRANGE_MOVEENDPOINTBYUNIT"
        summary="moves endpoint by the given unit."/>
    <variant name="UMA_API_TEXTRANGE_MOVEENPOINTBYRANGE"
        summary="moves endpoint by the given range."/>
  </token>
</histogram>

<histogram name="Accessibility.Performance.WinAPIs2.{Target}.{API}"
    units="microseconds" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    The time to execute a call to the UMA_API_ELEMENT_PROVIDER_FROM_POINT method
    on a View by an accessibility tool. Recorded once for every call.

    Warning: This metric may include reports from clients with low-resolution
    clocks (i.e. on Windows, ref. |TimeTicks::IsHighResolution()|). Such reports
    will cause this metric to have an abnormal distribution. When considering
    revising this histogram, see UMA_HISTOGRAM_CUSTOM_MICROSECONDS_TIMES for the
    solution.
  </summary>
  <token key="Target">
    <variant name="View" summary="The API was called on a view."/>
    <variant name="WebContents" summary="The API was called on web contents"/>
  </token>
  <token key="API">
    <variant name="UMA_API_ELEMENT_PROVIDER_FROM_POINT"
        summary="finds a node given a point."/>
    <variant name="UMA_API_GET_BOUNDINGRECTANGLE"
        summary="gets the bounding rectangle of a node."/>
    <variant name="UMA_API_GET_PATTERN_PROVIDER"
        summary="retrieves an object that provides support for the given
                 control pattern."/>
    <variant name="UMA_API_GET_PROPERTY_VALUE"
        summary="gets the value of the specified property."/>
    <variant name="UMA_API_NAVIGATE"
        summary="navigates by one node in the given direction."/>
    <variant name="UMA_API_TEXTRANGE_COMPARE"
        summary="compares two text ranges"/>
    <variant name="UMA_API_TEXTRANGE_COMPAREENDPOINTS"
        summary="compares two endpoints"/>
    <variant name="UMA_API_TEXTRANGE_EXPANDTOENCLOSINGUNIT"
        summary="expands or contracts the text range to given unit."/>
    <variant name="UMA_API_TEXTRANGE_FINDATTRIBUTE"
        summary="searches for an attribute within text range."/>
    <variant name="UMA_API_TEXTRANGE_FINDTEXT"
        summary="searches for given text within the text range."/>
    <variant name="UMA_API_TEXTRANGE_GETATTRIBUTEVALUE"
        summary="gets the value of the given attribute."/>
    <variant name="UMA_API_TEXTRANGE_GETBOUNDINGRECTANGLES"
        summary="gets the bounding rectanges of a text range."/>
    <variant name="UMA_API_TEXTRANGE_GETCHILDREN"
        summary="gets all children within a text range."/>
    <variant name="UMA_API_TEXTRANGE_GETENCLOSINGELEMENT"
        summary="gets the element that encloses the text range."/>
    <variant name="UMA_API_TEXTRANGE_GETTEXT"
        summary="gets the text within a text range."/>
    <variant name="UMA_API_TEXTRANGE_MOVE"
        summary="moves the text range by the given amount."/>
    <variant name="UMA_API_TEXTRANGE_MOVEENDPOINTBYUNIT"
        summary="moves endpoint by the given unit."/>
    <variant name="UMA_API_TEXTRANGE_MOVEENPOINTBYRANGE"
        summary="moves endpoint by the given range."/>
  </token>
</histogram>

<histogram name="Accessibility.ReadAnything.Algorithm.AddedToScreen2x"
    enum="BooleanYesNo" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records if main content extraction algorithm had any nodes to add to
    Screen2x results. This metric is recorded only when Screen2x has some
    results.
  </summary>
</histogram>

<histogram name="Accessibility.ReadAnything.Algorithm.HadWhenScreen2xEmpty"
    enum="BooleanYesNo" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records if main content extraction algorithm had any nodes when Screen2x
    results was empty. This metric is recorded only when Screen2x has no
    results.
  </summary>
</histogram>

<histogram name="Accessibility.ReadAnything.Color" enum="ReadAnythingColor"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the user-chosen color theme for the Read Anything panel.
  </summary>
</histogram>

<histogram name="Accessibility.ReadAnything.EmptyState"
    enum="ReadAnythingEmptyState" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records when the Read Anything panel displays the empty state and whether a
    user does a selection after it's shown.
  </summary>
</histogram>

<histogram name="Accessibility.ReadAnything.FontName"
    enum="ReadAnythingFontName" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the user-chosen font name for the Read Anything panel.
  </summary>
</histogram>

<histogram name="Accessibility.ReadAnything.FontScale" units="em"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the user-chosen font scale for the Read Anything panel.
  </summary>
</histogram>

<histogram name="Accessibility.ReadAnything.Heuristics"
    enum="ReadAnythingHeuristics" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    For each node returned by main content extractor, records if the node is
    pruned by one of the heuristics, or value none if it was not puned. This
    metric would be used to find out which heuristics are needed and can be
    passed to Screen2x pre/post processing.
  </summary>
</histogram>

<histogram name="Accessibility.ReadAnything.Language" enum="LocaleCodeBCP47"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the language of the web page shown in Read Anything after the
    corresponding AXTree is distilled.
  </summary>
</histogram>

<histogram name="Accessibility.ReadAnything.LetterSpacing"
    enum="ReadAnythingLetterSpacing" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the user-chosen letter spacing for the Read Anything panel.
  </summary>
</histogram>

<histogram name="Accessibility.ReadAnything.LineSpacing"
    enum="ReadAnythingLineSpacing" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the user-chosen line spacing for the Read Anything panel.
  </summary>
</histogram>

<histogram name="Accessibility.ReadAnything.MergedDistillationTime.{Result}"
    units="ms" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the time taken to distill a web page using the merged algorithm that
    combines results from the rules based algorithm and the ML model. Refer to
    AXTreeDistiller::Distill for details.
  </summary>
  <token key="Result">
    <variant name="Failure" summary="has failed"/>
    <variant name="Success" summary="has succeeded"/>
  </token>
</histogram>

<histogram name="Accessibility.ReadAnything.NewPage" enum="ReadAnythingNewPage"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records when the Read Anything panel distills a new page and whether the
    user uses Read Aloud to play the text aloud for the new page.
  </summary>
</histogram>

<histogram name="Accessibility.ReadAnything.OmniboxIconShown"
    enum="BooleanShown" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>Records when the Read Anything omnibox icon is shown.</summary>
</histogram>

<histogram name="Accessibility.ReadAnything.ReadAloud.HighlightGranularity"
    enum="ReadAnythingHighlightState" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the user-chosen highlight granularity for reading aloud in the Read
    Anything panel.
  </summary>
</histogram>

<histogram name="Accessibility.ReadAnything.ReadAloud.HighlightState"
    enum="ReadAnythingHighlightState" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the user-chosen highlight state for reading aloud in the Read
    Anything panel.
  </summary>
</histogram>

<histogram name="Accessibility.ReadAnything.ReadAloud.Language"
    enum="LocaleCodeBCP47" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records which language the user is using to read aloud text in the Read
    Anything panel.
  </summary>
</histogram>

<histogram name="Accessibility.ReadAnything.ReadAloud.SettingsChange"
    enum="ReadAnythingReadAloudSettingsChange" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records when the user changes a read aloud speech setting in the Read
    Anything panel.
  </summary>
</histogram>

<histogram name="Accessibility.ReadAnything.ReadAloud.Voice"
    enum="ReadAnythingReadAloudVoice2" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records which kind of voice the user is using to read aloud text in the Read
    Anything panel. This is recorded when speech stops, either from user action
    or when speech completes, to avoid blocking speech.
  </summary>
</histogram>

<histogram name="Accessibility.ReadAnything.ReadAloud.VoiceSpeed"
    units="speech rate %" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the user-chosen voice speed for reading aloud in the Read Anything
    panel.
  </summary>
</histogram>

<histogram name="Accessibility.ReadAnything.ReadAloud{Action}SessionCount"
    units="count" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the count per session of the number of Read Aloud {Action} actions.
  </summary>
  <token key="Action">
    <variant name="NextButton" summary="next granularity"/>
    <variant name="Pause" summary="pause speech"/>
    <variant name="Play" summary="play speech"/>
    <variant name="PreviousButton" summary="previous granularity"/>
  </token>
</histogram>

<histogram name="Accessibility.ReadAnything.RulesDistillationTime.{Result}"
    units="ms" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the time taken to distill a web page using the rules based
    algorithm. Refer to AXTreeDistiller::DistillViaAlgorithm for details.
  </summary>
  <token key="Result">
    <variant name="Failure" summary="has failed"/>
    <variant name="Success" summary="has succeeded"/>
  </token>
</histogram>

<histogram name="Accessibility.ReadAnything.ScrollEvent"
    enum="ReadAnythingScrollEvent" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>Records when a scroll happens.</summary>
</histogram>

<histogram name="Accessibility.ReadAnything.SettingsChange"
    enum="ReadAnythingSettingsChange" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records when the user changes a text style setting in the Read Anything
    panel.
  </summary>
</histogram>

<histogram name="Accessibility.ReadAnything.SpeechError"
    enum="ReadAnythingSpeechError" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records when a speech error event is captured with Read Anything Read Aloud.
  </summary>
</histogram>

<histogram name="Accessibility.ReadAnything.SpeechPlaybackSession" units="ms"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the length of time of a speech playback session from play to pause
    or from play to speech stopped, either due to reaching the end of content or
    an error.
  </summary>
</histogram>

<histogram name="Accessibility.ReadAnything.SpeechStopSource"
    enum="ReadAnythingSpeechError" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>Records when speech stops and why.</summary>
</histogram>

<histogram
    name="Accessibility.ReadAnything.TimeFromEntryTriggeredToWebUIConnected"
    units="ms" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the time from when opening the Read Anything side panel entry first
    triggers the renderer and when the WebUI connects.
  </summary>
</histogram>

<histogram name="Accessibility.ReadAnything.TimeFrom{Flow}StartedTo{Method}"
    units="ms" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the time from when opening {Flow} first is triggered and when its
    {Method} is called.
  </summary>
  <token key="Flow">
    <variant name="App" summary="read_anything/app.ts"/>
    <variant name="AppConstructor" summary="Deprecated 2025-03-19"/>
    <variant name="Toolbar" summary="read_anything/read_anything_toolbar.ts"/>
    <variant name="ToolbarConstructor" summary="Deprecated 2025-03-19"/>
  </token>
  <token key="Method">
    <variant name="ConnectedCallback" summary="Deprecated 2025-03-19"/>
    <variant name="Constructor"/>
  </token>
</histogram>

<histogram name="Accessibility.Reliability.Tree.UnserializeError"
    enum="AccessibilityTreeUnserializeError" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks the type of failure that occured when unserializing the accessibility
    tree. When an error in unserialization occurs, this is emitted and
    unserialization aborts. In the case of a tree with multiple issues, only the
    first error is recorded. Refer to AXTree::Unserialize for details. Warning:
    this histogram was expired 2023-11-30 to 2024-02-29, data might be missing.
  </summary>
</histogram>

<histogram name="Accessibility.ScreenAI.Component.Available2"
    enum="ComponentAvailability" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records if the component was available or could be downloaded when it was
    needed. If not available, it also records if the network connection existed
    or not.
  </summary>
</histogram>

<histogram name="Accessibility.ScreenAI.Component.InstallRetries" units="count"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the number of times that the component installation has failed as
    DLC service was busy and retried before successful install.
  </summary>
</histogram>

<histogram name="Accessibility.ScreenAI.DeviceCompatible" enum="Boolean"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records if ScreenAI library is compatible with the device. The metric is
    recorded when component updater or DLC downloader check if the library
    should be downloaded.
  </summary>
</histogram>

<histogram name="Accessibility.ScreenAI.DlcInstallResult"
    enum="DlcInstallResult" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>Records the result of Screen AI DLC installation.</summary>
</histogram>

<histogram name="Accessibility.ScreenAI.LibraryLoadDetailedResultOnWindows"
    enum="WinGetLastError" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records a Screen AI library load result on Windows. The result is recorded
    after initialization of Screen AI service and loading chrome_screen_ai.dll.
  </summary>
</histogram>

<histogram
    name="Accessibility.ScreenAI.MainContentExtraction.Error.{error_type}"
    enum="MainContentExtractionClientType" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records if main content extraction failed because {error_type}.
  </summary>
  <token key="error_type">
    <variant name="ResultEmpty" summary="Screen2x returned empty"/>
    <variant name="ResultNull" summary="Screen2x returned null"/>
    <variant name="SnapshotEmpty" summary="input snapshot was empty"/>
    <variant name="SnapshotProto"
        summary="input snapshot failed to convert to proto"/>
    <variant name="SnapshotUnserialize"
        summary="input snapshot could not be unserialized"/>
  </token>
</histogram>

<histogram name="Accessibility.ScreenAI.MainContentExtraction.Latency.{result}"
    units="ms" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the time taken to run main content extraction when it has {result}
    to return non empty results.
  </summary>
  <token key="result">
    <variant name="Failure" summary="failed"/>
    <variant name="Success" summary="succeeded"/>
  </token>
</histogram>

<histogram name="Accessibility.ScreenAI.MainContentExtraction.Successful2"
    enum="BooleanSuccess" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records if a request to Main Content Extraction feature of ScreenAI service
    returned non empty results. This metric is recorded only for requests that
    are sent to the Screen2x library and did not fail in preprocessing steps.
  </summary>
</histogram>

<histogram name="Accessibility.ScreenAI.OCR.ClientType" enum="OCRClientType"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>Records the client type of each OCR request.</summary>
</histogram>

<histogram name="Accessibility.ScreenAI.OCR.Downsampled.ClientType"
    enum="OCRClientType" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the client type of the OCR requests that have width or height above
    the threshold that results in downsampling.
  </summary>
</histogram>

<histogram name="Accessibility.ScreenAI.OCR.Failed.ClientType"
    enum="OCRClientType" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>Records the client type of each failed OCR request.</summary>
</histogram>

<histogram name="Accessibility.ScreenAI.OCR.ImageSize.PDF.{Result}"
    units="count" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the number of pixels in the passed image to OCR from PDF, when the
    output had {Result}.
  </summary>
  <token key="Result">
    <variant name="NoText" summary="no text"/>
    <variant name="WithText" summary="text"/>
  </token>
</histogram>

<histogram name="Accessibility.ScreenAI.OCR.Latency.{DownsampleStatus}"
    units="ms" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the latency for an OCR request in Screen AI service for an image
    with width and height {DownsampleStatus} downsampling threshold.
  </summary>
  <token key="DownsampleStatus">
    <variant name="Downsampled" summary="above"/>
    <variant name="NotDownsampled" summary="below"/>
  </token>
</histogram>

<histogram name="Accessibility.ScreenAI.OCR.LinesCount" units="count"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the number of lines that OCR recognized for an image.
  </summary>
</histogram>

<histogram name="Accessibility.ScreenAI.OCR.LinesCount.PDF" units="count"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the number of lines that OCR recognized for an image sent from PDF.
  </summary>
</histogram>

<histogram name="Accessibility.ScreenAI.OCR.MostDetectedLanguage.PDF"
    enum="LocaleCodeBCP47" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the language that is detected most when OCR is performed on a PDF
    page.
  </summary>
</histogram>

<histogram name="Accessibility.ScreenAI.OCR.Successful" enum="BooleanSuccess"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records if a request to Optical Character Recognition feature of ScreenAI
    service was successful or not.
  </summary>
</histogram>

<histogram name="Accessibility.ScreenAI.OCR.Time.PDF" units="ms"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records how long an OCR request in Screen AI service takes for a PDF
    request.
  </summary>
</histogram>

<histogram name="Accessibility.ScreenAI.Screen2xDistillationTime.{Result}"
    units="ms" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the time taken to distill a web page for cases where the action
    {Result}. It's being emitted each time whenever it distills or fails to
    distill a page. for Screen2x. Refer to
    ScreenAIService::ExtractMainContentInternal for details.
  </summary>
  <token key="Result">
    <variant name="Failure" summary="has failed"/>
    <variant name="Success" summary="has succeeded"/>
  </token>
</histogram>

<histogram name="Accessibility.ScreenAI.Searchify.ScreenReaderModeEnabled"
    enum="Boolean" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks if screen reader mode was enabled in the browser when PDF Searchifier
    added text to a PDF. This metric is recorded only once for each PDF, and
    records the status when searchify adds the first text to the page. If screen
    reader status changes after the first page is searchified, it's not
    recorded. The metric is recorded when browser is in screen reader compatible
    mode and it does not necessarily mean that a screen reader is running on the
    OS.
  </summary>
</histogram>

<histogram name="Accessibility.ScreenAI.Service.NotResponsive.IsOCR"
    enum="BooleanYesNo" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records when ScreenAI service does not get a reply from the library in one
    minute after a request. Records if the request was for OCR or not.
  </summary>
</histogram>

<histogram name="Accessibility.ScreenAI.{ServiceName}.InitializationLatency"
    units="ms" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records how long it takes to initialize {ServiceName} functionality in
    Screen AI service.
  </summary>
  <token key="ServiceName" variants="ScreenAIServices"/>
</histogram>

<histogram name="Accessibility.ScreenAI.{Step}.Initialized"
    enum="BooleanSuccess" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records if initialization of {Step} in Screen AI service was successful or
    not.
  </summary>
  <token key="Step">
    <variant name="Library" summary="library general functionalities"/>
    <variant name="MainContentExtraction"
        summary="Main Content Extraction functionality"/>
    <variant name="OCR" summary="OCR functionality"/>
  </token>
</histogram>

<histogram name="Accessibility.ScreenReader.ScrollToImage" enum="Boolean"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Whether the result of a scroll to make visible accessibility action is
    performed on an image node. This roughly corresponds to accessibility focus
    on an image. Logged from ChromeVox (ChromeOS) and
    BrowserAccessibilityManager (non-ChromeOS).
  </summary>
</histogram>

<histogram name="Accessibility.SetValue.Role" enum="AccessibilityRole"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Accessibility node roles where kSetValue is being called on. This should
    help us determine whether we can deprecate kSetValue. Recorded whenever a
    set value action is requested on an accessibility node.
  </summary>
</histogram>

<histogram name="Accessibility.VTTContainsStyleBlock" enum="BooleanEnabled"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Whether a VTT file contains an embedded style block. Recorded when a VTT
    file is parsed.
  </summary>
</histogram>

<histogram name="Accessibility.WebSpeech.Duration" units="ms"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Measures the duration of a call to the WebSpeech API. Recorded once per call
    to the Open Speech API that powers the WebSpeech API.
  </summary>
</histogram>

<histogram name="Accessibility.WebSpeech.OnDeviceAvailable" enum="Boolean"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Whether on-device speech recognition available for use with the Web Speech
    API. Logged when the speech recognition session is created.
  </summary>
</histogram>

<histogram name="Accessibility.WebSpeech.UseAudioForwarder" enum="Boolean"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Whether the audio forwarder is used for the Web Speech API. Logged when the
    speech recognition session is created.
  </summary>
</histogram>

<histogram name="Accessibility.WebSpeech.UseOnDevice" enum="Boolean"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Whether on-device speech recognition is used for the Web Speech API. Logged
    when the speech recognition session is created.
  </summary>
</histogram>

<histogram name="Accessibility.WinAPIs" enum="AccessibilityWinAPIEnum"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>Tracks usage of all public Windows accessibility APIs.</summary>
</histogram>

<histogram name="Accessibility.WinAPIs.GetPropertyValue"
    enum="AccessibilityWinAPIGetPropertyValueEnum" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks properties requested via UI Automation GetPropertyValue(). Warning:
    this histogram was expired 2023-11-30 to 2024-02-29, data might be missing.
  </summary>
</histogram>

<histogram name="Accessibility.WinAPIs.UIAutomation" enum="BooleanEnabled"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>Tracks presence of UI Automation core library (in process).</summary>
</histogram>

<histogram name="Accessibility.WinHighContrastTheme"
    enum="AccessibilityHighContrastColorScheme" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks which Windows high contrast theme is set in system settings (logged
    once after startup). AccessibilityHighContrastColorScheme::None indicates
    that high contrast mode is not enabled.
  </summary>
</histogram>

<histogram name="Accessibility.WinJAWSCompatibleWithUIA" enum="Boolean"
    expires_after="never">
<!-- expires-never: usage drives a11y prioritization in browser and content. -->

  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks if the JAWS version predates the fix for Chrome’s UIA support. True
    if the version is newer or exactly JAWS 2022.2402.1, 2023.2402.1,
    2024.2312.99, or 2025. False otherwise.
  </summary>
</histogram>

<histogram name="Accessibility.WinJAWSVersion" enum="JAWSMajorVersion"
    expires_after="never">
<!-- expires-never: usage drives a11y prioritization in browser and content. -->

  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks the major version of JAWS detected. Recorded when the AXMode in the
    browser process changes, usually when an AT connects or at launch when an AT
    is already connected.
  </summary>
</histogram>

<histogram name="Accessibility.WinNVDAVersion" enum="NVDAMajorVersion"
    expires_after="never">
<!-- expires-never: usage drives a11y prioritization in browser and content. -->

  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks the major version of NVDA detected. Recorded when the AXMode in the
    browser process changes, usually when an AT connects or at launch when an AT
    is already connected.
  </summary>
</histogram>

<histogram name="Accessibility.WinStickyKeys" enum="BooleanEnabled"
    expires_after="never">
<!-- expires-never: usage drives a11y prioritization in browser and content. -->

  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Whether Windows system settings show that Sticky Keys are enabled.
  </summary>
</histogram>

<histogram name="Accessibility.WinZoomText" enum="BooleanEnabled"
    expires_after="never">
<!-- expires-never: usage drives a11y prioritization in browser and content. -->

  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks whether the third-party ZoomText screen magnifier is running on
    Windows.
  </summary>
</histogram>

<histogram name="Accessibility.{ScreenReader}" enum="BooleanEnabled"
    expires_after="never">
<!-- expires-never: usage drives a11y prioritization in browser and content. -->

  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    - Tracks whether a screen reader is enabled -- {ScreenReader}.
  </summary>
  <token key="ScreenReader">
    <variant name="Android.TalkBack" summary="Android, specifically TalkBack."/>
    <variant name="Linux.Orca"
        summary="This tracks Orca screen reader usage specifically"/>
    <variant name="Mac.VoiceOver"
        summary="This tracks VoiceOver screen reader usage specifically"/>
    <variant name="WinJAWS" summary="Windows, specifically JAWS."/>
    <variant name="WinMagnifier"
        summary="Windows, specifically Windows Magnifier."/>
    <variant name="WinNarrator" summary="Windows, specifically Narrator."/>
    <variant name="WinNVDA" summary="Windows, specifically NVDA"/>
    <variant name="WinSupernova" summary="Windows, specifically Supernova."/>
    <variant name="WinZDSR" summary="Windows, specifically ZDSR (Zhengdu)"/>
  </token>
</histogram>

<histogram name="Accessibility.{ServiceName}.Service.CrashCountBeforeResume"
    units="count" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the number of {ServiceName} service crashes before one successful
    shutdown. It is recorded when the service shuts down due to being idle, and
    had crashed before and restarted.
  </summary>
  <token key="ServiceName" variants="ScreenAIServices"/>
</histogram>

<histogram name="Accessibility.{ServiceName}.Service.Initialization"
    enum="BooleanSuccess" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records if launching the {ServiceName} service was successful or not.
  </summary>
  <token key="ServiceName" variants="ScreenAIServices"/>
</histogram>

<histogram
    name="Accessibility.{ServiceName}.Service.InitializationTime.{Result}"
    units="ms" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records how long it took for the {ServiceName} service to {Result}.
  </summary>
  <token key="Result">
    <variant name="Failure" summary="fail initialization"/>
    <variant name="Success" summary="initialize successfully"/>
  </token>
  <token key="ServiceName" variants="ScreenAIServices"/>
</histogram>

<histogram name="Accessibility.{ServiceName}.Service.IsSuspended"
    enum="Boolean" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records if {ServiceName} service was suspended due to crash. Recorded when a
    client asks for service availablity or for connection to the service.
  </summary>
  <token key="ServiceName" variants="ScreenAIServices"/>
</histogram>

<histogram name="Accessibility.{ServiceName}.Service.LifeTime" units="ms"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the duration of {ServiceName} service. The metric is stored after
    the process shuts down or crashes.
  </summary>
  <token key="ServiceName" variants="ScreenAIServices"/>
</histogram>

<histogram name="Accessibility.{ServiceName}.Service.MaxMemoryLoad" units="MB"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the maximum memory usage of {ServiceName} service. The metric is
    stored after the process shuts down or crashes and up to 1GB and only if the
    process runs for more than one second.
  </summary>
  <token key="ServiceName" variants="ScreenAIServices"/>
</histogram>

<histogram name="DomDistiller.AdaBoostModel.NegativeScore" units="count"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the score for the ada boost model for negative scores (scores that
    prevent pages from showing). This model is used to determine if any given
    page is suitable for distillation. Recorded for every page load.
  </summary>
</histogram>

<histogram name="DomDistiller.AdaBoostModel.PositiveScore" units="count"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the score for the ada boost model for positive scores. This model is
    used to determine if any given page is suitable for distillation. Recorded
    for every page load.
  </summary>
</histogram>

<histogram name="DomDistiller.Android.AnyPageSignalWithinTimeout"
    enum="Boolean" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    For all distillation page signals, records if the distillation result made
    it within the magic toolbar button timeout. Recorded when
    ReaderModeActionProvider gets a distillation result.
  </summary>
</histogram>

<histogram name="DomDistiller.Android.DistillablePageSignalWithinTimeout"
    enum="Boolean" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    For distillable pages, records if the distillation result made it within the
    magic toolbar button timeout. Recorded when ReaderModeActionProvider gets a
    distillation result.
  </summary>
</histogram>

<histogram
    name="DomDistiller.Android.OnDistillableResult.AccessibilitySettingEnabled"
    enum="Boolean" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records whether the reader mode accessibility setting is enabled. Recorded
    when ReaderModeManager gets a distillation result.
  </summary>
</histogram>

<histogram
    name="DomDistiller.Android.OnDistillableResult.PageDistillationResult"
    enum="DistillationResult" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the end-state of the distillation result. This is different from the
    triggering model result because it also includes android business logic.
    Recorded when ReaderModeManager gets a distillation result.
  </summary>
</histogram>

<histogram name="DomDistiller.Android.ReaderModeEnabledInAccessibilitySettings"
    enum="Boolean" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Tracks when the user interacts with the reader mode toggle in accessibility
    settings.
  </summary>
</histogram>

<histogram name="DomDistiller.IsDistillable" enum="Boolean"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the end result when determining if given page is distillable.
    Recorded for every page load.
  </summary>
</histogram>

<histogram name="DomDistiller.LongModel.NegativeScore" units="count"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the score for the ada boost model for negative scores (scores that
    prevent pages from showing). This model is used to determine if any given
    page is suitable for distillation. Recorded for every page load.
  </summary>
</histogram>

<histogram name="DomDistiller.LongModel.PositiveScore" units="count"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the score for the ada boost model for positive scores. This model is
    used to determine if any given page is suitable for distillation. Recorded
    for every page load.
  </summary>
</histogram>

<histogram name="DomDistiller.ReaderMode.EntryPoint"
    enum="ReaderModeEntryPoint" expires_after="2023-11-30">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>The method a user used to enter reader mode on an article.</summary>
</histogram>

<histogram name="DomDistiller.ReaderMode.ExitPoint" enum="ReaderModeEntryPoint"
    expires_after="2023-11-30">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    The method a user used to exit reader mode and return to the original
    article.
  </summary>
</histogram>

<histogram name="DomDistiller.ReaderShownForPageLoad" enum="Boolean"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records if the panel became visible at any point after a page was navigated.
  </summary>
</histogram>

<histogram name="DomDistiller.Time.RunDistillationJavaScript" units="ms"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    The amount of time the distiller script spent running on the page. This is
    includes loading the script onto the web page. Recorded after the user has
    chosen to view a simplified version of the current page.
  </summary>
</histogram>

<histogram name="DomDistiller.Time.TimeToProvideResultToAccumulator" units="ms"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the amount of time it takes for the reader mode signal to make it to
    SignalAccumulator. Recorded when the distillation result is passed to CPA.
    Only recorded on Android.
  </summary>
</histogram>

<histogram name="DomDistiller.Time.ViewingReaderModePage" units="ms"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the amount of time a user spent on a Reader Mode Page. Recorded when
    the user exits reader mode. Only recorded on Android.
  </summary>
</histogram>

<histogram name="DomDistiller.WordCount" units="count"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the word count for the distilled page. Recorded when the page
    distillation result is received. Recorded per-page even if the article has
    multiple pages (e.g. a continue reading button).
  </summary>
</histogram>

<histogram name="PumpkinInstaller.InstallationSuccess" enum="BooleanSuccess"
    expires_after="2025-05-04">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Pumpkin installation is triggered when one of the features using Pumpkin
    (currently the only feature using Pumpkin is Dictation) are enabled. This
    histogram is recorded once when Pumpkin installation finishes with either
    success or failure. Warning: this histogram was expired 2023-11-30 to
    2024-02-29, data might be missing.
  </summary>
</histogram>

<histogram name="SodaInstaller.BinaryInstallationResult" enum="BooleanSuccess"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Soda Installation is triggered when one of the features using SODA (e.g.
    Live Caption, Projector, Offline Dictation) are enabled. Records whether
    SODA binary installation succeeded. This is recorded once when SODA
    installation attempt finishes with either success or failure.
  </summary>
</histogram>

<histogram name="SodaInstaller.BinaryInstallation{Result}Time" units="ms"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Soda Installation is triggered when one of the features using SODA (e.g.
    Live Caption, Projector, Offline Dictation) are enabled. Records the time
    taken for the binary installation to complete with {Result}, recorded at the
    end of the attempt.
  </summary>
  <token key="Result">
    <variant name="Failure"/>
    <variant name="Success"/>
  </token>
</histogram>

<histogram name="SodaInstaller.Language.{SodaLanguageCode}.InstallationResult"
    enum="BooleanSuccess" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Soda Installation is triggered when one of the features using SODA (e.g.
    Live Caption, Projector, Offline Dictation) are enabled. Records whether
    SODA language pack installation succeeded or not. This is recorded once when
    SODA language pack installation attempt finishes with either success or
    failure.
  </summary>
  <token key="SodaLanguageCode" variants="SodaLanguageCode"/>
</histogram>

<histogram
    name="SodaInstaller.Language.{SodaLanguageCode}.Installation{Result}Time"
    units="ms" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Soda Installation is triggered when one of the features using SODA (e.g.
    Live Caption, Projector, Offline Dictation) are enabled. Records the time
    taken for the SODA language pack installation for {SodaLanguageCode} to
    complete with {Result}, recorded at the end of the attempt.
  </summary>
  <token key="SodaLanguageCode" variants="SodaLanguageCode"/>
  <token key="Result">
    <variant name="Failure"/>
    <variant name="Success"/>
  </token>
</histogram>

<histogram name="TextToSpeech.Event" enum="TextToSpeechEvent"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Events fired by the text-to-speech engine when speaking an utterance.
    Warning: this histogram was expired 2023-11-30 to 2024-02-29, data might be
    missing.
  </summary>
</histogram>

<histogram name="TextToSpeech.ExtensionNetworkSpeechSynthesis.Playback"
    enum="AudioPlayback" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Counts when playback starts and stops on the audio element via the
    canplaythrough and ended events.
  </summary>
</histogram>

<histogram name="TextToSpeech.Settings.GetVoiceBytes"
    enum="TextToSpeechGetVoiceBytes" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    When the user requests a download of a text-to-speech voice, records the
    success/failure result, and service used to download it. Warning: this
    histogram was expired 2023-11-30 to 2024-02-29, data might be missing.
  </summary>
</histogram>

<histogram name="TextToSpeech.Utterance.FromExtensionAPI"
    enum="TextToSpeechFromExtensionAPI" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Indicates if an utterance spoken via synthesized text-to-speech was
    triggered by the Chrome TTS extension API or the web speech API. Warning:
    this histogram was expired 2023-11-30 to 2024-02-29, data might be missing.
  </summary>
</histogram>

<histogram name="TextToSpeech.Utterance.HasVoiceName"
    enum="TextToSpeechHasVoiceName" expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    True if an utterance spoken via synthesized text-to-speech requested a
    specific voice by name. Warning: this histogram was expired 2023-11-30 to
    2024-02-29, data might be missing.
  </summary>
</histogram>

<histogram name="TextToSpeech.Utterance.Native" enum="TextToSpeechNative"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    True if an utterance is spoken with native speech provided by the operating
    system, otherwise it's spoken via a Chrome extension that implements
    text-to-speech support. Warning: this histogram was expired 2023-11-30 to
    2024-02-29, data might be missing.
  </summary>
</histogram>

<histogram name="TextToSpeech.Utterance.Rate" units="count"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    The rate of an utterance to be spoken via synthesized text-to-speech.
    Warning: this histogram was expired 2023-11-30 to 2024-02-29, data might be
    missing.
  </summary>
</histogram>

<histogram name="TextToSpeech.Utterance.Source" enum="TextToSpeechSource"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    ChromeOS source that triggered text-to-speech utterance via extension API,
    from a few known accessibility clients (Select-to-speak, ChromeVox).
    Warning: this histogram was expired 2023-11-30 to 2024-02-29, data might be
    missing.
  </summary>
</histogram>

<histogram name="TextToSpeech.Utterance.TextLength" units="bytes"
    expires_after="2026-06-01">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    The length of an utterance to be spoken via synthesized text-to-speech.
    Warning: this histogram was expired 2023-11-30 to 2024-02-29, data might be
    missing.
  </summary>
</histogram>

</histograms>

</histogram-configuration>
