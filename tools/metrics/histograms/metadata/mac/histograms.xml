<!--
Copyright 2024 The Chromium Authors
Use of this source code is governed by a BSD-style license that can be
found in the LICENSE file.
-->

<!--
This file is used to generate a comprehensive list of others histograms
along with a detailed description for each histogram.

For best practices on writing histogram descriptions, see
https://chromium.googlesource.com/chromium/src.git/+/HEAD/tools/metrics/histograms/README.md

Please follow the instructions in the OWNERS file in this directory to find a
reviewer. If no OWNERS file exists, please consider signing up at
go/reviewing-metrics (Googlers only), as all subdirectories are expected to
have an OWNERS file. As a last resort you can send the CL to
<EMAIL>.
-->

<histogram-configuration>

<histograms>

<histogram name="Mac.AppClonefileError" enum="MacErrno"
    expires_after="2025-08-03">
  <owner><EMAIL></owner>
  <owner>src/ui/base/cocoa/OWNERS</owner>
  <summary>
    The `errno` from the `clonefile` syscall. This histogram is emitted once at
    browser startup.
  </summary>
</histogram>

<histogram name="Mac.AppCodeSignCloneCount" units="count"
    expires_after="2025-10-05">
  <owner><EMAIL></owner>
  <owner>src/ui/base/cocoa/OWNERS</owner>
  <summary>
    The current count of app clones in the code sign clone temp dir. This
    histogram is emitted once at browser startup.
  </summary>
</histogram>

<histogram name="Mac.AppCodeSignCloneCreationTime" units="ms"
    expires_after="2025-09-14">
  <owner><EMAIL></owner>
  <owner>src/ui/base/cocoa/OWNERS</owner>
  <summary>
    The time it took in milliseconds to create the browser app code sign clone.
    Logged once at browser startup.
  </summary>
</histogram>

<histogram name="Mac.AppCodeSignCloneExists" enum="MacCloneExists"
    expires_after="2025-08-03">
  <owner><EMAIL></owner>
  <owner>src/ui/base/cocoa/OWNERS</owner>
  <summary>
    The existence of the clone on disk. This histogram is emitted up to two
    times. Once, immediately after successful clone creation. Then again, if the
    clone is prematurely removed.
  </summary>
</histogram>

<histogram name="Mac.AppFileSystemType" enum="MacFileSystemType"
    expires_after="2025-09-14">
  <owner><EMAIL></owner>
  <owner>src/ui/base/cocoa/OWNERS</owner>
  <summary>
    The file system type where the running instance of Chrome is installed. This
    histogram is emitted once at browser startup.
  </summary>
</histogram>

<histogram name="Mac.AppHardLinkError" enum="MacErrno"
    expires_after="2025-10-05">
  <owner><EMAIL></owner>
  <owner>src/ui/base/cocoa/OWNERS</owner>
  <summary>
    The `errno` from the `link` syscall. This histogram is emitted once at
    browser startup.
  </summary>
</histogram>

<histogram name="Mac.AppUpgradeCodeSignatureValidationStatus"
    enum="MacSecurityFrameworkOSStatus" expires_after="2025-09-14">
  <owner><EMAIL></owner>
  <owner>src/ui/base/cocoa/OWNERS</owner>
  <summary>
    The dynamic code signature validation status of Chrome with a pending Chrome
    update on disk. A single metric is emitted about an hour after an update to
    the running instance of Chrome has been staged on disk.
  </summary>
</histogram>

<histogram name="Mac.Fullscreen.LocationBarViewScreenY" units="px"
    expires_after="2025-11-02">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the location bar's screen Y coordinate when the omnibox popup is
    shown while the browser is in fullscreen mode.

    This histogram is for debugging crbug.com/365733574, that bubbles are
    misplaced in mac fullscreen.
  </summary>
</histogram>

<histogram name="Mac.Fullscreen.OmniboxPopupTargetScreenY" units="px"
    expires_after="2025-11-02">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the target Y coordinate of the omnibox popup when it is shown while
    the browser is in fullscreen mode.

    This histogram is for debugging crbug.com/365733574, that bubbles are
    misplaced in mac fullscreen.
  </summary>
</histogram>

<histogram name="Mac.Fullscreen.OverlayNSWindowScreenY" units="px"
    expires_after="2025-11-02">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the overlay NSWindow's screen Y coordinate when the omnibox popup is
    shown while the browser is in fullscreen mode.

    This histogram is for debugging crbug.com/365733574, that bubbles are
    misplaced in mac fullscreen.
  </summary>
</histogram>

<histogram name="Mac.Fullscreen.OverlayWidgetScreenY" units="px"
    expires_after="2025-08-31">
  <owner><EMAIL></owner>
  <owner><EMAIL></owner>
  <summary>
    Records the overlay widget's screen Y coordinate when the omnibox popup is
    shown while the browser is in fullscreen mode.

    This histogram is for debugging crbug.com/365733574, that bubbles are
    misplaced in mac fullscreen.
  </summary>
</histogram>

<histogram name="Mac.LaunchApplicationResult" enum="MacLaunchApplicationResult"
    expires_after="2025-01-26">
  <owner><EMAIL></owner>
  <owner>src/base/mac/OWNERS</owner>
  <summary>
    The result of calls to base::mac::LaunchApplication, logged once for every
    invocation of that method.
  </summary>
</histogram>

<histogram name="Mac.ProcessRequirement.CurrentProcessValid"
    enum="BooleanSuccess" expires_after="2025-10-05">
  <owner><EMAIL></owner>
  <owner>src/base/mac/OWNERS</owner>
  <summary>
    The result of validating the current process against a same signing identity
    process requirement. This histogram is emitted once at browser startup.
  </summary>
</histogram>

<histogram name="Mac.ProcessRequirement.FallbackValidationCategory.Result"
    enum="MacSecurityFrameworkOSStatus" expires_after="2025-10-05">
  <owner><EMAIL></owner>
  <owner>src/base/mac/OWNERS</owner>
  <summary>
    The result of retrieving the validation category of the current process via
    the fallback code path. 0 on success, or an`OSStatus` value. This histogram
    is emitted once at browser startup.
  </summary>
</histogram>

<histogram name="Mac.ProcessRequirement.TeamIdentifier.Result" enum="MacErrno"
    expires_after="2025-06-01">
  <owner><EMAIL></owner>
  <owner>src/base/mac/OWNERS</owner>
  <summary>
    The result of retrieving the team identifier of the current process. 0 on
    success, the `errno` value from the `csops` system call otherwise. This
    histogram is emitted once at browser startup.
  </summary>
</histogram>

<histogram name="Mac.ProcessRequirement.Timing.{Operation}" units="ms"
    expires_after="2025-10-05">
  <owner><EMAIL></owner>
  <owner>src/base/mac/OWNERS</owner>
  <summary>
    The time taken to {Operation}. This histogram is emitted once at browser
    startup.
  </summary>
  <token key="Operation">
    <variant name="BuildSameIdentityRequirement"
        summary="build a same identity requirement"/>
    <variant name="ValidateSameIdentity"
        summary="validate the current process against a same identity
                 requirement"/>
  </token>
</histogram>

<histogram name="Mac.ProcessRequirement.ValidationCategory.Result"
    enum="MacErrno" expires_after="2025-10-05">
  <owner><EMAIL></owner>
  <owner>src/base/mac/OWNERS</owner>
  <summary>
    The result of retrieving the validation category of the current process. 0
    on success, the `errno` value from the `csops` system call otherwise. This
    histogram is emitted once at browser startup.
  </summary>
</histogram>

<histogram name="Mac.ProcessRequirement.ValidationRequired"
    enum="BooleanSuccess" expires_after="2025-10-05">
  <owner><EMAIL></owner>
  <owner>src/base/mac/OWNERS</owner>
  <summary>
    Whether ProcessRequirement validation was short-circuited due to the
    requirement not needing code signature validation. This histogram is emitted
    whenever a ProcessRequirement is used to validate a process.
  </summary>
</histogram>

<histogram name="Mac.ProcessRequirement.ValidationResult"
    enum="MacSecurityFrameworkOSStatus" expires_after="2025-10-05">
  <owner><EMAIL></owner>
  <owner>src/base/mac/OWNERS</owner>
  <summary>
    The result of validating a ProcessRequirement. This histogram is emitted
    whenever a ProcessRequirement is used to validate a process.
  </summary>
</histogram>

<histogram name="Mac.ProcessRequirement.{Field}.HasExpectedValue"
    enum="BooleanSuccess" expires_after="2025-10-05">
  <owner><EMAIL></owner>
  <owner>src/base/mac/OWNERS</owner>
  <summary>
    Whether the {Field} retrieved for the current process matched the expected
    value. This will only be emitted if the {Field} was successfully retrieved.
    This histogram is emitted once at browser startup.
  </summary>
  <token key="Field">
    <variant name="FallbackValidationCategory"
        summary="fallback validation category"/>
    <variant name="TeamIdentifier" summary="team identifier"/>
    <variant name="ValidationCategory" summary="validation category"/>
  </token>
</histogram>

</histograms>

</histogram-configuration>
