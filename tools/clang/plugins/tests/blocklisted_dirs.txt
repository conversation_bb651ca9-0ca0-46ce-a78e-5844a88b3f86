/src/chromium/src/myheader.h:2:21: warning: [chromium-style] Overriding method must be marked with 'override' or 'final'.
  virtual void foo();  // Should warn about missing 'override'.
                    ^
                     override
/src/chromium/src/myheader.h:2:3: warning: [chromium-style] 'virtual' will be redundant; 'override' implies 'virtual'.
  virtual void foo();  // Should warn about missing 'override'.
  ^~~~~~~
/src/chrome-breakpad/src/myheader.h:124:21: warning: [chromium-style] Overriding method must be marked with 'override' or 'final'.
  virtual void foo();  // Should warn about missing 'override'.
                    ^
                     override
/src/chrome-breakpad/src/myheader.h:124:3: warning: [chromium-style] 'virtual' will be redundant; 'override' implies 'virtual'.
  virtual void foo();  // Should warn about missing 'override'.
  ^~~~~~~
4 warnings generated.
