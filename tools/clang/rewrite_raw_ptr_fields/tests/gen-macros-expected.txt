==== BEGIN EDITS ====
include-user-header:::gen-macros-actual.cc:::-1:::-1:::base/memory/raw_ptr.h
include-user-header:::gen-macros-actual.cc:::-1:::-1:::base/memory/raw_ref.h
r:::gen-macros-actual.cc:::1957:::13:::raw_ptr<struct event> 
r:::gen-macros-actual.cc:::2026:::14:::raw_ptr<struct event *> 
r:::gen-macros-actual.cc:::2770:::13:::const raw_ref<struct event> 
r:::gen-macros-actual.cc:::2799:::13:::const raw_ref<struct event> 
==== END EDITS ====
==== BEGIN FIELD FILTERS ====
MacroTest1::ptr_field  # macro
MacroTest2::(anonymous struct)::span_field  # macro
MacroTest2::(anonymous struct)::tqe_next  # macro
MacroTest2::(anonymous struct)::tqe_prev  # macro
raw_ref_test::MacroTest1::ref_field  # macro
raw_ref_test::MacroTest2::(anonymous struct)::tqe_next  # macro
raw_ref_test::MacroTest2::(anonymous struct)::tqe_prev  # macro
==== END FIELD FILTERS ====
