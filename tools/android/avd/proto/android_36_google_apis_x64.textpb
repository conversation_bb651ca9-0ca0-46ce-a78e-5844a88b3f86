# Copyright 2024 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

# Configuration for an Android 16 AVD on google_apis on x86_64

emulator_package {
  package_name: "chromium/third_party/android_sdk/public/emulator"
  version: "zbW-DiZAD3fm8BoSXv4L6FYVN79w8r_wMrqWZZ7azxwC"  # 35.4.9 (Stable)
}

system_image_package {
  package_name: "chromium/third_party/android_sdk/public/system-images/android-36/google_apis/x86_64"
  version: "nS1z3ppjuBsshStsL6WRWB4FwUaUN9zjMA7M5baTrxwC"  # (v6) BP22.250325.006
}
system_image_name: "system-images;android-36;google_apis;x86_64"

avd_package {
  package_name: "chromium/third_party/android_sdk/public/avds/android-36/google_apis/x86_64"
  # Created in https://ci.chromium.org/ui/b/8716035643399769969
  # Patched gmscore version 25.07.33 in https://crrev.com/c/6377466
  version: "fBvf28KN-g9UdiFRuimWIY00_hVZodtu0WWgDsFKFT8C"
}
avd_name: "android_36_google_apis_x64"

avd_launch_settings {
  gpu_mode: "swangle_indirect"
}