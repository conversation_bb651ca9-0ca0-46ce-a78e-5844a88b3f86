# Copyright 2025 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

# Configuration for an Android-14 (U, API 34) AVD on google_apis on x86_64

emulator_package {
  package_name: "chromium/third_party/android_sdk/public/emulator"
  version: "abxH5kvB9WO4Xg_EHEiIjAl1rqmM2hQRhU5e4_9AFboC"  # 35.3.11 (Stable)
}

system_image_package {
  package_name: "chromium/third_party/android_sdk/public/system-images/android-34/android-automotive/x86_64"
  version: "yu3GTxX-EiMAdks2nEQxDflUnmmPrO7e_z753zemG9sC"  # r4 (UAA1.250210.001)
}
system_image_name: "system-images;android-34-ext9;android-automotive;x86_64"

avd_package {
  package_name: "chromium/third_party/android_sdk/public/avds/android-34/android-automotive/x86_64"
  # Created in https://ci.chromium.org/ui/b/8718495335733819377
  version: "bVeZZMzmAyZG7mdp1lNkpa1VAM38Tgp_0URIMIwQb9MC"
}
avd_name: "android_34_automotive_x64"

avd_launch_settings {
  gpu_mode: "swangle_indirect"
}