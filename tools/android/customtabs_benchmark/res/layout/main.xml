<?xml version="1.0" encoding="utf-8"?>
<!--
Copyright 2017 The Chromium Authors
Use of this source code is governed by a BSD-style license that can be
found in the LICENSE file.
-->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/activity_main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingBottom="16dp"
    android:paddingStart="16dp"
    android:paddingEnd="16dp"
    android:paddingTop="16dp"
    tools:context="org.chromium.customtabs.test.MainActivity"
    android:orientation="vertical">

    <TextView
        android:text="@string/url_to_load"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/textView3"
        android:labelFor="@+id/url_text" />

    <EditText
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:inputType="textUri"
        android:text="@string/default_url"
        android:ems="10"
        android:id="@+id/url_text" />

    <RadioGroup
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">
        <RadioButton android:id="@+id/radio_chrome"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/chrome"
            android:checked="true" />
        <RadioButton android:id="@+id/radio_webview"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/webview"/>
    </RadioGroup>

    <CheckBox
        android:text="@string/warmup"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/warmup_checkbox" />

    <CheckBox
        android:text="@string/will_load_parallel_url"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/parallel_url_checkbox" />

    <TextView
        android:text="@string/parallel_url_to_load"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/parallel_url_label"
        android:labelFor="@+id/parallel_url_text" />

    <EditText
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:inputType="textUri"
        android:text="@string/default_parallel_url"
        android:ems="10"
        android:id="@+id/parallel_url_text" />

    <Button
        android:text="@string/go"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/go_button" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/textView4" />
</LinearLayout>
