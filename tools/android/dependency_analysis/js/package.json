{"name": "dependency-analysis", "version": "1.1.0", "scripts": {"serve": "webpack-dev-server --mode development", "serve-dist": "http-server ./dist --port 8888", "build": "webpack", "lint": "eslint src/*.js src/vue_components/*.vue", "lint-fix": "eslint --fix src/*.js src/vue_components/*.vue"}, "dependencies": {"@trevoreyre/autocomplete-vue": "^2.2.0", "d3": "^7.7.0", "vue": "^2.6.11", "vue-js-modal": "^2.0.1", "vue-material": "^1.0.0-beta-15"}, "devDependencies": {"copy-webpack-plugin": "^6.0.3", "css-loader": "^3.6.0", "eslint": "^7.2.0", "eslint-config-google": "^0.14.0", "eslint-plugin-jsdoc": "^39.6.4", "eslint-plugin-vue": "^7.0.0-alpha.9", "file-loader": "^6.0.0", "html-webpack-plugin": "^4.3.0", "http-server": "^14.1.1", "sass": "^1.56.1", "sass-loader": "^9.0.3", "vue-loader": "^15.9.3", "vue-style-loader": "^4.1.2", "vue-template-compiler": "^2.6.11", "webpack": "^5.75.0", "webpack-bundle-analyzer": "^4.7.0", "webpack-cli": "^5.0.1", "webpack-dev-server": "^4.11.1"}}