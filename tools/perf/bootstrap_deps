# Copyright 2013 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

# This file specifies dependencies required to bootstrap tools/perf.
# deps are used by chromeos, so please do not remove them: crbug.com/651219.

deps = {
    "src/build/android": "",
    "src/chrome/test/data/image_decoding": "",
    "src/chrome/test/data/perf/canvas_bench": "",
    "src/chrome/test/data/perf/throughput_test_cases": "",
    "src/chrome/test/data/perf/tough_compositor_cases": "",
    "src/chrome/test/data/third_party/spaceport": "",
    "src/components/variations/service": "",
    "src/content/test/data/gpu": "",
    "src/content/test/data/media": "",
    "src/media/test/data": "",
    "src/content/test/gpu": "",
    "src/tools/json_comment_eater": "",
    "src/tools/json_to_struct": "",
    "src/tools/perf/benchmarks": "",
    "src/tools/perf/core": "",
    "src/tools/perf/measurements": "",
    "src/tools/perf/page_sets": "",
    "src/tools/perf/metrics": "",
    "src/tools/perf/profile_creators": "",
    "src/tools/perf/run_benchmark": "",
    "src/tools/variations": "",
    "src/testing": "",
    "src/tools/telemetry": "",
    "src/third_party/blink/perf_tests": "",
    "src/third_party/catapult": "",
    "src/third_party/speedometer": "",
}
