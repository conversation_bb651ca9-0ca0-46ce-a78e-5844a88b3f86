(INFO) 2018-07-06 02:37:03,467 timeout_retry.WaitFor:105  condition '<lambda>' met
(INFO) 2018-07-06 02:37:03,468 timeout_retry.WaitFor:105  condition '<lambda>' met
(WARNING) 2018-07-06 02:37:03,468 desktop_browser_finder.FindAllAvailableBrowsers:274  Chrome build location for mac_x86_64 not found. Browser will be run without Flash.
(INFO) 2018-07-06 02:37:03,638 browser_finder.FindBrowser:123  Chose browser: PossibleDesktopBrowser(type=reference, executable=/b/s/w/ir/third_party/catapult/common/py_utils/py_utils/bin/reference_builds/chrome_stable_mac_x86_64_c8bef32ba36d46afed8fc8483aeb72ce65656986/chrome-mac/Google Chrome.app/Contents/MacOS/Google Chrome, flash=None)
(INFO) 2018-07-06 02:37:03,641 timeout_retry.WaitFor:105  condition '<lambda>' met
(INFO) 2018-07-06 02:37:03,641 timeout_retry.WaitFor:105  condition '<lambda>' met
(WARNING) 2018-07-06 02:37:03,641 desktop_browser_finder.FindAllAvailableBrowsers:274  Chrome build location for mac_x86_64 not found. Browser will be run without Flash.
(INFO) 2018-07-06 02:37:03,807 browser_finder.FindBrowser:123  Chose browser: PossibleDesktopBrowser(type=reference, executable=/b/s/w/ir/third_party/catapult/common/py_utils/py_utils/bin/reference_builds/chrome_stable_mac_x86_64_c8bef32ba36d46afed8fc8483aeb72ce65656986/chrome-mac/Google Chrome.app/Contents/MacOS/Google Chrome, flash=None)
(INFO) 2018-07-06 02:37:03,807 ts_proxy_server.StartServer:68  Tsproxy commandline: ['/b/s/w/ir/.swarming_module_cache/vpython/fe1f6b/bin/python', '/b/s/w/ir/third_party/catapult/telemetry/third_party/tsproxy/tsproxy.py', '--port=0', '--desthost=127.0.0.1']
(INFO) 2018-07-06 02:37:03,893 ts_proxy_server.StartServer:75  TsProxy port: 60978
(INFO) 2018-07-06 02:37:03,893 __init__._StartedForwarding:47  DoNothingForwarder started between 127.0.0.1:60978 and 60978
[ RUN      ] blink_perf.owp_storage/blob-perf-files.html
(INFO) 2018-07-06 02:37:03,918 desktop_browser_backend.Start:239  Starting Chrome [u'/b/s/w/ir/third_party/catapult/common/py_utils/py_utils/bin/reference_builds/chrome_stable_mac_x86_64_c8bef32ba36d46afed8fc8483aeb72ce65656986/chrome-mac/Google Chrome.app/Contents/MacOS/Google Chrome', '--enable-experimental-web-platform-features', '--blob-transport-max-file-size=10240', '--blob-transport-by-file-trigger=307300', '--blob-transport-min-file-size=2048', '--js-flags=--expose_gc', '--blob-transport-shared-memory-max-size=30720', '--autoplay-policy=no-user-gesture-required', '--enable-net-benchmarking', '--metrics-recording-only', '--no-default-browser-check', '--no-first-run', '--enable-gpu-benchmarking', '--deny-permission-prompts', '--autoplay-policy=no-user-gesture-required', '--disable-background-networking', '--disable-component-extensions-with-background-pages', '--disable-default-apps', '--disable-search-geolocation-disclosure', '--proxy-server=socks://localhost:60978', '--ignore-certificate-errors-spki-list=PhrPvGIaAMmd29hj8BCZOq096yj7uMpRNHpn5PDxI6I=', '--remote-debugging-port=0', '--enable-crash-reporter-for-testing', '--disable-component-update', '--window-size=1280,1024', '--user-data-dir=/b/s/w/itBaXesI/tmphpstcp', 'about:blank']
(INFO) 2018-07-06 02:37:04,200 __init__._StartedForwarding:47  DoNothingForwarder started between 127.0.0.1:60979 and 60979
(INFO) 2018-07-06 02:37:04,201 chrome_browser_backend._GetDevToolsClient:118  Got devtools config: ws://127.0.0.1:60979/devtools/browser/edde9bb5-9702-49b3-9ad6-4447c1b0ca73
(INFO) 2018-07-06 02:37:04,434 browser._LogBrowserInfo:99  Browser started (pid=43969).
(INFO) 2018-07-06 02:37:04,434 browser._LogBrowserInfo:102  OS: mac highsierra
(INFO) 2018-07-06 02:37:04,459 browser._LogBrowserInfo:105  Detailed OS version: 10.13.3 17D47
(INFO) 2018-07-06 02:37:04,811 browser._LogBrowserInfo:109  Model: MacBookPro 11.5
(INFO) 2018-07-06 02:37:04,811 browser._LogBrowserInfo:111  Browser command line: /b/s/w/ir/third_party/catapult/common/py_utils/py_utils/bin/reference_builds/chrome_stable_mac_x86_64_c8bef32ba36d46afed8fc8483aeb72ce65656986/chrome-mac/Google Chrome.app/Contents/MacOS/Google Chrome --enable-experimental-web-platform-features --blob-transport-max-file-size=10240 --blob-transport-by-file-trigger=307300 --blob-transport-min-file-size=2048 --js-flags=--expose_gc --blob-transport-shared-memory-max-size=30720 --autoplay-policy=no-user-gesture-required --enable-net-benchmarking --metrics-recording-only --no-default-browser-check --no-first-run --enable-gpu-benchmarking --deny-permission-prompts --autoplay-policy=no-user-gesture-required --disable-background-networking --disable-component-extensions-with-background-pages --disable-default-apps --disable-search-geolocation-disclosure --proxy-server=socks://localhost:60978 --ignore-certificate-errors-spki-list=PhrPvGIaAMmd29hj8BCZOq096yj7uMpRNHpn5PDxI6I= --remote-debugging-port=0 --enable-crash-reporter-for-testing --disable-component-update --window-size=1280,1024 --user-data-dir=/b/s/w/itBaXesI/tmphpstcp --flag-switches-begin --flag-switches-end about:blank
(INFO) 2018-07-06 02:37:04,811 browser._LogBrowserInfo:114  GPU device 0: VENDOR = 0x1002 (ATI), DEVICE = 0x6821
(INFO) 2018-07-06 02:37:04,811 browser._LogBrowserInfo:114  GPU device 1: VENDOR = 0x8086 (Intel), DEVICE = 0xd26
(INFO) 2018-07-06 02:37:04,812 browser._LogBrowserInfo:116  GPU Attributes:
(INFO) 2018-07-06 02:37:04,812 browser._LogBrowserInfo:118    amd_switchable      : True
(INFO) 2018-07-06 02:37:04,812 browser._LogBrowserInfo:118    can_support_threaded_texture_mailbox: False
(INFO) 2018-07-06 02:37:04,812 browser._LogBrowserInfo:118    direct_composition  : False
(INFO) 2018-07-06 02:37:04,812 browser._LogBrowserInfo:118    direct_rendering    : True
(INFO) 2018-07-06 02:37:04,812 browser._LogBrowserInfo:118    driver_date         : 
(INFO) 2018-07-06 02:37:04,812 browser._LogBrowserInfo:118    driver_vendor       : 
(INFO) 2018-07-06 02:37:04,812 browser._LogBrowserInfo:118    driver_version      : 
(INFO) 2018-07-06 02:37:04,812 browser._LogBrowserInfo:118    encrypted_only      : False
(INFO) 2018-07-06 02:37:04,812 browser._LogBrowserInfo:118    gl_extensions       : 
(INFO) 2018-07-06 02:37:04,812 browser._LogBrowserInfo:118    gl_renderer         : 
(INFO) 2018-07-06 02:37:04,812 browser._LogBrowserInfo:118    gl_reset_notification_strategy: 0
(INFO) 2018-07-06 02:37:04,812 browser._LogBrowserInfo:118    gl_vendor           : 
(INFO) 2018-07-06 02:37:04,812 browser._LogBrowserInfo:118    gl_version          : 
(INFO) 2018-07-06 02:37:04,813 browser._LogBrowserInfo:118    gl_ws_extensions    : 
(INFO) 2018-07-06 02:37:04,813 browser._LogBrowserInfo:118    gl_ws_vendor        : 
(INFO) 2018-07-06 02:37:04,813 browser._LogBrowserInfo:118    gl_ws_version       : 
(INFO) 2018-07-06 02:37:04,813 browser._LogBrowserInfo:118    in_process_gpu      : False
(INFO) 2018-07-06 02:37:04,813 browser._LogBrowserInfo:118    initialization_time : 0.029067
(INFO) 2018-07-06 02:37:04,813 browser._LogBrowserInfo:118    jpeg_decode_accelerator_supported: False
(INFO) 2018-07-06 02:37:04,813 browser._LogBrowserInfo:118    max_framerate_denominator: 1
(INFO) 2018-07-06 02:37:04,813 browser._LogBrowserInfo:118    max_framerate_numerator: 30
(INFO) 2018-07-06 02:37:04,813 browser._LogBrowserInfo:118    max_msaa_samples    : 
(INFO) 2018-07-06 02:37:04,813 browser._LogBrowserInfo:118    max_resolution_height: 2160
(INFO) 2018-07-06 02:37:04,813 browser._LogBrowserInfo:118    max_resolution_width: 4096
(INFO) 2018-07-06 02:37:04,813 browser._LogBrowserInfo:118    min_resolution_height: 16
(INFO) 2018-07-06 02:37:04,813 browser._LogBrowserInfo:118    min_resolution_width: 16
(INFO) 2018-07-06 02:37:04,813 browser._LogBrowserInfo:118    optimus             : False
(INFO) 2018-07-06 02:37:04,814 browser._LogBrowserInfo:118    passthrough_cmd_decoder: False
(INFO) 2018-07-06 02:37:04,814 browser._LogBrowserInfo:118    pixel_shader_version: 
(INFO) 2018-07-06 02:37:04,814 browser._LogBrowserInfo:118    process_crash_count : 0
(INFO) 2018-07-06 02:37:04,814 browser._LogBrowserInfo:118    profile             : 3
(INFO) 2018-07-06 02:37:04,814 browser._LogBrowserInfo:118    sandboxed           : True
(INFO) 2018-07-06 02:37:04,814 browser._LogBrowserInfo:118    software_rendering  : False
(INFO) 2018-07-06 02:37:04,814 browser._LogBrowserInfo:118    supports_overlays   : False
(INFO) 2018-07-06 02:37:04,814 browser._LogBrowserInfo:118    vertex_shader_version: 
(INFO) 2018-07-06 02:37:04,814 browser._LogBrowserInfo:118    video_decode_accelerator_flags: 0
(INFO) 2018-07-06 02:37:04,814 browser._LogBrowserInfo:120  Feature Status:
(INFO) 2018-07-06 02:37:04,814 browser._LogBrowserInfo:122    2d_canvas           : enabled
(INFO) 2018-07-06 02:37:04,814 browser._LogBrowserInfo:122    checker_imaging     : disabled_off
(INFO) 2018-07-06 02:37:04,814 browser._LogBrowserInfo:122    flash_3d            : enabled
(INFO) 2018-07-06 02:37:04,815 browser._LogBrowserInfo:122    flash_stage3d       : enabled
(INFO) 2018-07-06 02:37:04,815 browser._LogBrowserInfo:122    flash_stage3d_baseline: enabled
(INFO) 2018-07-06 02:37:04,815 browser._LogBrowserInfo:122    gpu_compositing     : enabled
(INFO) 2018-07-06 02:37:04,815 browser._LogBrowserInfo:122    multiple_raster_threads: enabled_on
(INFO) 2018-07-06 02:37:04,815 browser._LogBrowserInfo:122    native_gpu_memory_buffers: enabled
(INFO) 2018-07-06 02:37:04,815 browser._LogBrowserInfo:122    rasterization       : enabled
(INFO) 2018-07-06 02:37:04,815 browser._LogBrowserInfo:122    surface_synchronization: enabled_on
(INFO) 2018-07-06 02:37:04,815 browser._LogBrowserInfo:122    video_decode        : enabled
(INFO) 2018-07-06 02:37:04,815 browser._LogBrowserInfo:122    webgl               : enabled
(INFO) 2018-07-06 02:37:04,815 browser._LogBrowserInfo:122    webgl2              : enabled
(INFO) 2018-07-06 02:37:04,815 browser._LogBrowserInfo:124  Driver Bug Workarounds:
(INFO) 2018-07-06 02:37:04,815 browser._LogBrowserInfo:126    add_and_true_to_loop_condition
(INFO) 2018-07-06 02:37:04,815 browser._LogBrowserInfo:126    adjust_src_dst_region_for_blitframebuffer
(INFO) 2018-07-06 02:37:04,815 browser._LogBrowserInfo:126    avoid_stencil_buffers
(INFO) 2018-07-06 02:37:04,816 browser._LogBrowserInfo:126    decode_encode_srgb_for_generatemipmap
(INFO) 2018-07-06 02:37:04,816 browser._LogBrowserInfo:126    disable_framebuffer_cmaa
(INFO) 2018-07-06 02:37:04,816 browser._LogBrowserInfo:126    disable_webgl_rgb_multisampling_usage
(INFO) 2018-07-06 02:37:04,816 browser._LogBrowserInfo:126    dont_use_loops_to_initialize_variables
(INFO) 2018-07-06 02:37:04,816 browser._LogBrowserInfo:126    emulate_abs_int_function
(INFO) 2018-07-06 02:37:04,816 browser._LogBrowserInfo:126    get_frag_data_info_bug
(INFO) 2018-07-06 02:37:04,816 browser._LogBrowserInfo:126    init_two_cube_map_levels_before_copyteximage
(INFO) 2018-07-06 02:37:04,816 browser._LogBrowserInfo:126    msaa_is_slow
(INFO) 2018-07-06 02:37:04,816 browser._LogBrowserInfo:126    pack_parameters_workaround_with_pack_buffer
(INFO) 2018-07-06 02:37:04,816 browser._LogBrowserInfo:126    rebind_transform_feedback_before_resume
(INFO) 2018-07-06 02:37:04,816 browser._LogBrowserInfo:126    regenerate_struct_names
(INFO) 2018-07-06 02:37:04,817 browser._LogBrowserInfo:126    remove_invariant_and_centroid_for_essl3
(INFO) 2018-07-06 02:37:04,817 browser._LogBrowserInfo:126    reset_teximage2d_base_level
(INFO) 2018-07-06 02:37:04,817 browser._LogBrowserInfo:126    rewrite_texelfetchoffset_to_texelfetch
(INFO) 2018-07-06 02:37:04,817 browser._LogBrowserInfo:126    scalarize_vec_and_mat_constructor_args
(INFO) 2018-07-06 02:37:04,817 browser._LogBrowserInfo:126    set_zero_level_before_generating_mipmap
(INFO) 2018-07-06 02:37:04,817 browser._LogBrowserInfo:126    unfold_short_circuit_as_ternary_operation
(INFO) 2018-07-06 02:37:04,817 browser._LogBrowserInfo:126    unpack_alignment_workaround_with_unpack_buffer
(INFO) 2018-07-06 02:37:04,817 browser._LogBrowserInfo:126    unpack_image_height_workaround_with_unpack_buffer
(INFO) 2018-07-06 02:37:04,817 browser._LogBrowserInfo:126    use_intermediary_for_copy_texture_image
(INFO) 2018-07-06 02:37:04,817 browser._LogBrowserInfo:126    use_unused_standard_shared_blocks
(INFO) 2018-07-06 02:37:04,831 ts_proxy_server._IssueCommand:97  Issuing command to ts_proxy_server: set rtt 0
(INFO) 2018-07-06 02:37:04,832 ts_proxy_server._IssueCommand:97  Issuing command to ts_proxy_server: set inkbps 0
(INFO) 2018-07-06 02:37:04,832 ts_proxy_server._IssueCommand:97  Issuing command to ts_proxy_server: set outkbps 0
(INFO) 2018-07-06 02:37:04,832 cache_temperature.EnsurePageCacheTemperature:179  PageCacheTemperature: any
(INFO) 2018-07-06 02:37:07,221 chrome_tracing_agent._CreateTraceConfigFile:284  Trace config file string: {"trace_config": {"included_categories": ["Blob", "blink.console"], "record_mode": "record-as-much-as-possible"}}
(INFO) 2018-07-06 02:37:07,225 tracing_backend.StartTracing:137  Start Tracing Request: {'params': {'transferMode': 'ReturnAsStream', 'traceConfig': {'recordMode': 'recordAsMuchAsPossible', 'includedCategories': [u'Blob', 'blink.console']}}, 'method': 'Tracing.start'}
(INFO) 2018-07-06 02:37:07,764 chrome_tracing_agent.RecordClockSyncMarker:180  Chrome version: 3359
(INFO) 2018-07-06 02:37:07,830 trace_data.Serialize:190  Trace sizes in bytes: {'traceEvents': 1224736, 'telemetry': 9095, 'tabIds': 36}
(INFO) 2018-07-06 02:37:12,672 trace_data.Serialize:199  trace2html finished in 4.84 seconds.
(WARNING) 2018-07-06 02:37:12,741 model._CreateImporters:283  No importer found for TraceDataPart("telemetry")
(INFO) 2018-07-06 02:37:12,939 browser.Close:207  Closing browser (pid=43969) ...
(INFO) 2018-07-06 02:37:13,113 browser.Close:220  Browser is closed.
Running 20 times
Ignoring warm-up run (44.70000000583241 ms)
21.399999997811392 ms
20.79999999841675 ms
17.899999998917338 ms
20.700000000942964 ms
18.299999996088445 ms
19.700000004377216 ms
19.69999999710126 ms
15.299999999115244 ms
16.90000000235159 ms
15.299999999115244 ms
17.699999996693805 ms
15.899999998509884 ms
26.799999999639113 ms
14.100000000325963 ms
16.700000000128057 ms
15.899999998509884 ms
15.899999998509884 ms
16.100000000733417 ms
16.800000004877802 ms
22.400000001653098 ms
Description: Benchmark for creating blobs using File transport then reading both synchronously and in parallel.

Time:
values 21.399999997811392, 20.79999999841675, 17.899999998917338, 20.700000000942964, 18.299999996088445, 19.700000004377216, 19.69999999710126, 15.299999999115244, 16.90000000235159, 15.299999999115244, 17.699999996693805, 15.899999998509884, 26.799999999639113, 14.100000000325963, 16.700000000128057, 15.899999998509884, 15.899999998509884, 16.100000000733417, 16.800000004877802, 22.400000001653098 ms
avg 18.21499999969092 ms
median 17.299999999522697 ms
stdev 3.073704262756526 ms
min 14.100000000325963 ms
max 26.799999999639113 ms


CPU times of trace event "BlobRequest::ReadFileItem":
values 0.0000000000, 0.0000000000, 0.0000000000, 0.0000000000, 0.0000000000, 0.0000000000, 0.0000000000, 0.0000000000, 0.0000000000, 0.0000000000, 0.0000000000, 0.0000000000, 0.0000000000, 0.0000000000, 0.0000000000, 0.0000000000, 0.0000000000, 0.0000000000, 0.0000000000, 0.0000000000 ms
avg 0.0000000000 ms

CPU times of trace event "Registry::RegisterBlob":
values 0.7200000000, 0.6040000000, 0.6090000000, 0.5970000000, 0.6520000000, 0.6260000000, 0.6740000000, 1.1260000000, 0.6210000000, 0.6560000000, 0.6570000000, 0.6320000000, 0.6190000000, 0.6040000000, 0.5930000000, 0.9520000000, 0.6460000000, 0.6550000000, 0.6290000000, 0.6710000000 ms
avg 0.6771500000 ms



[       OK ] blink_perf.owp_storage/blob-perf-files.html (9228 ms)
[ RUN      ] blink_perf.owp_storage/blob-perf-ipc.html
(INFO) 2018-07-06 02:37:13,147 desktop_browser_backend.Start:239  Starting Chrome [u'/b/s/w/ir/third_party/catapult/common/py_utils/py_utils/bin/reference_builds/chrome_stable_mac_x86_64_c8bef32ba36d46afed8fc8483aeb72ce65656986/chrome-mac/Google Chrome.app/Contents/MacOS/Google Chrome', '--enable-experimental-web-platform-features', '--blob-transport-max-file-size=10240', '--blob-transport-by-file-trigger=307300', '--blob-transport-min-file-size=2048', '--js-flags=--expose_gc', '--blob-transport-shared-memory-max-size=30720', '--autoplay-policy=no-user-gesture-required', '--enable-net-benchmarking', '--metrics-recording-only', '--no-default-browser-check', '--no-first-run', '--enable-gpu-benchmarking', '--deny-permission-prompts', '--autoplay-policy=no-user-gesture-required', '--disable-background-networking', '--disable-component-extensions-with-background-pages', '--disable-default-apps', '--disable-search-geolocation-disclosure', '--proxy-server=socks://localhost:60978', '--ignore-certificate-errors-spki-list=PhrPvGIaAMmd29hj8BCZOq096yj7uMpRNHpn5PDxI6I=', '--remote-debugging-port=0', '--enable-crash-reporter-for-testing', '--disable-component-update', '--window-size=1280,1024', '--user-data-dir=/b/s/w/itBaXesI/tmpf_lo17', 'about:blank']
(INFO) 2018-07-06 02:37:13,389 __init__._StartedForwarding:47  DoNothingForwarder started between 127.0.0.1:61001 and 61001
(INFO) 2018-07-06 02:37:13,389 chrome_browser_backend._GetDevToolsClient:118  Got devtools config: ws://127.0.0.1:61001/devtools/browser/0bcceacb-fb12-49dc-b8da-701aa18e7db7
(INFO) 2018-07-06 02:37:13,659 browser._LogBrowserInfo:99  Browser started (pid=43989).
(INFO) 2018-07-06 02:37:13,660 browser._LogBrowserInfo:102  OS: mac highsierra
(INFO) 2018-07-06 02:37:13,660 browser._LogBrowserInfo:105  Detailed OS version: 10.13.3 17D47
(INFO) 2018-07-06 02:37:14,030 browser._LogBrowserInfo:109  Model: MacBookPro 11.5
(INFO) 2018-07-06 02:37:14,031 browser._LogBrowserInfo:111  Browser command line: /b/s/w/ir/third_party/catapult/common/py_utils/py_utils/bin/reference_builds/chrome_stable_mac_x86_64_c8bef32ba36d46afed8fc8483aeb72ce65656986/chrome-mac/Google Chrome.app/Contents/MacOS/Google Chrome --enable-experimental-web-platform-features --blob-transport-max-file-size=10240 --blob-transport-by-file-trigger=307300 --blob-transport-min-file-size=2048 --js-flags=--expose_gc --blob-transport-shared-memory-max-size=30720 --autoplay-policy=no-user-gesture-required --enable-net-benchmarking --metrics-recording-only --no-default-browser-check --no-first-run --enable-gpu-benchmarking --deny-permission-prompts --autoplay-policy=no-user-gesture-required --disable-background-networking --disable-component-extensions-with-background-pages --disable-default-apps --disable-search-geolocation-disclosure --proxy-server=socks://localhost:60978 --ignore-certificate-errors-spki-list=PhrPvGIaAMmd29hj8BCZOq096yj7uMpRNHpn5PDxI6I= --remote-debugging-port=0 --enable-crash-reporter-for-testing --disable-component-update --window-size=1280,1024 --user-data-dir=/b/s/w/itBaXesI/tmpf_lo17 --flag-switches-begin --flag-switches-end about:blank
(INFO) 2018-07-06 02:37:14,031 browser._LogBrowserInfo:114  GPU device 0: VENDOR = 0x1002 (ATI), DEVICE = 0x6821
(INFO) 2018-07-06 02:37:14,031 browser._LogBrowserInfo:114  GPU device 1: VENDOR = 0x8086 (Intel), DEVICE = 0xd26
(INFO) 2018-07-06 02:37:14,031 browser._LogBrowserInfo:116  GPU Attributes:
(INFO) 2018-07-06 02:37:14,031 browser._LogBrowserInfo:118    amd_switchable      : True
(INFO) 2018-07-06 02:37:14,031 browser._LogBrowserInfo:118    can_support_threaded_texture_mailbox: False
(INFO) 2018-07-06 02:37:14,031 browser._LogBrowserInfo:118    direct_composition  : False
(INFO) 2018-07-06 02:37:14,031 browser._LogBrowserInfo:118    direct_rendering    : True
(INFO) 2018-07-06 02:37:14,031 browser._LogBrowserInfo:118    driver_date         : 
(INFO) 2018-07-06 02:37:14,031 browser._LogBrowserInfo:118    driver_vendor       : 
(INFO) 2018-07-06 02:37:14,032 browser._LogBrowserInfo:118    driver_version      : 
(INFO) 2018-07-06 02:37:14,032 browser._LogBrowserInfo:118    encrypted_only      : False
(INFO) 2018-07-06 02:37:14,032 browser._LogBrowserInfo:118    gl_extensions       : 
(INFO) 2018-07-06 02:37:14,032 browser._LogBrowserInfo:118    gl_renderer         : 
(INFO) 2018-07-06 02:37:14,032 browser._LogBrowserInfo:118    gl_reset_notification_strategy: 0
(INFO) 2018-07-06 02:37:14,032 browser._LogBrowserInfo:118    gl_vendor           : 
(INFO) 2018-07-06 02:37:14,032 browser._LogBrowserInfo:118    gl_version          : 
(INFO) 2018-07-06 02:37:14,032 browser._LogBrowserInfo:118    gl_ws_extensions    : 
(INFO) 2018-07-06 02:37:14,032 browser._LogBrowserInfo:118    gl_ws_vendor        : 
(INFO) 2018-07-06 02:37:14,032 browser._LogBrowserInfo:118    gl_ws_version       : 
(INFO) 2018-07-06 02:37:14,032 browser._LogBrowserInfo:118    in_process_gpu      : False
(INFO) 2018-07-06 02:37:14,032 browser._LogBrowserInfo:118    initialization_time : 0.029082
(INFO) 2018-07-06 02:37:14,033 browser._LogBrowserInfo:118    jpeg_decode_accelerator_supported: False
(INFO) 2018-07-06 02:37:14,033 browser._LogBrowserInfo:118    max_framerate_denominator: 1
(INFO) 2018-07-06 02:37:14,033 browser._LogBrowserInfo:118    max_framerate_numerator: 30
(INFO) 2018-07-06 02:37:14,033 browser._LogBrowserInfo:118    max_msaa_samples    : 
(INFO) 2018-07-06 02:37:14,033 browser._LogBrowserInfo:118    max_resolution_height: 2160
(INFO) 2018-07-06 02:37:14,033 browser._LogBrowserInfo:118    max_resolution_width: 4096
(INFO) 2018-07-06 02:37:14,033 browser._LogBrowserInfo:118    min_resolution_height: 16
(INFO) 2018-07-06 02:37:14,033 browser._LogBrowserInfo:118    min_resolution_width: 16
(INFO) 2018-07-06 02:37:14,033 browser._LogBrowserInfo:118    optimus             : False
(INFO) 2018-07-06 02:37:14,033 browser._LogBrowserInfo:118    passthrough_cmd_decoder: False
(INFO) 2018-07-06 02:37:14,033 browser._LogBrowserInfo:118    pixel_shader_version: 
(INFO) 2018-07-06 02:37:14,034 browser._LogBrowserInfo:118    process_crash_count : 0
(INFO) 2018-07-06 02:37:14,034 browser._LogBrowserInfo:118    profile             : 3
(INFO) 2018-07-06 02:37:14,034 browser._LogBrowserInfo:118    sandboxed           : True
(INFO) 2018-07-06 02:37:14,034 browser._LogBrowserInfo:118    software_rendering  : False
(INFO) 2018-07-06 02:37:14,034 browser._LogBrowserInfo:118    supports_overlays   : False
(INFO) 2018-07-06 02:37:14,034 browser._LogBrowserInfo:118    vertex_shader_version: 
(INFO) 2018-07-06 02:37:14,034 browser._LogBrowserInfo:118    video_decode_accelerator_flags: 0
(INFO) 2018-07-06 02:37:14,034 browser._LogBrowserInfo:120  Feature Status:
(INFO) 2018-07-06 02:37:14,034 browser._LogBrowserInfo:122    2d_canvas           : enabled
(INFO) 2018-07-06 02:37:14,034 browser._LogBrowserInfo:122    checker_imaging     : disabled_off
(INFO) 2018-07-06 02:37:14,034 browser._LogBrowserInfo:122    flash_3d            : enabled
(INFO) 2018-07-06 02:37:14,034 browser._LogBrowserInfo:122    flash_stage3d       : enabled
(INFO) 2018-07-06 02:37:14,034 browser._LogBrowserInfo:122    flash_stage3d_baseline: enabled
(INFO) 2018-07-06 02:37:14,034 browser._LogBrowserInfo:122    gpu_compositing     : enabled
(INFO) 2018-07-06 02:37:14,035 browser._LogBrowserInfo:122    multiple_raster_threads: enabled_on
(INFO) 2018-07-06 02:37:14,035 browser._LogBrowserInfo:122    native_gpu_memory_buffers: enabled
(INFO) 2018-07-06 02:37:14,035 browser._LogBrowserInfo:122    rasterization       : enabled
(INFO) 2018-07-06 02:37:14,035 browser._LogBrowserInfo:122    surface_synchronization: enabled_on
(INFO) 2018-07-06 02:37:14,035 browser._LogBrowserInfo:122    video_decode        : enabled
(INFO) 2018-07-06 02:37:14,035 browser._LogBrowserInfo:122    webgl               : enabled
(INFO) 2018-07-06 02:37:14,035 browser._LogBrowserInfo:122    webgl2              : enabled
(INFO) 2018-07-06 02:37:14,035 browser._LogBrowserInfo:124  Driver Bug Workarounds:
(INFO) 2018-07-06 02:37:14,035 browser._LogBrowserInfo:126    add_and_true_to_loop_condition
(INFO) 2018-07-06 02:37:14,035 browser._LogBrowserInfo:126    adjust_src_dst_region_for_blitframebuffer
(INFO) 2018-07-06 02:37:14,035 browser._LogBrowserInfo:126    avoid_stencil_buffers
(INFO) 2018-07-06 02:37:14,035 browser._LogBrowserInfo:126    decode_encode_srgb_for_generatemipmap
(INFO) 2018-07-06 02:37:14,035 browser._LogBrowserInfo:126    disable_framebuffer_cmaa
(INFO) 2018-07-06 02:37:14,035 browser._LogBrowserInfo:126    disable_webgl_rgb_multisampling_usage
(INFO) 2018-07-06 02:37:14,036 browser._LogBrowserInfo:126    dont_use_loops_to_initialize_variables
(INFO) 2018-07-06 02:37:14,036 browser._LogBrowserInfo:126    emulate_abs_int_function
(INFO) 2018-07-06 02:37:14,036 browser._LogBrowserInfo:126    get_frag_data_info_bug
(INFO) 2018-07-06 02:37:14,036 browser._LogBrowserInfo:126    init_two_cube_map_levels_before_copyteximage
(INFO) 2018-07-06 02:37:14,036 browser._LogBrowserInfo:126    msaa_is_slow
(INFO) 2018-07-06 02:37:14,036 browser._LogBrowserInfo:126    pack_parameters_workaround_with_pack_buffer
(INFO) 2018-07-06 02:37:14,036 browser._LogBrowserInfo:126    rebind_transform_feedback_before_resume
(INFO) 2018-07-06 02:37:14,036 browser._LogBrowserInfo:126    regenerate_struct_names
(INFO) 2018-07-06 02:37:14,036 browser._LogBrowserInfo:126    remove_invariant_and_centroid_for_essl3
(INFO) 2018-07-06 02:37:14,036 browser._LogBrowserInfo:126    reset_teximage2d_base_level
(INFO) 2018-07-06 02:37:14,037 browser._LogBrowserInfo:126    rewrite_texelfetchoffset_to_texelfetch
(INFO) 2018-07-06 02:37:14,037 browser._LogBrowserInfo:126    scalarize_vec_and_mat_constructor_args
(INFO) 2018-07-06 02:37:14,037 browser._LogBrowserInfo:126    set_zero_level_before_generating_mipmap
(INFO) 2018-07-06 02:37:14,037 browser._LogBrowserInfo:126    unfold_short_circuit_as_ternary_operation
(INFO) 2018-07-06 02:37:14,037 browser._LogBrowserInfo:126    unpack_alignment_workaround_with_unpack_buffer
(INFO) 2018-07-06 02:37:14,037 browser._LogBrowserInfo:126    unpack_image_height_workaround_with_unpack_buffer
(INFO) 2018-07-06 02:37:14,037 browser._LogBrowserInfo:126    use_intermediary_for_copy_texture_image
(INFO) 2018-07-06 02:37:14,037 browser._LogBrowserInfo:126    use_unused_standard_shared_blocks
(INFO) 2018-07-06 02:37:14,048 ts_proxy_server._IssueCommand:97  Issuing command to ts_proxy_server: set rtt 0
(INFO) 2018-07-06 02:37:14,049 ts_proxy_server._IssueCommand:97  Issuing command to ts_proxy_server: set inkbps 0
(INFO) 2018-07-06 02:37:14,049 ts_proxy_server._IssueCommand:97  Issuing command to ts_proxy_server: set outkbps 0
(INFO) 2018-07-06 02:37:14,050 cache_temperature.EnsurePageCacheTemperature:179  PageCacheTemperature: any
(INFO) 2018-07-06 02:37:14,294 chrome_tracing_agent._CreateTraceConfigFile:284  Trace config file string: {"trace_config": {"included_categories": ["Blob", "blink.console"], "record_mode": "record-as-much-as-possible"}}
(INFO) 2018-07-06 02:37:14,297 tracing_backend.StartTracing:137  Start Tracing Request: {'params': {'transferMode': 'ReturnAsStream', 'traceConfig': {'recordMode': 'recordAsMuchAsPossible', 'includedCategories': [u'Blob', 'blink.console']}}, 'method': 'Tracing.start'}
(INFO) 2018-07-06 02:37:15,430 chrome_tracing_agent.RecordClockSyncMarker:180  Chrome version: 3359
(INFO) 2018-07-06 02:37:15,594 trace_data.Serialize:190  Trace sizes in bytes: {'traceEvents': 3287570, 'telemetry': 10710, 'tabIds': 36}
(INFO) 2018-07-06 02:37:20,605 trace_data.Serialize:199  trace2html finished in 5.01 seconds.
(WARNING) 2018-07-06 02:37:20,788 model._CreateImporters:283  No importer found for TraceDataPart("telemetry")
(INFO) 2018-07-06 02:37:21,368 browser.Close:207  Closing browser (pid=43989) ...
(INFO) 2018-07-06 02:37:21,476 browser.Close:220  Browser is closed.
Running 50 times
Ignoring warm-up run (42.099999998754356 ms)
17.099999997299165 ms
17.099999997299165 ms
15.100000004167669 ms
15.500000001338776 ms
14.999999999417923 ms
20.49999999871943 ms
14.600000002246816 ms
14.699999999720603 ms
13.89999999810243 ms
15.899999998509884 ms
14.800000004470348 ms
14.299999995273538 ms
14.600000002246816 ms
14.000000002852175 ms
16.59999999537831 ms
16.29999999568099 ms
14.699999999720603 ms
14.400000000023283 ms
14.100000000325963 ms
15.399999996589031 ms
15.999999995983671 ms
15.100000004167669 ms
13.29999999870779 ms
14.19999999779975 ms
14.19999999779975 ms
14.000000002852175 ms
14.300000002549496 ms
15.500000001338776 ms
17.80000000144355 ms
13.59999999840511 ms
13.89999999810243 ms
14.400000000023283 ms
14.400000000023283 ms
13.89999999810243 ms
15.300000006391201 ms
14.19999999779975 ms
16.60000000265427 ms
14.49999999749707 ms
13.800000000628643 ms
17.399999996996485 ms
14.100000000325963 ms
14.200000005075708 ms
14.100000000325963 ms
14.800000004470348 ms
17.199999994772952 ms
14.100000000325963 ms
13.800000000628643 ms
14.599999994970858 ms
14.19999999779975 ms
14.599999994970858 ms
Description: Benchmark for creating blobs using IPC transport then reading both synchronously and in parallel.

Time:
values 17.099999997299165, 17.099999997299165, 15.100000004167669, 15.500000001338776, 14.999999999417923, 20.49999999871943, 14.600000002246816, 14.699999999720603, 13.89999999810243, 15.899999998509884, 14.800000004470348, 14.299999995273538, 14.600000002246816, 14.000000002852175, 16.59999999537831, 16.29999999568099, 14.699999999720603, 14.400000000023283, 14.100000000325963, 15.399999996589031, 15.999999995983671, 15.100000004167669, 13.29999999870779, 14.19999999779975, 14.19999999779975, 14.000000002852175, 14.300000002549496, 15.500000001338776, 17.80000000144355, 13.59999999840511, 13.89999999810243, 14.400000000023283, 14.400000000023283, 13.89999999810243, 15.300000006391201, 14.19999999779975, 16.60000000265427, 14.49999999749707, 13.800000000628643, 17.399999996996485, 14.100000000325963, 14.200000005075708, 14.100000000325963, 14.800000004470348, 17.199999994772952, 14.100000000325963, 13.800000000628643, 14.599999994970858, 14.19999999779975, 14.599999994970858 ms
avg 15.013999999646332 ms
median 14.599999994970858 ms
stdev 1.3545102279907673 ms
min 13.29999999870779 ms
max 20.49999999871943 ms


CPU times of trace event "BlobReader::ReadBytesItem":
values 0.1670000000, 0.1650000000, 0.1570000000, 0.1500000000, 0.1650000000, 0.2220000000, 0.1530000000, 0.1790000000, 0.1640000000, 0.1650000000, 0.1570000000, 0.1540000000, 0.1480000000, 0.1670000000, 0.1780000000, 0.1640000000, 0.1730000000, 0.1750000000, 0.1520000000, 0.1610000000, 0.1690000000, 0.1510000000, 0.1560000000, 0.1570000000, 0.1600000000, 0.1640000000, 0.1700000000, 0.1680000000, 0.1620000000, 0.1670000000, 0.1650000000, 0.1590000000, 0.1520000000, 0.1550000000, 0.1680000000, 0.1550000000, 0.1630000000, 0.1580000000, 0.1550000000, 0.1820000000, 0.1620000000, 0.1560000000, 0.1660000000, 0.1780000000, 0.1520000000, 0.1580000000, 0.1630000000, 0.1560000000, 0.1550000000, 0.1770000000 ms
avg 0.1635000000 ms

CPU times of trace event "Registry::RegisterBlob":
values 1.5590000000, 1.5020000000, 1.4690000000, 1.2460000000, 1.3560000000, 1.5920000000, 1.2360000000, 1.2770000000, 1.3960000000, 1.7200000000, 1.5350000000, 1.3280000000, 1.4760000000, 1.3180000000, 1.4290000000, 1.7780000000, 1.4470000000, 1.4210000000, 1.3740000000, 1.3040000000, 1.6800000000, 1.1900000000, 1.3810000000, 1.2980000000, 1.3490000000, 1.2310000000, 1.7080000000, 1.2450000000, 1.4440000000, 1.6120000000, 1.3350000000, 1.2940000000, 1.1890000000, 1.2120000000, 1.7290000000, 1.3680000000, 1.5910000000, 1.2560000000, 1.4000000000, 1.5150000000, 1.4500000000, 1.3480000000, 1.4800000000, 1.4150000000, 1.3460000000, 1.6580000000, 1.4520000000, 1.9890000000, 1.2940000000, 1.3200000000 ms
avg 1.4308400000 ms



[       OK ] blink_perf.owp_storage/blob-perf-ipc.html (8364 ms)
[ RUN      ] blink_perf.owp_storage/blob-perf-shm.html
(INFO) 2018-07-06 02:37:21,508 desktop_browser_backend.Start:239  Starting Chrome [u'/b/s/w/ir/third_party/catapult/common/py_utils/py_utils/bin/reference_builds/chrome_stable_mac_x86_64_c8bef32ba36d46afed8fc8483aeb72ce65656986/chrome-mac/Google Chrome.app/Contents/MacOS/Google Chrome', '--enable-experimental-web-platform-features', '--blob-transport-max-file-size=10240', '--blob-transport-by-file-trigger=307300', '--blob-transport-min-file-size=2048', '--js-flags=--expose_gc', '--blob-transport-shared-memory-max-size=30720', '--autoplay-policy=no-user-gesture-required', '--enable-net-benchmarking', '--metrics-recording-only', '--no-default-browser-check', '--no-first-run', '--enable-gpu-benchmarking', '--deny-permission-prompts', '--autoplay-policy=no-user-gesture-required', '--disable-background-networking', '--disable-component-extensions-with-background-pages', '--disable-default-apps', '--disable-search-geolocation-disclosure', '--proxy-server=socks://localhost:60978', '--ignore-certificate-errors-spki-list=PhrPvGIaAMmd29hj8BCZOq096yj7uMpRNHpn5PDxI6I=', '--remote-debugging-port=0', '--enable-crash-reporter-for-testing', '--disable-component-update', '--window-size=1280,1024', '--user-data-dir=/b/s/w/itBaXesI/tmpvWxAia', 'about:blank']
(INFO) 2018-07-06 02:37:21,649 __init__._StartedForwarding:47  DoNothingForwarder started between 127.0.0.1:61020 and 61020
(INFO) 2018-07-06 02:37:21,650 chrome_browser_backend._GetDevToolsClient:118  Got devtools config: ws://127.0.0.1:61020/devtools/browser/91ea8df1-b0d9-4f5e-90b0-b8aee5cd963a
(INFO) 2018-07-06 02:37:22,034 browser._LogBrowserInfo:99  Browser started (pid=44006).
(INFO) 2018-07-06 02:37:22,035 browser._LogBrowserInfo:102  OS: mac highsierra
(INFO) 2018-07-06 02:37:22,035 browser._LogBrowserInfo:105  Detailed OS version: 10.13.3 17D47
(INFO) 2018-07-06 02:37:22,413 browser._LogBrowserInfo:109  Model: MacBookPro 11.5
(INFO) 2018-07-06 02:37:22,413 browser._LogBrowserInfo:111  Browser command line: /b/s/w/ir/third_party/catapult/common/py_utils/py_utils/bin/reference_builds/chrome_stable_mac_x86_64_c8bef32ba36d46afed8fc8483aeb72ce65656986/chrome-mac/Google Chrome.app/Contents/MacOS/Google Chrome --enable-experimental-web-platform-features --blob-transport-max-file-size=10240 --blob-transport-by-file-trigger=307300 --blob-transport-min-file-size=2048 --js-flags=--expose_gc --blob-transport-shared-memory-max-size=30720 --autoplay-policy=no-user-gesture-required --enable-net-benchmarking --metrics-recording-only --no-default-browser-check --no-first-run --enable-gpu-benchmarking --deny-permission-prompts --autoplay-policy=no-user-gesture-required --disable-background-networking --disable-component-extensions-with-background-pages --disable-default-apps --disable-search-geolocation-disclosure --proxy-server=socks://localhost:60978 --ignore-certificate-errors-spki-list=PhrPvGIaAMmd29hj8BCZOq096yj7uMpRNHpn5PDxI6I= --remote-debugging-port=0 --enable-crash-reporter-for-testing --disable-component-update --window-size=1280,1024 --user-data-dir=/b/s/w/itBaXesI/tmpvWxAia --flag-switches-begin --flag-switches-end about:blank
(INFO) 2018-07-06 02:37:22,414 browser._LogBrowserInfo:114  GPU device 0: VENDOR = 0x1002 (ATI), DEVICE = 0x6821
(INFO) 2018-07-06 02:37:22,414 browser._LogBrowserInfo:114  GPU device 1: VENDOR = 0x8086 (Intel), DEVICE = 0xd26
(INFO) 2018-07-06 02:37:22,414 browser._LogBrowserInfo:116  GPU Attributes:
(INFO) 2018-07-06 02:37:22,414 browser._LogBrowserInfo:118    amd_switchable      : True
(INFO) 2018-07-06 02:37:22,414 browser._LogBrowserInfo:118    can_support_threaded_texture_mailbox: False
(INFO) 2018-07-06 02:37:22,414 browser._LogBrowserInfo:118    direct_composition  : False
(INFO) 2018-07-06 02:37:22,414 browser._LogBrowserInfo:118    direct_rendering    : True
(INFO) 2018-07-06 02:37:22,414 browser._LogBrowserInfo:118    driver_date         : 
(INFO) 2018-07-06 02:37:22,414 browser._LogBrowserInfo:118    driver_vendor       : 
(INFO) 2018-07-06 02:37:22,414 browser._LogBrowserInfo:118    driver_version      : 
(INFO) 2018-07-06 02:37:22,414 browser._LogBrowserInfo:118    encrypted_only      : False
(INFO) 2018-07-06 02:37:22,414 browser._LogBrowserInfo:118    gl_extensions       : 
(INFO) 2018-07-06 02:37:22,414 browser._LogBrowserInfo:118    gl_renderer         : 
(INFO) 2018-07-06 02:37:22,414 browser._LogBrowserInfo:118    gl_reset_notification_strategy: 0
(INFO) 2018-07-06 02:37:22,415 browser._LogBrowserInfo:118    gl_vendor           : 
(INFO) 2018-07-06 02:37:22,415 browser._LogBrowserInfo:118    gl_version          : 
(INFO) 2018-07-06 02:37:22,415 browser._LogBrowserInfo:118    gl_ws_extensions    : 
(INFO) 2018-07-06 02:37:22,415 browser._LogBrowserInfo:118    gl_ws_vendor        : 
(INFO) 2018-07-06 02:37:22,415 browser._LogBrowserInfo:118    gl_ws_version       : 
(INFO) 2018-07-06 02:37:22,415 browser._LogBrowserInfo:118    in_process_gpu      : False
(INFO) 2018-07-06 02:37:22,415 browser._LogBrowserInfo:118    initialization_time : 0.027028
(INFO) 2018-07-06 02:37:22,415 browser._LogBrowserInfo:118    jpeg_decode_accelerator_supported: False
(INFO) 2018-07-06 02:37:22,415 browser._LogBrowserInfo:118    max_framerate_denominator: 1
(INFO) 2018-07-06 02:37:22,415 browser._LogBrowserInfo:118    max_framerate_numerator: 30
(INFO) 2018-07-06 02:37:22,415 browser._LogBrowserInfo:118    max_msaa_samples    : 
(INFO) 2018-07-06 02:37:22,415 browser._LogBrowserInfo:118    max_resolution_height: 2160
(INFO) 2018-07-06 02:37:22,415 browser._LogBrowserInfo:118    max_resolution_width: 4096
(INFO) 2018-07-06 02:37:22,415 browser._LogBrowserInfo:118    min_resolution_height: 16
(INFO) 2018-07-06 02:37:22,416 browser._LogBrowserInfo:118    min_resolution_width: 16
(INFO) 2018-07-06 02:37:22,416 browser._LogBrowserInfo:118    optimus             : False
(INFO) 2018-07-06 02:37:22,416 browser._LogBrowserInfo:118    passthrough_cmd_decoder: False
(INFO) 2018-07-06 02:37:22,416 browser._LogBrowserInfo:118    pixel_shader_version: 
(INFO) 2018-07-06 02:37:22,416 browser._LogBrowserInfo:118    process_crash_count : 0
(INFO) 2018-07-06 02:37:22,416 browser._LogBrowserInfo:118    profile             : 3
(INFO) 2018-07-06 02:37:22,416 browser._LogBrowserInfo:118    sandboxed           : True
(INFO) 2018-07-06 02:37:22,416 browser._LogBrowserInfo:118    software_rendering  : False
(INFO) 2018-07-06 02:37:22,416 browser._LogBrowserInfo:118    supports_overlays   : False
(INFO) 2018-07-06 02:37:22,416 browser._LogBrowserInfo:118    vertex_shader_version: 
(INFO) 2018-07-06 02:37:22,416 browser._LogBrowserInfo:118    video_decode_accelerator_flags: 0
(INFO) 2018-07-06 02:37:22,416 browser._LogBrowserInfo:120  Feature Status:
(INFO) 2018-07-06 02:37:22,416 browser._LogBrowserInfo:122    2d_canvas           : enabled
(INFO) 2018-07-06 02:37:22,416 browser._LogBrowserInfo:122    checker_imaging     : disabled_off
(INFO) 2018-07-06 02:37:22,417 browser._LogBrowserInfo:122    flash_3d            : enabled
(INFO) 2018-07-06 02:37:22,417 browser._LogBrowserInfo:122    flash_stage3d       : enabled
(INFO) 2018-07-06 02:37:22,417 browser._LogBrowserInfo:122    flash_stage3d_baseline: enabled
(INFO) 2018-07-06 02:37:22,417 browser._LogBrowserInfo:122    gpu_compositing     : enabled
(INFO) 2018-07-06 02:37:22,417 browser._LogBrowserInfo:122    multiple_raster_threads: enabled_on
(INFO) 2018-07-06 02:37:22,417 browser._LogBrowserInfo:122    native_gpu_memory_buffers: enabled
(INFO) 2018-07-06 02:37:22,417 browser._LogBrowserInfo:122    rasterization       : enabled
(INFO) 2018-07-06 02:37:22,417 browser._LogBrowserInfo:122    surface_synchronization: enabled_on
(INFO) 2018-07-06 02:37:22,417 browser._LogBrowserInfo:122    video_decode        : enabled
(INFO) 2018-07-06 02:37:22,417 browser._LogBrowserInfo:122    webgl               : enabled
(INFO) 2018-07-06 02:37:22,417 browser._LogBrowserInfo:122    webgl2              : enabled
(INFO) 2018-07-06 02:37:22,417 browser._LogBrowserInfo:124  Driver Bug Workarounds:
(INFO) 2018-07-06 02:37:22,417 browser._LogBrowserInfo:126    add_and_true_to_loop_condition
(INFO) 2018-07-06 02:37:22,418 browser._LogBrowserInfo:126    adjust_src_dst_region_for_blitframebuffer
(INFO) 2018-07-06 02:37:22,418 browser._LogBrowserInfo:126    avoid_stencil_buffers
(INFO) 2018-07-06 02:37:22,418 browser._LogBrowserInfo:126    decode_encode_srgb_for_generatemipmap
(INFO) 2018-07-06 02:37:22,418 browser._LogBrowserInfo:126    disable_framebuffer_cmaa
(INFO) 2018-07-06 02:37:22,418 browser._LogBrowserInfo:126    disable_webgl_rgb_multisampling_usage
(INFO) 2018-07-06 02:37:22,418 browser._LogBrowserInfo:126    dont_use_loops_to_initialize_variables
(INFO) 2018-07-06 02:37:22,418 browser._LogBrowserInfo:126    emulate_abs_int_function
(INFO) 2018-07-06 02:37:22,418 browser._LogBrowserInfo:126    get_frag_data_info_bug
(INFO) 2018-07-06 02:37:22,418 browser._LogBrowserInfo:126    init_two_cube_map_levels_before_copyteximage
(INFO) 2018-07-06 02:37:22,418 browser._LogBrowserInfo:126    msaa_is_slow
(INFO) 2018-07-06 02:37:22,418 browser._LogBrowserInfo:126    pack_parameters_workaround_with_pack_buffer
(INFO) 2018-07-06 02:37:22,419 browser._LogBrowserInfo:126    rebind_transform_feedback_before_resume
(INFO) 2018-07-06 02:37:22,419 browser._LogBrowserInfo:126    regenerate_struct_names
(INFO) 2018-07-06 02:37:22,419 browser._LogBrowserInfo:126    remove_invariant_and_centroid_for_essl3
(INFO) 2018-07-06 02:37:22,419 browser._LogBrowserInfo:126    reset_teximage2d_base_level
(INFO) 2018-07-06 02:37:22,419 browser._LogBrowserInfo:126    rewrite_texelfetchoffset_to_texelfetch
(INFO) 2018-07-06 02:37:22,419 browser._LogBrowserInfo:126    scalarize_vec_and_mat_constructor_args
(INFO) 2018-07-06 02:37:22,419 browser._LogBrowserInfo:126    set_zero_level_before_generating_mipmap
(INFO) 2018-07-06 02:37:22,419 browser._LogBrowserInfo:126    unfold_short_circuit_as_ternary_operation
(INFO) 2018-07-06 02:37:22,419 browser._LogBrowserInfo:126    unpack_alignment_workaround_with_unpack_buffer
(INFO) 2018-07-06 02:37:22,419 browser._LogBrowserInfo:126    unpack_image_height_workaround_with_unpack_buffer
(INFO) 2018-07-06 02:37:22,419 browser._LogBrowserInfo:126    use_intermediary_for_copy_texture_image
(INFO) 2018-07-06 02:37:22,420 browser._LogBrowserInfo:126    use_unused_standard_shared_blocks
(INFO) 2018-07-06 02:37:22,433 ts_proxy_server._IssueCommand:97  Issuing command to ts_proxy_server: set rtt 0
(INFO) 2018-07-06 02:37:22,434 ts_proxy_server._IssueCommand:97  Issuing command to ts_proxy_server: set inkbps 0
(INFO) 2018-07-06 02:37:22,434 ts_proxy_server._IssueCommand:97  Issuing command to ts_proxy_server: set outkbps 0
(INFO) 2018-07-06 02:37:22,435 cache_temperature.EnsurePageCacheTemperature:179  PageCacheTemperature: any
(INFO) 2018-07-06 02:37:22,670 chrome_tracing_agent._CreateTraceConfigFile:284  Trace config file string: {"trace_config": {"included_categories": ["Blob", "blink.console"], "record_mode": "record-as-much-as-possible"}}
(INFO) 2018-07-06 02:37:22,672 tracing_backend.StartTracing:137  Start Tracing Request: {'params': {'transferMode': 'ReturnAsStream', 'traceConfig': {'recordMode': 'recordAsMuchAsPossible', 'includedCategories': [u'Blob', 'blink.console']}}, 'method': 'Tracing.start'}
(INFO) 2018-07-06 02:37:23,212 chrome_tracing_agent.RecordClockSyncMarker:180  Chrome version: 3359
(INFO) 2018-07-06 02:37:23,281 trace_data.Serialize:190  Trace sizes in bytes: {'traceEvents': 1225246, 'telemetry': 9114, 'tabIds': 36}
(INFO) 2018-07-06 02:37:28,141 trace_data.Serialize:199  trace2html finished in 4.86 seconds.
(WARNING) 2018-07-06 02:37:28,211 model._CreateImporters:283  No importer found for TraceDataPart("telemetry")
(INFO) 2018-07-06 02:37:28,439 browser.Close:207  Closing browser (pid=44006) ...
(INFO) 2018-07-06 02:37:28,612 browser.Close:220  Browser is closed.
Running 20 times
Ignoring warm-up run (47.09999999613501 ms)
19.400000004679896 ms
21.7999999949825 ms
18.800000005285256 ms
22.400000001653098 ms
17.80000000144355 ms
19.59999999962747 ms
19.69999999710126 ms
14.800000004470348 ms
15.800000001036096 ms
15.299999999115244 ms
18.800000005285256 ms
16.90000000235159 ms
23.400000005494803 ms
14.999999999417923 ms
15.600000006088521 ms
15.700000003562309 ms
15.800000001036096 ms
17.600000006495975 ms
15.400000003864989 ms
25.500000003376044 ms
Description: Benchmark for creating blobs using Shared Memory transport then reading both synchronously and in parallel.

Time:
values 19.400000004679896, 21.7999999949825, 18.800000005285256, 22.400000001653098, 17.80000000144355, 19.59999999962747, 19.69999999710126, 14.800000004470348, 15.800000001036096, 15.299999999115244, 18.800000005285256, 16.90000000235159, 23.400000005494803, 14.999999999417923, 15.600000006088521, 15.700000003562309, 15.800000001036096, 17.600000006495975, 15.400000003864989, 25.500000003376044 ms
avg 18.255000002318415 ms
median 17.700000003969762 ms
stdev 3.095238278180864 ms
min 14.800000004470348 ms
max 25.500000003376044 ms


CPU times of trace event "BlobReader::ReadBytesItem":
values 0.8170000000, 0.8480000000, 0.8030000000, 0.8390000000, 0.7910000000, 0.8240000000, 0.7380000000, 0.7810000000, 0.7900000000, 0.7900000000, 0.8250000000, 0.8430000000, 0.7710000000, 0.7420000000, 0.7730000000, 0.7470000000, 0.7970000000, 0.7980000000, 0.8100000000, 0.8470000000 ms
avg 0.7987000000 ms

CPU times of trace event "Registry::RegisterBlob":
values 0.6980000000, 0.6620000000, 0.6590000000, 0.6410000000, 0.6210000000, 0.6910000000, 0.6400000000, 0.8450000000, 0.6590000000, 0.6430000000, 0.9270000000, 0.6550000000, 0.7260000000, 0.6050000000, 0.6820000000, 1.1390000000, 0.7060000000, 0.6720000000, 0.6280000000, 0.6130000000 ms
avg 0.7056000000 ms



[       OK ] blink_perf.owp_storage/blob-perf-shm.html (7140 ms)
[ RUN      ] blink_perf.owp_storage/blob-perf-tiny.html
(INFO) 2018-07-06 02:37:28,650 desktop_browser_backend.Start:239  Starting Chrome [u'/b/s/w/ir/third_party/catapult/common/py_utils/py_utils/bin/reference_builds/chrome_stable_mac_x86_64_c8bef32ba36d46afed8fc8483aeb72ce65656986/chrome-mac/Google Chrome.app/Contents/MacOS/Google Chrome', '--enable-experimental-web-platform-features', '--blob-transport-max-file-size=10240', '--blob-transport-by-file-trigger=307300', '--blob-transport-min-file-size=2048', '--js-flags=--expose_gc', '--blob-transport-shared-memory-max-size=30720', '--autoplay-policy=no-user-gesture-required', '--enable-net-benchmarking', '--metrics-recording-only', '--no-default-browser-check', '--no-first-run', '--enable-gpu-benchmarking', '--deny-permission-prompts', '--autoplay-policy=no-user-gesture-required', '--disable-background-networking', '--disable-component-extensions-with-background-pages', '--disable-default-apps', '--disable-search-geolocation-disclosure', '--proxy-server=socks://localhost:60978', '--ignore-certificate-errors-spki-list=PhrPvGIaAMmd29hj8BCZOq096yj7uMpRNHpn5PDxI6I=', '--remote-debugging-port=0', '--enable-crash-reporter-for-testing', '--disable-component-update', '--window-size=1280,1024', '--user-data-dir=/b/s/w/itBaXesI/tmp08xrD9', 'about:blank']
(INFO) 2018-07-06 02:37:28,816 __init__._StartedForwarding:47  DoNothingForwarder started between 127.0.0.1:61039 and 61039
(INFO) 2018-07-06 02:37:28,817 chrome_browser_backend._GetDevToolsClient:118  Got devtools config: ws://127.0.0.1:61039/devtools/browser/464d676c-53fb-4d15-b7c8-9141abb10bd8
(INFO) 2018-07-06 02:37:29,041 browser._LogBrowserInfo:99  Browser started (pid=44022).
(INFO) 2018-07-06 02:37:29,041 browser._LogBrowserInfo:102  OS: mac highsierra
(INFO) 2018-07-06 02:37:29,041 browser._LogBrowserInfo:105  Detailed OS version: 10.13.3 17D47
(INFO) 2018-07-06 02:37:29,535 browser._LogBrowserInfo:109  Model: MacBookPro 11.5
(INFO) 2018-07-06 02:37:29,535 browser._LogBrowserInfo:111  Browser command line: /b/s/w/ir/third_party/catapult/common/py_utils/py_utils/bin/reference_builds/chrome_stable_mac_x86_64_c8bef32ba36d46afed8fc8483aeb72ce65656986/chrome-mac/Google Chrome.app/Contents/MacOS/Google Chrome --enable-experimental-web-platform-features --blob-transport-max-file-size=10240 --blob-transport-by-file-trigger=307300 --blob-transport-min-file-size=2048 --js-flags=--expose_gc --blob-transport-shared-memory-max-size=30720 --autoplay-policy=no-user-gesture-required --enable-net-benchmarking --metrics-recording-only --no-default-browser-check --no-first-run --enable-gpu-benchmarking --deny-permission-prompts --autoplay-policy=no-user-gesture-required --disable-background-networking --disable-component-extensions-with-background-pages --disable-default-apps --disable-search-geolocation-disclosure --proxy-server=socks://localhost:60978 --ignore-certificate-errors-spki-list=PhrPvGIaAMmd29hj8BCZOq096yj7uMpRNHpn5PDxI6I= --remote-debugging-port=0 --enable-crash-reporter-for-testing --disable-component-update --window-size=1280,1024 --user-data-dir=/b/s/w/itBaXesI/tmp08xrD9 --flag-switches-begin --flag-switches-end about:blank
(INFO) 2018-07-06 02:37:29,535 browser._LogBrowserInfo:114  GPU device 0: VENDOR = 0x1002 (ATI), DEVICE = 0x6821
(INFO) 2018-07-06 02:37:29,535 browser._LogBrowserInfo:114  GPU device 1: VENDOR = 0x8086 (Intel), DEVICE = 0xd26
(INFO) 2018-07-06 02:37:29,535 browser._LogBrowserInfo:116  GPU Attributes:
(INFO) 2018-07-06 02:37:29,536 browser._LogBrowserInfo:118    amd_switchable      : True
(INFO) 2018-07-06 02:37:29,536 browser._LogBrowserInfo:118    can_support_threaded_texture_mailbox: False
(INFO) 2018-07-06 02:37:29,536 browser._LogBrowserInfo:118    direct_composition  : False
(INFO) 2018-07-06 02:37:29,536 browser._LogBrowserInfo:118    direct_rendering    : True
(INFO) 2018-07-06 02:37:29,536 browser._LogBrowserInfo:118    driver_date         : 
(INFO) 2018-07-06 02:37:29,536 browser._LogBrowserInfo:118    driver_vendor       : 
(INFO) 2018-07-06 02:37:29,536 browser._LogBrowserInfo:118    driver_version      : 
(INFO) 2018-07-06 02:37:29,536 browser._LogBrowserInfo:118    encrypted_only      : False
(INFO) 2018-07-06 02:37:29,536 browser._LogBrowserInfo:118    gl_extensions       : 
(INFO) 2018-07-06 02:37:29,536 browser._LogBrowserInfo:118    gl_renderer         : 
(INFO) 2018-07-06 02:37:29,536 browser._LogBrowserInfo:118    gl_reset_notification_strategy: 0
(INFO) 2018-07-06 02:37:29,536 browser._LogBrowserInfo:118    gl_vendor           : 
(INFO) 2018-07-06 02:37:29,536 browser._LogBrowserInfo:118    gl_version          : 
(INFO) 2018-07-06 02:37:29,537 browser._LogBrowserInfo:118    gl_ws_extensions    : 
(INFO) 2018-07-06 02:37:29,537 browser._LogBrowserInfo:118    gl_ws_vendor        : 
(INFO) 2018-07-06 02:37:29,537 browser._LogBrowserInfo:118    gl_ws_version       : 
(INFO) 2018-07-06 02:37:29,537 browser._LogBrowserInfo:118    in_process_gpu      : False
(INFO) 2018-07-06 02:37:29,537 browser._LogBrowserInfo:118    initialization_time : 0.026735
(INFO) 2018-07-06 02:37:29,537 browser._LogBrowserInfo:118    jpeg_decode_accelerator_supported: False
(INFO) 2018-07-06 02:37:29,537 browser._LogBrowserInfo:118    max_framerate_denominator: 1
(INFO) 2018-07-06 02:37:29,537 browser._LogBrowserInfo:118    max_framerate_numerator: 30
(INFO) 2018-07-06 02:37:29,537 browser._LogBrowserInfo:118    max_msaa_samples    : 
(INFO) 2018-07-06 02:37:29,537 browser._LogBrowserInfo:118    max_resolution_height: 2160
(INFO) 2018-07-06 02:37:29,537 browser._LogBrowserInfo:118    max_resolution_width: 4096
(INFO) 2018-07-06 02:37:29,537 browser._LogBrowserInfo:118    min_resolution_height: 16
(INFO) 2018-07-06 02:37:29,537 browser._LogBrowserInfo:118    min_resolution_width: 16
(INFO) 2018-07-06 02:37:29,537 browser._LogBrowserInfo:118    optimus             : False
(INFO) 2018-07-06 02:37:29,538 browser._LogBrowserInfo:118    passthrough_cmd_decoder: False
(INFO) 2018-07-06 02:37:29,538 browser._LogBrowserInfo:118    pixel_shader_version: 
(INFO) 2018-07-06 02:37:29,538 browser._LogBrowserInfo:118    process_crash_count : 0
(INFO) 2018-07-06 02:37:29,538 browser._LogBrowserInfo:118    profile             : 3
(INFO) 2018-07-06 02:37:29,538 browser._LogBrowserInfo:118    sandboxed           : True
(INFO) 2018-07-06 02:37:29,538 browser._LogBrowserInfo:118    software_rendering  : False
(INFO) 2018-07-06 02:37:29,538 browser._LogBrowserInfo:118    supports_overlays   : False
(INFO) 2018-07-06 02:37:29,538 browser._LogBrowserInfo:118    vertex_shader_version: 
(INFO) 2018-07-06 02:37:29,538 browser._LogBrowserInfo:118    video_decode_accelerator_flags: 0
(INFO) 2018-07-06 02:37:29,538 browser._LogBrowserInfo:120  Feature Status:
(INFO) 2018-07-06 02:37:29,538 browser._LogBrowserInfo:122    2d_canvas           : enabled
(INFO) 2018-07-06 02:37:29,538 browser._LogBrowserInfo:122    checker_imaging     : disabled_off
(INFO) 2018-07-06 02:37:29,538 browser._LogBrowserInfo:122    flash_3d            : enabled
(INFO) 2018-07-06 02:37:29,538 browser._LogBrowserInfo:122    flash_stage3d       : enabled
(INFO) 2018-07-06 02:37:29,539 browser._LogBrowserInfo:122    flash_stage3d_baseline: enabled
(INFO) 2018-07-06 02:37:29,539 browser._LogBrowserInfo:122    gpu_compositing     : enabled
(INFO) 2018-07-06 02:37:29,539 browser._LogBrowserInfo:122    multiple_raster_threads: enabled_on
(INFO) 2018-07-06 02:37:29,539 browser._LogBrowserInfo:122    native_gpu_memory_buffers: enabled
(INFO) 2018-07-06 02:37:29,539 browser._LogBrowserInfo:122    rasterization       : enabled
(INFO) 2018-07-06 02:37:29,539 browser._LogBrowserInfo:122    surface_synchronization: enabled_on
(INFO) 2018-07-06 02:37:29,539 browser._LogBrowserInfo:122    video_decode        : enabled
(INFO) 2018-07-06 02:37:29,539 browser._LogBrowserInfo:122    webgl               : enabled
(INFO) 2018-07-06 02:37:29,539 browser._LogBrowserInfo:122    webgl2              : enabled
(INFO) 2018-07-06 02:37:29,539 browser._LogBrowserInfo:124  Driver Bug Workarounds:
(INFO) 2018-07-06 02:37:29,539 browser._LogBrowserInfo:126    add_and_true_to_loop_condition
(INFO) 2018-07-06 02:37:29,539 browser._LogBrowserInfo:126    adjust_src_dst_region_for_blitframebuffer
(INFO) 2018-07-06 02:37:29,539 browser._LogBrowserInfo:126    avoid_stencil_buffers
(INFO) 2018-07-06 02:37:29,539 browser._LogBrowserInfo:126    decode_encode_srgb_for_generatemipmap
(INFO) 2018-07-06 02:37:29,540 browser._LogBrowserInfo:126    disable_framebuffer_cmaa
(INFO) 2018-07-06 02:37:29,540 browser._LogBrowserInfo:126    disable_webgl_rgb_multisampling_usage
(INFO) 2018-07-06 02:37:29,540 browser._LogBrowserInfo:126    dont_use_loops_to_initialize_variables
(INFO) 2018-07-06 02:37:29,540 browser._LogBrowserInfo:126    emulate_abs_int_function
(INFO) 2018-07-06 02:37:29,540 browser._LogBrowserInfo:126    get_frag_data_info_bug
(INFO) 2018-07-06 02:37:29,540 browser._LogBrowserInfo:126    init_two_cube_map_levels_before_copyteximage
(INFO) 2018-07-06 02:37:29,540 browser._LogBrowserInfo:126    msaa_is_slow
(INFO) 2018-07-06 02:37:29,540 browser._LogBrowserInfo:126    pack_parameters_workaround_with_pack_buffer
(INFO) 2018-07-06 02:37:29,540 browser._LogBrowserInfo:126    rebind_transform_feedback_before_resume
(INFO) 2018-07-06 02:37:29,540 browser._LogBrowserInfo:126    regenerate_struct_names
(INFO) 2018-07-06 02:37:29,540 browser._LogBrowserInfo:126    remove_invariant_and_centroid_for_essl3
(INFO) 2018-07-06 02:37:29,540 browser._LogBrowserInfo:126    reset_teximage2d_base_level
(INFO) 2018-07-06 02:37:29,540 browser._LogBrowserInfo:126    rewrite_texelfetchoffset_to_texelfetch
(INFO) 2018-07-06 02:37:29,541 browser._LogBrowserInfo:126    scalarize_vec_and_mat_constructor_args
(INFO) 2018-07-06 02:37:29,541 browser._LogBrowserInfo:126    set_zero_level_before_generating_mipmap
(INFO) 2018-07-06 02:37:29,541 browser._LogBrowserInfo:126    unfold_short_circuit_as_ternary_operation
(INFO) 2018-07-06 02:37:29,541 browser._LogBrowserInfo:126    unpack_alignment_workaround_with_unpack_buffer
(INFO) 2018-07-06 02:37:29,541 browser._LogBrowserInfo:126    unpack_image_height_workaround_with_unpack_buffer
(INFO) 2018-07-06 02:37:29,541 browser._LogBrowserInfo:126    use_intermediary_for_copy_texture_image
(INFO) 2018-07-06 02:37:29,541 browser._LogBrowserInfo:126    use_unused_standard_shared_blocks
(INFO) 2018-07-06 02:37:29,553 ts_proxy_server._IssueCommand:97  Issuing command to ts_proxy_server: set rtt 0
(INFO) 2018-07-06 02:37:29,553 ts_proxy_server._IssueCommand:97  Issuing command to ts_proxy_server: set inkbps 0
(INFO) 2018-07-06 02:37:29,553 ts_proxy_server._IssueCommand:97  Issuing command to ts_proxy_server: set outkbps 0
(INFO) 2018-07-06 02:37:29,554 cache_temperature.EnsurePageCacheTemperature:179  PageCacheTemperature: any
(INFO) 2018-07-06 02:37:29,812 chrome_tracing_agent._CreateTraceConfigFile:284  Trace config file string: {"trace_config": {"included_categories": ["Blob", "blink.console"], "record_mode": "record-as-much-as-possible"}}
(INFO) 2018-07-06 02:37:29,815 tracing_backend.StartTracing:137  Start Tracing Request: {'params': {'transferMode': 'ReturnAsStream', 'traceConfig': {'recordMode': 'recordAsMuchAsPossible', 'includedCategories': [u'Blob', 'blink.console']}}, 'method': 'Tracing.start'}
(INFO) 2018-07-06 02:37:30,909 chrome_tracing_agent.RecordClockSyncMarker:180  Chrome version: 3359
(INFO) 2018-07-06 02:37:31,068 trace_data.Serialize:190  Trace sizes in bytes: {'traceEvents': 3270596, 'telemetry': 10727, 'tabIds': 36}
(INFO) 2018-07-06 02:37:35,986 trace_data.Serialize:199  trace2html finished in 4.92 seconds.
(WARNING) 2018-07-06 02:37:36,215 model._CreateImporters:283  No importer found for TraceDataPart("telemetry")
(INFO) 2018-07-06 02:37:36,776 browser.Close:207  Closing browser (pid=44022) ...
(INFO) 2018-07-06 02:37:36,954 browser.Close:220  Browser is closed.
Running 50 times
Ignoring warm-up run (42.90000000037253 ms)
15.299999999115244 ms
15.999999995983671 ms
14.000000002852175 ms
17.199999994772952 ms
14.999999999417923 ms
15.499999994062819 ms
14.19999999779975 ms
15.600000006088521 ms
13.89999999810243 ms
16.400000000430737 ms
13.699999995878898 ms
14.300000002549496 ms
14.19999999779975 ms
16.20000000548316 ms
14.900000001944136 ms
14.79999999719439 ms
13.900000005378388 ms
13.999999995576218 ms
16.499999997904524 ms
13.799999993352685 ms
15.599999998812564 ms
14.699999999720603 ms
13.800000000628643 ms
14.300000002549496 ms
14.999999999417923 ms
13.89999999810243 ms
13.800000000628643 ms
14.900000001944136 ms
15.399999996589031 ms
14.500000004773028 ms
14.300000002549496 ms
13.999999995576218 ms
15.200000001641456 ms
14.400000000023283 ms
14.300000002549496 ms
13.89999999810243 ms
14.600000002246816 ms
14.600000002246816 ms
14.000000002852175 ms
18.199999998614658 ms
13.800000000628643 ms
14.400000000023283 ms
14.100000000325963 ms
14.100000000325963 ms
14.400000000023283 ms
15.200000001641456 ms
13.800000000628643 ms
16.499999997904524 ms
14.299999995273538 ms
14.000000002852175 ms
Description: Benchmark for creating blobs using IPC transport then reading both synchronously and in parallel.

Time:
values 15.299999999115244, 15.999999995983671, 14.000000002852175, 17.199999994772952, 14.999999999417923, 15.499999994062819, 14.19999999779975, 15.600000006088521, 13.89999999810243, 16.400000000430737, 13.699999995878898, 14.300000002549496, 14.19999999779975, 16.20000000548316, 14.900000001944136, 14.79999999719439, 13.900000005378388, 13.999999995576218, 16.499999997904524, 13.799999993352685, 15.599999998812564, 14.699999999720603, 13.800000000628643, 14.300000002549496, 14.999999999417923, 13.89999999810243, 13.800000000628643, 14.900000001944136, 15.399999996589031, 14.500000004773028, 14.300000002549496, 13.999999995576218, 15.200000001641456, 14.400000000023283, 14.300000002549496, 13.89999999810243, 14.600000002246816, 14.600000002246816, 14.000000002852175, 18.199999998614658, 13.800000000628643, 14.400000000023283, 14.100000000325963, 14.100000000325963, 14.400000000023283, 15.200000001641456, 13.800000000628643, 16.499999997904524, 14.299999995273538, 14.000000002852175 ms
avg 14.747999999817692 ms
median 14.400000000023283 ms
stdev 0.9895865965084867 ms
min 13.699999995878898 ms
max 18.199999998614658 ms


CPU times of trace event "BlobReader::ReadBytesItem":
values 0.1650000000, 0.1600000000, 0.1480000000, 0.2060000000, 0.1800000000, 0.1590000000, 0.1580000000, 0.1470000000, 0.1590000000, 0.1560000000, 0.1520000000, 0.1600000000, 0.1550000000, 0.1550000000, 0.1690000000, 0.1740000000, 0.1510000000, 0.1550000000, 0.1600000000, 0.1530000000, 0.1790000000, 0.1600000000, 0.1490000000, 0.1580000000, 0.1520000000, 0.1530000000, 0.1550000000, 0.1640000000, 0.1820000000, 0.1540000000, 0.1550000000, 0.1540000000, 0.1670000000, 0.1720000000, 0.1720000000, 0.1480000000, 0.1710000000, 0.1520000000, 0.1710000000, 0.1690000000, 0.1540000000, 0.1540000000, 0.1510000000, 0.1770000000, 0.1570000000, 0.1580000000, 0.1750000000, 0.1710000000, 0.1640000000, 0.1550000000 ms
avg 0.1615000000 ms

CPU times of trace event "Registry::RegisterBlob":
values 1.4120000000, 1.3280000000, 1.2170000000, 1.5050000000, 1.3270000000, 1.2830000000, 1.1980000000, 1.2320000000, 1.4260000000, 1.6400000000, 1.2000000000, 1.6590000000, 1.7140000000, 1.3270000000, 1.3020000000, 1.8440000000, 1.1510000000, 1.2220000000, 1.2310000000, 1.2850000000, 1.5760000000, 1.3560000000, 1.4330000000, 1.2110000000, 1.2470000000, 1.2500000000, 1.5700000000, 1.3990000000, 1.2080000000, 1.7040000000, 1.2070000000, 1.6970000000, 1.6220000000, 1.3090000000, 1.1270000000, 1.1930000000, 1.4730000000, 1.3940000000, 1.5850000000, 1.3250000000, 1.1540000000, 1.3210000000, 1.2110000000, 1.4520000000, 1.3520000000, 1.8600000000, 1.1770000000, 1.4640000000, 1.3080000000, 1.3730000000 ms
avg 1.3812200000 ms



[       OK ] blink_perf.owp_storage/blob-perf-tiny.html (8341 ms)
[ RUN      ] blink_perf.owp_storage/idb-load-docs.html
(INFO) 2018-07-06 02:37:36,989 desktop_browser_backend.Start:239  Starting Chrome [u'/b/s/w/ir/third_party/catapult/common/py_utils/py_utils/bin/reference_builds/chrome_stable_mac_x86_64_c8bef32ba36d46afed8fc8483aeb72ce65656986/chrome-mac/Google Chrome.app/Contents/MacOS/Google Chrome', '--enable-experimental-web-platform-features', '--blob-transport-max-file-size=10240', '--blob-transport-by-file-trigger=307300', '--blob-transport-min-file-size=2048', '--js-flags=--expose_gc', '--blob-transport-shared-memory-max-size=30720', '--autoplay-policy=no-user-gesture-required', '--enable-net-benchmarking', '--metrics-recording-only', '--no-default-browser-check', '--no-first-run', '--enable-gpu-benchmarking', '--deny-permission-prompts', '--autoplay-policy=no-user-gesture-required', '--disable-background-networking', '--disable-component-extensions-with-background-pages', '--disable-default-apps', '--disable-search-geolocation-disclosure', '--proxy-server=socks://localhost:60978', '--ignore-certificate-errors-spki-list=PhrPvGIaAMmd29hj8BCZOq096yj7uMpRNHpn5PDxI6I=', '--remote-debugging-port=0', '--enable-crash-reporter-for-testing', '--disable-component-update', '--window-size=1280,1024', '--user-data-dir=/b/s/w/itBaXesI/tmpzvnUjT', 'about:blank']
(INFO) 2018-07-06 02:37:37,158 __init__._StartedForwarding:47  DoNothingForwarder started between 127.0.0.1:61058 and 61058
(INFO) 2018-07-06 02:37:37,158 chrome_browser_backend._GetDevToolsClient:118  Got devtools config: ws://127.0.0.1:61058/devtools/browser/8dab06c7-4c1d-453a-9716-6d45aab9307a
(INFO) 2018-07-06 02:37:37,518 browser._LogBrowserInfo:99  Browser started (pid=44038).
(INFO) 2018-07-06 02:37:37,519 browser._LogBrowserInfo:102  OS: mac highsierra
(INFO) 2018-07-06 02:37:37,519 browser._LogBrowserInfo:105  Detailed OS version: 10.13.3 17D47
(INFO) 2018-07-06 02:37:37,839 browser._LogBrowserInfo:109  Model: MacBookPro 11.5
(INFO) 2018-07-06 02:37:37,840 browser._LogBrowserInfo:111  Browser command line: /b/s/w/ir/third_party/catapult/common/py_utils/py_utils/bin/reference_builds/chrome_stable_mac_x86_64_c8bef32ba36d46afed8fc8483aeb72ce65656986/chrome-mac/Google Chrome.app/Contents/MacOS/Google Chrome --enable-experimental-web-platform-features --blob-transport-max-file-size=10240 --blob-transport-by-file-trigger=307300 --blob-transport-min-file-size=2048 --js-flags=--expose_gc --blob-transport-shared-memory-max-size=30720 --autoplay-policy=no-user-gesture-required --enable-net-benchmarking --metrics-recording-only --no-default-browser-check --no-first-run --enable-gpu-benchmarking --deny-permission-prompts --autoplay-policy=no-user-gesture-required --disable-background-networking --disable-component-extensions-with-background-pages --disable-default-apps --disable-search-geolocation-disclosure --proxy-server=socks://localhost:60978 --ignore-certificate-errors-spki-list=PhrPvGIaAMmd29hj8BCZOq096yj7uMpRNHpn5PDxI6I= --remote-debugging-port=0 --enable-crash-reporter-for-testing --disable-component-update --window-size=1280,1024 --user-data-dir=/b/s/w/itBaXesI/tmpzvnUjT --flag-switches-begin --flag-switches-end about:blank
(INFO) 2018-07-06 02:37:37,840 browser._LogBrowserInfo:114  GPU device 0: VENDOR = 0x1002 (ATI), DEVICE = 0x6821
(INFO) 2018-07-06 02:37:37,840 browser._LogBrowserInfo:114  GPU device 1: VENDOR = 0x8086 (Intel), DEVICE = 0xd26
(INFO) 2018-07-06 02:37:37,840 browser._LogBrowserInfo:116  GPU Attributes:
(INFO) 2018-07-06 02:37:37,840 browser._LogBrowserInfo:118    amd_switchable      : True
(INFO) 2018-07-06 02:37:37,840 browser._LogBrowserInfo:118    can_support_threaded_texture_mailbox: False
(INFO) 2018-07-06 02:37:37,840 browser._LogBrowserInfo:118    direct_composition  : False
(INFO) 2018-07-06 02:37:37,840 browser._LogBrowserInfo:118    direct_rendering    : True
(INFO) 2018-07-06 02:37:37,840 browser._LogBrowserInfo:118    driver_date         : 
(INFO) 2018-07-06 02:37:37,840 browser._LogBrowserInfo:118    driver_vendor       : 
(INFO) 2018-07-06 02:37:37,840 browser._LogBrowserInfo:118    driver_version      : 
(INFO) 2018-07-06 02:37:37,840 browser._LogBrowserInfo:118    encrypted_only      : False
(INFO) 2018-07-06 02:37:37,841 browser._LogBrowserInfo:118    gl_extensions       : 
(INFO) 2018-07-06 02:37:37,841 browser._LogBrowserInfo:118    gl_renderer         : 
(INFO) 2018-07-06 02:37:37,841 browser._LogBrowserInfo:118    gl_reset_notification_strategy: 0
(INFO) 2018-07-06 02:37:37,841 browser._LogBrowserInfo:118    gl_vendor           : 
(INFO) 2018-07-06 02:37:37,841 browser._LogBrowserInfo:118    gl_version          : 
(INFO) 2018-07-06 02:37:37,841 browser._LogBrowserInfo:118    gl_ws_extensions    : 
(INFO) 2018-07-06 02:37:37,841 browser._LogBrowserInfo:118    gl_ws_vendor        : 
(INFO) 2018-07-06 02:37:37,841 browser._LogBrowserInfo:118    gl_ws_version       : 
(INFO) 2018-07-06 02:37:37,841 browser._LogBrowserInfo:118    in_process_gpu      : False
(INFO) 2018-07-06 02:37:37,841 browser._LogBrowserInfo:118    initialization_time : 0.028554
(INFO) 2018-07-06 02:37:37,842 browser._LogBrowserInfo:118    jpeg_decode_accelerator_supported: False
(INFO) 2018-07-06 02:37:37,842 browser._LogBrowserInfo:118    max_framerate_denominator: 1
(INFO) 2018-07-06 02:37:37,842 browser._LogBrowserInfo:118    max_framerate_numerator: 30
(INFO) 2018-07-06 02:37:37,842 browser._LogBrowserInfo:118    max_msaa_samples    : 
(INFO) 2018-07-06 02:37:37,842 browser._LogBrowserInfo:118    max_resolution_height: 2160
(INFO) 2018-07-06 02:37:37,842 browser._LogBrowserInfo:118    max_resolution_width: 4096
(INFO) 2018-07-06 02:37:37,842 browser._LogBrowserInfo:118    min_resolution_height: 16
(INFO) 2018-07-06 02:37:37,842 browser._LogBrowserInfo:118    min_resolution_width: 16
(INFO) 2018-07-06 02:37:37,842 browser._LogBrowserInfo:118    optimus             : False
(INFO) 2018-07-06 02:37:37,842 browser._LogBrowserInfo:118    passthrough_cmd_decoder: False
(INFO) 2018-07-06 02:37:37,842 browser._LogBrowserInfo:118    pixel_shader_version: 
(INFO) 2018-07-06 02:37:37,842 browser._LogBrowserInfo:118    process_crash_count : 0
(INFO) 2018-07-06 02:37:37,843 browser._LogBrowserInfo:118    profile             : 3
(INFO) 2018-07-06 02:37:37,843 browser._LogBrowserInfo:118    sandboxed           : True
(INFO) 2018-07-06 02:37:37,843 browser._LogBrowserInfo:118    software_rendering  : False
(INFO) 2018-07-06 02:37:37,843 browser._LogBrowserInfo:118    supports_overlays   : False
(INFO) 2018-07-06 02:37:37,843 browser._LogBrowserInfo:118    vertex_shader_version: 
(INFO) 2018-07-06 02:37:37,843 browser._LogBrowserInfo:118    video_decode_accelerator_flags: 0
(INFO) 2018-07-06 02:37:37,843 browser._LogBrowserInfo:120  Feature Status:
(INFO) 2018-07-06 02:37:37,843 browser._LogBrowserInfo:122    2d_canvas           : enabled
(INFO) 2018-07-06 02:37:37,843 browser._LogBrowserInfo:122    checker_imaging     : disabled_off
(INFO) 2018-07-06 02:37:37,843 browser._LogBrowserInfo:122    flash_3d            : enabled
(INFO) 2018-07-06 02:37:37,843 browser._LogBrowserInfo:122    flash_stage3d       : enabled
(INFO) 2018-07-06 02:37:37,843 browser._LogBrowserInfo:122    flash_stage3d_baseline: enabled
(INFO) 2018-07-06 02:37:37,844 browser._LogBrowserInfo:122    gpu_compositing     : enabled
(INFO) 2018-07-06 02:37:37,844 browser._LogBrowserInfo:122    multiple_raster_threads: enabled_on
(INFO) 2018-07-06 02:37:37,844 browser._LogBrowserInfo:122    native_gpu_memory_buffers: enabled
(INFO) 2018-07-06 02:37:37,844 browser._LogBrowserInfo:122    rasterization       : enabled
(INFO) 2018-07-06 02:37:37,844 browser._LogBrowserInfo:122    surface_synchronization: enabled_on
(INFO) 2018-07-06 02:37:37,844 browser._LogBrowserInfo:122    video_decode        : enabled
(INFO) 2018-07-06 02:37:37,844 browser._LogBrowserInfo:122    webgl               : enabled
(INFO) 2018-07-06 02:37:37,844 browser._LogBrowserInfo:122    webgl2              : enabled
(INFO) 2018-07-06 02:37:37,844 browser._LogBrowserInfo:124  Driver Bug Workarounds:
(INFO) 2018-07-06 02:37:37,844 browser._LogBrowserInfo:126    add_and_true_to_loop_condition
(INFO) 2018-07-06 02:37:37,845 browser._LogBrowserInfo:126    adjust_src_dst_region_for_blitframebuffer
(INFO) 2018-07-06 02:37:37,845 browser._LogBrowserInfo:126    avoid_stencil_buffers
(INFO) 2018-07-06 02:37:37,845 browser._LogBrowserInfo:126    decode_encode_srgb_for_generatemipmap
(INFO) 2018-07-06 02:37:37,845 browser._LogBrowserInfo:126    disable_framebuffer_cmaa
(INFO) 2018-07-06 02:37:37,845 browser._LogBrowserInfo:126    disable_webgl_rgb_multisampling_usage
(INFO) 2018-07-06 02:37:37,845 browser._LogBrowserInfo:126    dont_use_loops_to_initialize_variables
(INFO) 2018-07-06 02:37:37,845 browser._LogBrowserInfo:126    emulate_abs_int_function
(INFO) 2018-07-06 02:37:37,845 browser._LogBrowserInfo:126    get_frag_data_info_bug
(INFO) 2018-07-06 02:37:37,845 browser._LogBrowserInfo:126    init_two_cube_map_levels_before_copyteximage
(INFO) 2018-07-06 02:37:37,846 browser._LogBrowserInfo:126    msaa_is_slow
(INFO) 2018-07-06 02:37:37,846 browser._LogBrowserInfo:126    pack_parameters_workaround_with_pack_buffer
(INFO) 2018-07-06 02:37:37,846 browser._LogBrowserInfo:126    rebind_transform_feedback_before_resume
(INFO) 2018-07-06 02:37:37,846 browser._LogBrowserInfo:126    regenerate_struct_names
(INFO) 2018-07-06 02:37:37,846 browser._LogBrowserInfo:126    remove_invariant_and_centroid_for_essl3
(INFO) 2018-07-06 02:37:37,846 browser._LogBrowserInfo:126    reset_teximage2d_base_level
(INFO) 2018-07-06 02:37:37,846 browser._LogBrowserInfo:126    rewrite_texelfetchoffset_to_texelfetch
(INFO) 2018-07-06 02:37:37,846 browser._LogBrowserInfo:126    scalarize_vec_and_mat_constructor_args
(INFO) 2018-07-06 02:37:37,846 browser._LogBrowserInfo:126    set_zero_level_before_generating_mipmap
(INFO) 2018-07-06 02:37:37,846 browser._LogBrowserInfo:126    unfold_short_circuit_as_ternary_operation
(INFO) 2018-07-06 02:37:37,846 browser._LogBrowserInfo:126    unpack_alignment_workaround_with_unpack_buffer
(INFO) 2018-07-06 02:37:37,846 browser._LogBrowserInfo:126    unpack_image_height_workaround_with_unpack_buffer
(INFO) 2018-07-06 02:37:37,846 browser._LogBrowserInfo:126    use_intermediary_for_copy_texture_image
(INFO) 2018-07-06 02:37:37,847 browser._LogBrowserInfo:126    use_unused_standard_shared_blocks
(INFO) 2018-07-06 02:37:37,855 ts_proxy_server._IssueCommand:97  Issuing command to ts_proxy_server: set rtt 0
(INFO) 2018-07-06 02:37:37,856 ts_proxy_server._IssueCommand:97  Issuing command to ts_proxy_server: set inkbps 0
(INFO) 2018-07-06 02:37:37,856 ts_proxy_server._IssueCommand:97  Issuing command to ts_proxy_server: set outkbps 0
(INFO) 2018-07-06 02:37:37,857 cache_temperature.EnsurePageCacheTemperature:179  PageCacheTemperature: any
(INFO) 2018-07-06 02:37:38,419 chrome_tracing_agent._CreateTraceConfigFile:284  Trace config file string: {"trace_config": {"included_categories": ["IndexedDB", "blink.console"], "record_mode": "record-as-much-as-possible"}}
(INFO) 2018-07-06 02:37:38,422 tracing_backend.StartTracing:137  Start Tracing Request: {'params': {'transferMode': 'ReturnAsStream', 'traceConfig': {'recordMode': 'recordAsMuchAsPossible', 'includedCategories': [u'IndexedDB', 'blink.console']}}, 'method': 'Tracing.start'}
(INFO) 2018-07-06 02:37:39,217 chrome_tracing_agent.RecordClockSyncMarker:180  Chrome version: 3359
(INFO) 2018-07-06 02:37:39,351 trace_data.Serialize:190  Trace sizes in bytes: {'traceEvents': 2958095, 'telemetry': 9913, 'tabIds': 36}
(INFO) 2018-07-06 02:37:44,223 trace_data.Serialize:199  trace2html finished in 4.87 seconds.
(WARNING) 2018-07-06 02:37:44,426 model._CreateImporters:283  No importer found for TraceDataPart("telemetry")
(INFO) 2018-07-06 02:37:45,101 browser.Close:207  Closing browser (pid=44038) ...
(INFO) 2018-07-06 02:37:45,263 browser.Close:220  Browser is closed.
Running 20 times
Ignoring warm-up run (61.09999999898719 ms)
26.499999999941792 ms
29.59999999438878 ms
25.99999999802094 ms
25.400000005902257 ms
25.30000000115251 ms
28.200000000651926 ms
23.799999995389953 ms
28.800000000046566 ms
22.00000000448199 ms
23.300000000745058 ms
23.300000000745058 ms
22.199999999429565 ms
23.700000005192123 ms
22.69999999407446 ms
22.90000000357395 ms
24.300000004586764 ms
29.60000000166474 ms
24.300000004586764 ms
24.70000000175787 ms
27.300000001559965 ms
Description: Benchmark modeling the IndexedDB activity of a Google Docs offline page load.

Time:
values 26.499999999941792, 29.59999999438878, 25.99999999802094, 25.400000005902257, 25.30000000115251, 28.200000000651926, 23.799999995389953, 28.800000000046566, 22.00000000448199, 23.300000000745058, 23.300000000745058, 22.199999999429565, 23.700000005192123, 22.69999999407446, 22.90000000357395, 24.300000004586764, 29.60000000166474, 24.300000004586764, 24.70000000175787, 27.300000001559965 ms
avg 25.195000000894648 ms
median 24.500000003172318 ms
stdev 2.423020080904328 ms
min 22.00000000448199 ms
max 29.60000000166474 ms


CPU times of trace event "IDBObjectStore::put":
values 12.5719999969, 10.5489999875, 11.1089999974, 10.9820000008, 9.6589999944, 12.8980000019, 12.0780000016, 9.4120000079, 9.9600000009, 10.7309999987, 9.6929999962, 9.9210000038, 10.5879999995, 9.2949999943, 10.5600000024, 10.5969999954, 9.4630000070, 10.5119999945, 10.0240000039, 10.5930000022 ms
avg 10.5597999994 ms

CPU times of trace event "IDBObjectStore::delete":
values 3.5839999989, 5.3079999983, 4.3220000044, 4.0180000067, 4.1290000007, 8.1469999999, 5.4820000008, 4.1530000046, 4.7100000009, 4.2030000016, 4.3650000021, 5.0389999971, 4.5510000065, 4.5099999979, 5.9479999989, 4.3570000008, 5.5320000052, 4.6079999954, 4.7060000002, 8.7430000007 ms
avg 5.0207500011 ms

CPU times of trace event "IndexedDBTransaction::lifetime":
values 19.0369999930, 21.8989999965, 17.9129999951, 17.8450000063, 19.0349999964, 22.1990000010, 17.9169999957, 18.8469999954, 16.2170000002, 17.2180000022, 17.6489999965, 16.7389999926, 17.2119999975, 16.6680000052, 17.3610000014, 18.0890000015, 23.0420000032, 17.4129999951, 18.2389999926, 21.9090000018 ms
avg 18.6223999985 ms

CPU times of trace event "IDBFactory::open":
values 1.7829999998, 1.8229999989, 2.9500000030, 1.9950000048, 1.3269999996, 1.2159999982, 1.2270000055, 2.0850000009, 1.4869999960, 1.5549999997, 1.1679999977, 1.1939999983, 1.8950000033, 1.2459999993, 1.2210000008, 1.7129999995, 1.4589999989, 2.1190000027, 1.3620000035, 1.2910000011 ms
avg 1.6058000006 ms

CPU times of trace event "IDBObjectStore::openCursor":
values 3.0300000012, 3.4170000032, 3.6780000031, 2.9460000023, 3.0740000010, 8.1339999959, 4.4789999947, 2.7990000024, 3.9790000021, 3.6039999947, 2.9580000043, 2.6140000001, 2.9380000010, 2.9729999974, 5.2339999974, 2.8729999959, 3.8150000051, 3.5930000022, 3.1000000015, 8.7260000035 ms
avg 3.8982000005 ms

CPU times of trace event "IDBObjectStore::get":
values 14.7709999904, 18.2429999858, 14.7880000025, 15.1700000018, 14.6049999967, 18.3309999928, 14.2770000100, 12.6180000007, 12.8989999890, 14.1359999999, 13.6069999933, 14.0709999874, 14.3730000034, 13.1480000094, 14.5249999985, 14.7829999998, 20.3049999997, 14.3959999979, 14.3770000115, 17.5569999889 ms
avg 15.0489999980 ms



[       OK ] blink_perf.owp_storage/idb-load-docs.html (8318 ms)
(INFO) 2018-07-06 02:37:45,892 cloud_storage.Insert:378  Uploading /b/s/w/itBaXesI/tmpTnBjs0.html to gs://chrome-telemetry-output/blob_perf_files_html_2018-07-06_02-37-03_29126.html
View generated trace files online at https://console.developers.google.com/m/cloudstorage/b/chrome-telemetry-output/o/blob_perf_files_html_2018-07-06_02-37-03_29126.html for story blob-perf-files.html
(INFO) 2018-07-06 02:37:48,014 cloud_storage.Insert:378  Uploading /b/s/w/itBaXesI/tmpJO2bPw.html to gs://chrome-telemetry-output/blob_perf_ipc_html_2018-07-06_02-37-13_2249.html
View generated trace files online at https://console.developers.google.com/m/cloudstorage/b/chrome-telemetry-output/o/blob_perf_ipc_html_2018-07-06_02-37-13_2249.html for story blob-perf-ipc.html
(INFO) 2018-07-06 02:37:50,181 cloud_storage.Insert:378  Uploading /b/s/w/itBaXesI/tmpmfyYZX.html to gs://chrome-telemetry-output/blob_perf_shm_html_2018-07-06_02-37-21_56665.html
View generated trace files online at https://console.developers.google.com/m/cloudstorage/b/chrome-telemetry-output/o/blob_perf_shm_html_2018-07-06_02-37-21_56665.html for story blob-perf-shm.html
(INFO) 2018-07-06 02:37:52,258 cloud_storage.Insert:378  Uploading /b/s/w/itBaXesI/tmp1kCwci.html to gs://chrome-telemetry-output/blob_perf_tiny_html_2018-07-06_02-37-28_63657.html
View generated trace files online at https://console.developers.google.com/m/cloudstorage/b/chrome-telemetry-output/o/blob_perf_tiny_html_2018-07-06_02-37-28_63657.html for story blob-perf-tiny.html
(INFO) 2018-07-06 02:37:54,402 cloud_storage.Insert:378  Uploading /b/s/w/itBaXesI/tmpD9ngCo.html to gs://chrome-telemetry-output/idb_load_docs_html_2018-07-06_02-37-36_67706.html
View generated trace files online at https://console.developers.google.com/m/cloudstorage/b/chrome-telemetry-output/o/idb_load_docs_html_2018-07-06_02-37-36_67706.html for story idb-load-docs.html
(INFO) 2018-07-06 02:37:56,516 cloud_storage.Insert:378  Uploading /b/s/w/itBaXesI/tmpZ6GNAhtelemetry/artifacts/telemetry_test4XDWd9 to gs://chrome-telemetry-output/435f57eb-8100-11e8-b358-787b8ab24218
Uploading logs of page blob-perf-shm.html to https://console.developers.google.com/m/cloudstorage/b/chrome-telemetry-output/o/435f57eb-8100-11e8-b358-787b8ab24218 (1 out of 1)
(INFO) 2018-07-06 02:37:57,952 cloud_storage.Insert:378  Uploading /b/s/w/itBaXesI/tmpZ6GNAhtelemetry/artifacts/telemetry_testPfezu7 to gs://chrome-telemetry-output/443b61fd-8100-11e8-88b8-787b8ab24218
Uploading logs of page idb-load-docs.html to https://console.developers.google.com/m/cloudstorage/b/chrome-telemetry-output/o/443b61fd-8100-11e8-88b8-787b8ab24218 (1 out of 1)
(INFO) 2018-07-06 02:37:59,524 cloud_storage.Insert:378  Uploading /b/s/w/itBaXesI/tmpZ6GNAhtelemetry/artifacts/telemetry_testJH33tC to gs://chrome-telemetry-output/452b491c-8100-11e8-9ac3-787b8ab24218
Uploading logs of page blob-perf-tiny.html to https://console.developers.google.com/m/cloudstorage/b/chrome-telemetry-output/o/452b491c-8100-11e8-9ac3-787b8ab24218 (1 out of 1)
(INFO) 2018-07-06 02:38:01,078 cloud_storage.Insert:378  Uploading /b/s/w/itBaXesI/tmpZ6GNAhtelemetry/artifacts/telemetry_test4kZXy7 to gs://chrome-telemetry-output/46186878-8100-11e8-bd4c-787b8ab24218
Uploading logs of page blob-perf-ipc.html to https://console.developers.google.com/m/cloudstorage/b/chrome-telemetry-output/o/46186878-8100-11e8-bd4c-787b8ab24218 (1 out of 1)
(INFO) 2018-07-06 02:38:02,536 cloud_storage.Insert:378  Uploading /b/s/w/itBaXesI/tmpZ6GNAhtelemetry/artifacts/telemetry_testoSHMpB to gs://chrome-telemetry-output/46f6df4a-8100-11e8-98f8-787b8ab24218
Uploading logs of page blob-perf-files.html to https://console.developers.google.com/m/cloudstorage/b/chrome-telemetry-output/o/46f6df4a-8100-11e8-98f8-787b8ab24218 (1 out of 1)
(INFO) 2018-07-06 02:38:04,000 memory_debug.LogHostMemoryUsage:60  Used 4.3 GiB out of 16.0 GiB memory available.
(INFO) 2018-07-06 02:38:04,000 memory_debug.LogHostMemoryUsage:61  Memory usage of top 10 processes groups
(INFO) 2018-07-06 02:38:04,015 memory_debug.LogHostMemoryUsage:77  - python2.7 - 168.0 MiB - pids: ['41069', '41074', '43956']
(INFO) 2018-07-06 02:38:04,015 memory_debug.LogHostMemoryUsage:77  - Python - 105.6 MiB - pids: ['318', '41063', '41064']
(INFO) 2018-07-06 02:38:04,015 memory_debug.LogHostMemoryUsage:77  - Terminal - 52.0 MiB - pids: ['251']
(INFO) 2018-07-06 02:38:04,015 memory_debug.LogHostMemoryUsage:77  - NotificationCenter - 45.6 MiB - pids: ['287']
(INFO) 2018-07-06 02:38:04,015 memory_debug.LogHostMemoryUsage:77  - Finder - 39.3 MiB - pids: ['256']
(INFO) 2018-07-06 02:38:04,015 memory_debug.LogHostMemoryUsage:77  - python - 37.6 MiB - pids: ['324']
(INFO) 2018-07-06 02:38:04,015 memory_debug.LogHostMemoryUsage:77  - routined - 33.3 MiB - pids: ['308']
(INFO) 2018-07-06 02:38:04,015 memory_debug.LogHostMemoryUsage:77  - suggestd - 33.3 MiB - pids: ['352']
(INFO) 2018-07-06 02:38:04,015 memory_debug.LogHostMemoryUsage:77  - SystemUIServer - 33.2 MiB - pids: ['255']
(INFO) 2018-07-06 02:38:04,015 memory_debug.LogHostMemoryUsage:77  - CalendarAgent - 32.8 MiB - pids: ['298']
(INFO) 2018-07-06 02:38:04,015 memory_debug.LogHostMemoryUsage:78  Current process:
(INFO) 2018-07-06 02:38:04,015 memory_debug._LogProcessInfo:41  128.2 MiB (pid=43956)
[  PASSED  ] 5 tests.

(WARNING) 2018-07-06 02:38:04,020 histogram_set_json_output_formatter.Format:27  Found existing histograms json but failed to parse it.
(WARNING) 2018-07-06 02:38:04,024 histogram_set_json_output_formatter.Format:27  Found existing histograms json but failed to parse it.
(WARNING) 2018-07-06 02:38:04,027 histogram_set_json_output_formatter.Format:27  Found existing histograms json but failed to parse it.
(WARNING) 2018-07-06 02:38:04,031 histogram_set_json_output_formatter.Format:27  Found existing histograms json but failed to parse it.
(WARNING) 2018-07-06 02:38:04,035 histogram_set_json_output_formatter.Format:27  Found existing histograms json but failed to parse it.
(WARNING) 2018-07-06 02:38:04,039 histogram_set_json_output_formatter.Format:27  Found existing histograms json but failed to parse it.
(WARNING) 2018-07-06 02:38:04,117 histogram_set_json_output_formatter.Format:27  Found existing histograms json but failed to parse it.
(WARNING) 2018-07-06 02:38:04,121 histogram_set_json_output_formatter.Format:27  Found existing histograms json but failed to parse it.
(WARNING) 2018-07-06 02:38:04,125 histogram_set_json_output_formatter.Format:27  Found existing histograms json but failed to parse it.
(WARNING) 2018-07-06 02:38:04,129 histogram_set_json_output_formatter.Format:27  Found existing histograms json but failed to parse it.
(WARNING) 2018-07-06 02:38:04,133 histogram_set_json_output_formatter.Format:27  Found existing histograms json but failed to parse it.
(WARNING) 2018-07-06 02:38:04,137 histogram_set_json_output_formatter.Format:27  Found existing histograms json but failed to parse it.
(WARNING) 2018-07-06 02:38:04,140 histogram_set_json_output_formatter.Format:27  Found existing histograms json but failed to parse it.
View result at file:///b/s/w/itBaXesI/tmpZ6GNAhtelemetry/histograms.json
View result at file:///b/s/w/itBaXesI/tmpZ6GNAhtelemetry/histograms.json
View result at file:///b/s/w/itBaXesI/tmpZ6GNAhtelemetry/histograms.json
View result at file:///b/s/w/itBaXesI/tmpZ6GNAhtelemetry/histograms.json
View result at file:///b/s/w/itBaXesI/tmpZ6GNAhtelemetry/histograms.json
View result at file:///b/s/w/itBaXesI/tmpZ6GNAhtelemetry/histograms.json
View result at file:///b/s/w/itBaXesI/tmpZ6GNAhtelemetry/histograms.json
View result at file:///b/s/w/itBaXesI/tmpZ6GNAhtelemetry/histograms.json
View result at file:///b/s/w/itBaXesI/tmpZ6GNAhtelemetry/histograms.json
View result at file:///b/s/w/itBaXesI/tmpZ6GNAhtelemetry/histograms.json
View result at file:///b/s/w/itBaXesI/tmpZ6GNAhtelemetry/histograms.json
View result at file:///b/s/w/itBaXesI/tmpZ6GNAhtelemetry/histograms.json
View result at file:///b/s/w/itBaXesI/tmpZ6GNAhtelemetry/histograms.json
View result at file:///b/s/w/itBaXesI/tmpZ6GNAhtelemetry/histograms.json
View result at file:///b/s/w/itBaXesI/tmpZ6GNAhtelemetry/test-results.json
