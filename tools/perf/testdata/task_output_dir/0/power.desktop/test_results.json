{"tests": {"power.desktop": {"TrivialAnimationPageSharedPageState": {"actual": "PASS", "artifacts": {"logs": ["https://console.developers.google.com/m/cloudstorage/b/chrome-telemetry-output/o/f8c052cc-80fd-11e8-822b-787b8ab24218"]}, "is_unexpected": false, "times": [40.55236101150513], "time": 40.55236101150513, "expected": "PASS"}, "slideshare": {"actual": "PASS", "artifacts": {"logs": ["https://console.developers.google.com/m/cloudstorage/b/chrome-telemetry-output/o/f9a2ec7a-80fd-11e8-ac49-787b8ab24218"]}, "is_unexpected": false, "times": [41.31651782989502], "time": 41.31651782989502, "expected": "PASS"}, "TrivialBlinkingCursorPageSharedPageState": {"actual": "PASS", "artifacts": {"logs": ["https://console.developers.google.com/m/cloudstorage/b/chrome-telemetry-output/o/fa9ad64c-80fd-11e8-8644-787b8ab24218"]}, "is_unexpected": false, "times": [40.061970949172974], "time": 40.061970949172974, "expected": "PASS"}, "instagram": {"actual": "PASS", "artifacts": {"logs": ["https://console.developers.google.com/m/cloudstorage/b/chrome-telemetry-output/o/fb7f167d-80fd-11e8-b921-787b8ab24218"]}, "is_unexpected": false, "times": [41.49246597290039], "time": 41.49246597290039, "expected": "PASS"}, "indiatimes": {"actual": "PASS", "artifacts": {"logs": ["https://console.developers.google.com/m/cloudstorage/b/chrome-telemetry-output/o/fc5ea300-80fd-11e8-820a-787b8ab24218"]}, "is_unexpected": false, "times": [40.19874405860901], "time": 40.19874405860901, "expected": "PASS"}, "TrivialGifPageSharedPageState": {"actual": "PASS", "artifacts": {"logs": ["https://console.developers.google.com/m/cloudstorage/b/chrome-telemetry-output/o/fd3d9da1-80fd-11e8-b67c-787b8ab24218"]}, "is_unexpected": false, "times": [39.772878885269165], "time": 39.772878885269165, "expected": "PASS"}, "TrivialBlurAnimationPageSharedPageState": {"actual": "PASS", "artifacts": {"logs": ["https://console.developers.google.com/m/cloudstorage/b/chrome-telemetry-output/o/fe278991-80fd-11e8-9e2b-787b8ab24218"]}, "is_unexpected": false, "times": [41.0666708946228], "time": 41.0666708946228, "expected": "PASS"}, "TrivialFullscreenVideoPageSharedPageState": {"actual": "PASS", "artifacts": {"logs": ["https://console.developers.google.com/m/cloudstorage/b/chrome-telemetry-output/o/ff1db6dc-80fd-11e8-9bf5-787b8ab24218"]}, "is_unexpected": false, "times": [41.26658582687378], "time": 41.26658582687378, "expected": "PASS"}, "TrivialCanvasPageSharedPageState": {"actual": "PASS", "artifacts": {"logs": ["https://console.developers.google.com/m/cloudstorage/b/chrome-telemetry-output/o/03af231e-80fe-11e8-a013-787b8ab24218"]}, "is_unexpected": false, "times": [40.32404398918152], "time": 40.32404398918152, "expected": "PASS"}, "TrivialScrollingPageSharedPageState": {"actual": "PASS", "artifacts": {"logs": ["https://console.developers.google.com/m/cloudstorage/b/chrome-telemetry-output/o/00e9ea94-80fe-11e8-ab29-787b8ab24218"]}, "is_unexpected": false, "times": [40.30246615409851], "time": 40.30246615409851, "expected": "PASS"}, "uol": {"actual": "PASS", "artifacts": {"logs": ["https://console.developers.google.com/m/cloudstorage/b/chrome-telemetry-output/o/01d7603a-80fe-11e8-9fa6-787b8ab24218"]}, "is_unexpected": false, "times": [50.15763306617737], "time": 50.15763306617737, "expected": "PASS"}, "abcnews": {"actual": "PASS", "artifacts": {"logs": ["https://console.developers.google.com/m/cloudstorage/b/chrome-telemetry-output/o/02be4d9c-80fe-11e8-8b01-787b8ab24218"]}, "is_unexpected": false, "times": [138.0637059211731], "time": 138.0637059211731, "expected": "PASS"}, "TrivialWebGLPageSharedPageState": {"actual": "PASS", "artifacts": {"logs": ["https://console.developers.google.com/m/cloudstorage/b/chrome-telemetry-output/o/00031c7d-80fe-11e8-9f15-787b8ab24218"]}, "is_unexpected": false, "times": [41.260926961898804], "time": 41.260926961898804, "expected": "PASS"}, "microsoft": {"actual": "PASS", "artifacts": {"logs": ["https://console.developers.google.com/m/cloudstorage/b/chrome-telemetry-output/o/049b4078-80fe-11e8-af5a-787b8ab24218"]}, "is_unexpected": false, "times": [40.285988092422485], "time": 40.285988092422485, "expected": "PASS"}, "sina": {"actual": "PASS", "artifacts": {"logs": ["https://console.developers.google.com/m/cloudstorage/b/chrome-telemetry-output/o/057c2c33-80fe-11e8-8411-787b8ab24218"]}, "is_unexpected": false, "times": [42.55037713050842], "time": 42.55037713050842, "expected": "PASS"}}}, "interrupted": false, "num_failures_by_type": {"PASS": 15}, "version": 3, "seconds_since_epoch": 1530868138.709751, "path_delimiter": "/"}