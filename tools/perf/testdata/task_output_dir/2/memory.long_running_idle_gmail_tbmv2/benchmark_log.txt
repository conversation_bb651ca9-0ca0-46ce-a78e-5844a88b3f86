(INFO) 2018-07-06 02:35:30,210 timeout_retry.WaitFor:105  condition '<lambda>' met
(INFO) 2018-07-06 02:35:30,211 timeout_retry.WaitFor:105  condition '<lambda>' met
(WARNING) 2018-07-06 02:35:30,211 desktop_browser_finder.FindAllAvailableBrowsers:274  Chrome build location for mac_x86_64 not found. Browser will be run without Flash.
(INFO) 2018-07-06 02:35:30,212 browser_finder.FindBrowser:123  Chose browser: PossibleDesktopBrowser(type=release, executable=/b/s/w/ir/out/Release/Google Chrome.app/Contents/MacOS/Google Chrome, flash=None)
(INFO) 2018-07-06 02:35:30,220 timeout_retry.WaitFor:105  condition '<lambda>' met
(INFO) 2018-07-06 02:35:30,220 timeout_retry.WaitFor:105  condition '<lambda>' met
(WARNING) 2018-07-06 02:35:30,220 desktop_browser_finder.FindAllAvailableBrowsers:274  Chrome build location for mac_x86_64 not found. Browser will be run without Flash.
(INFO) 2018-07-06 02:35:30,221 browser_finder.FindBrowser:123  Chose browser: PossibleDesktopBrowser(type=release, executable=/b/s/w/ir/out/Release/Google Chrome.app/Contents/MacOS/Google Chrome, flash=None)
(INFO) 2018-07-06 02:35:30,222 archive_info.DownloadArchivesIfNeeded:68  Downloading WPR archives. This can take a long time.
(INFO) 2018-07-06 02:35:30,236 archive_info.DownloadArchivesIfNeeded:110  All WPR archives are downloaded, took 0.0137150287628 seconds.
(INFO) 2018-07-06 02:35:30,237 timeout_retry.WaitFor:105  condition '<lambda>' met
(INFO) 2018-07-06 02:35:30,238 timeout_retry.WaitFor:105  condition '<lambda>' met
(WARNING) 2018-07-06 02:35:30,238 desktop_browser_finder.FindAllAvailableBrowsers:274  Chrome build location for mac_x86_64 not found. Browser will be run without Flash.
(INFO) 2018-07-06 02:35:30,238 browser_finder.FindBrowser:123  Chose browser: PossibleDesktopBrowser(type=release, executable=/b/s/w/ir/out/Release/Google Chrome.app/Contents/MacOS/Google Chrome, flash=None)
(INFO) 2018-07-06 02:35:30,238 ts_proxy_server.StartServer:68  Tsproxy commandline: ['/b/s/w/ir/.swarming_module_cache/vpython/fe1f6b/bin/python', '/b/s/w/ir/third_party/catapult/telemetry/third_party/tsproxy/tsproxy.py', '--port=0', '--desthost=127.0.0.1']
(INFO) 2018-07-06 02:35:30,319 ts_proxy_server.StartServer:75  TsProxy port: 65083
(INFO) 2018-07-06 02:35:30,319 __init__._StartedForwarding:47  DoNothingForwarder started between 127.0.0.1:65083 and 65083
[ RUN      ] memory.long_running_idle_gmail_tbmv2/https://mail.google.com/mail/
(INFO) 2018-07-06 02:35:30,321 chrome_tracing_agent._CreateTraceConfigFile:284  Trace config file string: {"trace_config": {"excluded_categories": ["*"], "included_categories": ["v8", "webkit.console", "renderer.scheduler", "blink.console", "disabled-by-default-memory-infra"], "memory_dump_config": {"triggers": []}, "record_mode": "record-as-much-as-possible"}}
(INFO) 2018-07-06 02:35:30,346 webpagereplay_go_server.StartServer:222  Starting Web-Page-Replay: [u'/b/s/w/ir/third_party/catapult/telemetry/telemetry/internal/bin/mac/x86_64/wpr', 'replay', '--http_port=0', '--https_port=0', '--https_key_file=/b/s/w/ir/third_party/catapult/web_page_replay_go/wpr_key.pem', '--https_cert_file=/b/s/w/ir/third_party/catapult/web_page_replay_go/wpr_cert.pem', '--inject_scripts=/b/s/w/ir/third_party/catapult/web_page_replay_go/deterministic.js', u'/b/s/w/ir/tools/perf/page_sets/data/long_running_idle_gmail_page_000.wprgo']
(INFO) 2018-07-06 02:35:30,599 webpagereplay_go_server.StartServer:232  WPR ports: {'http': 65086, 'https': 65087}
(INFO) 2018-07-06 02:35:30,599 ts_proxy_server._IssueCommand:97  Issuing command to ts_proxy_server: set mapports 443:65087,*:65086
(WARNING) 2018-07-06 02:35:30,607 possible_browser.FlushOsPageCaches:77  Flush system cache is not supported. Did not flush OS page cache.
(INFO) 2018-07-06 02:35:30,628 desktop_browser_backend.Start:239  Starting Chrome ['/b/s/w/ir/out/Release/Google Chrome.app/Contents/MacOS/Google Chrome', u'--force-fieldtrial-params=AccountConsistencyVariations.DiceMigration:method/dice_migration,AudioService.AudioProcess_LaunchOnStartup_v3:teardown_timeout_s/0,AutofillProfileOrderByFrecency.EnabledLimitTo3:limit/3,AutomaticTabDiscarding.Enabled_Once_10-gen2:MinimumProtectionTime/600,BackgroundVideoOptimizations.BackgroundOptimizationEnabled1sOrLessMediaSource:max_keyframe_distance_media_source_ms/1000/max_keyframe_distance_ms/0,BetterMultiTabsLoading.PageAlmostIdleSignalEnabled:mainThreadTaskLoadLowThreshold/25,BlinkSchedulerDedicatedWorkerThrottling.Enabled:max_budget_ms/1000/max_delay_ms/60000/recovery_rate/0%2E01,CSSExternalScanner.Enabled_ScanAndPreload:cssExternalScannerNoPreload/false/cssExternalScannerPreload/true,DataReductionProxyRobustConnection.Enabled:bypass_missing_via_disabled/true/warmup_fetch_callback_enabled/true,DelayRequestsOnMultiplexedConnections.Enabled:MaxEffectiveConnectionType/3G,HTTPBadPhase3.NotSecureWarning:treatment/warning,LowPriorityIframes2.Enabled:max_effective_connection_type_threshold/4G,NTPUseGoogleLocalNtp.Enabled:local_ntp_interactive_doodles/false,NetworkQualityEstimator.Enabled:hanging_request_http_rtt_upper_bound_http_rtt_multiplier/6/hanging_request_http_rtt_upper_bound_transport_rtt_multiplier/8/hanging_request_upper_bound_min_http_rtt_msec/500/lower_bound_http_rtt_transport_rtt_multiplier/1/rssi_weight_per_signal_strength_level/0%2E9/throughput_hanging_requests_cwnd_size_multiplier/1/upper_bound_http_rtt_transport_rtt_multiplier/5,OmniboxBundledExperimentV1.DesktopExperiments:UIVerticalMargin/10,PasswordGenerationRequirements.Enabled:prefix_length/0/timeout/5000/version/0,PasswordSmartBubble.3-Times:dismissal_count/3,PersistentHistograms.EnabledOnDisk5:send_unreported_metrics/yes/storage/MappedFile,RecurrentSSLInterstitial.RecurrentInterstitialWithUIEnabled:show_error_ui/true,RendererSchedulerWakeUpThrottling.RendererSchedulerWakeUpThrottling:wake_up_duration_ms/5,ReportCertificateErrors.ShowAndPossiblySend:sendingThreshold/1%2E0,ResourceLoadScheduler.Enabled_bg_limit_3_2:bg_limit/3/bg_sub_limit/2,SafeBrowsingAdSamplerTrigger.AdSampler_NormalRate:safe_browsing_ad_sampler_frequency_denominator/1000/trigger_type_and_quota_csv/2%2C10,SafeBrowsingSuspiciousSiteTrigger.Enabled:suspicious_site_trigger_quota/10,SafeBrowsingThreatDomDetailsTagAttributes.AdIdentifiers:tag_attribute_csv/div%2Cdata-google-query-id%2Cdiv%2Cid%2Ciframe%2Cid,SavePreviousDocumentResources.until-dcl:until/onDOMContentLoaded,SecurityChip.Enabled:animation/animate-nonsecure-only/visibility/show-all,SignInPasswordPromo.Enable3:dismissal_threshold/3,SimplifyHttpsIndicator.EvToSecure:treatment/ev-to-secure,TLS13Variant.Draft23V4:variant/draft23,ThrottleDelayable.Enabled:EffectiveConnectionType1/3G/MaxDelayableRequests1/14/NonDelayableWeight1/2%2E0,TranslateRankerModel.20180123_launch:translate-ranker-model-url/https%3A%2F%2Fwww%2Egstatic%2Ecom%2Fchrome%2Fintelligence%2Fassist%2Franker%2Fmodels%2Ftranslate%2Ftranslate_ranker_20180123%2Emodel,TranslateServerStudy.SmartRendering:server_params/smrd,UKM.Enabled:RecordSessionId/true,WebBluetoothBlocklist.TestGroup:blocklist_additions/********%3Ae%2Cfffd%3Ae%2Ced5f25a4%3Ae', u'--force-fieldtrials=AccountConsistencyVariations/DiceMigration/AdFramePriority/LowPriorityAdFrameEnabled/AppleScriptExecuteJavaScript/Disabled/AudioService/AudioProcess_LaunchOnStartup_v3/AutofillCreditCardBankNameDisplay/BankName_Experiment/AutofillExpandedPopupViews/Enabled/AutofillFieldMetadata/Enabled/AutofillProfileOrderByFrecency/EnabledLimitTo3/AutofillResetFullServerCardsOnAuthError/Enabled/AutofillRestrictUnownedFieldsToFormlessCheckout/Disabled/AutofillSingleClick/Enabled/AutofillUpstreamUpdatePromptExplanation/Enabled/AutomaticTabDiscarding/Enabled_Once_10-gen2/BackgroundPagePriority/LowPriorityEnabled/BackgroundVideoOptimizations/BackgroundOptimizationEnabled1sOrLessMediaSource/BetterMultiTabsLoading/PageAlmostIdleSignalEnabled/BlinkSchedulerDedicatedWorkerThrottling/Enabled/BlinkSchedulerHighPriorityInputOnCompositorThread/Enabled/BlockTabUnders/Enabled/BrowserScheduler/RedirectWithDefaultInitParams/BundledConnectionHelp/HTTPHelpCenterEnabled/CSSExternalScanner/Enabled_ScanAndPreload/CertificateTransparencyLogAuditing/Enabled/ChromeSuggestionsTuningForceTopSites/Disabled/CompositorImageAnimation/CompositorImageAnimation/DataReductionProxyRobustConnection/Enabled/DecoupleTranslateLanguage/Experiment/DefaultEnableGpuRasterization/DefaultEnableGpuRasterization/DelayRequestsOnMultiplexedConnections/Enabled/DynamicExpectCT/DynamicExpectCTEnabled/EmitGoogleSearchDomainMixingMetrics/Enabled/EnableCastDiscovery/Enabled/EnableDialLocalDiscovery/Enabled/EnableEmojiContextMenu/Enabled/EnableMediaRouter/Enabled/EnableNewMediaRouterRouteController/Enabled/ExpectCTReporting/ExpectCTReportingEnabled/ExtensionDeveloperModeWarning/Enabled/ExtensionInstallVerification/Enforce/FrameAndTaskTypePriorityDuringLoading/LowPriorityForHiddenFrameDuringLoading/GoogleBrandedContextMenu/branded/GpuScheduler/Enabled/GuestViewCrossProcessFrames/Enabled/HTTPBadPhase3/NotSecureWarning/Html5ByDefault/Enabled/HttpFormWarning/Enabled/IDBTombstoneSweeper/TombstoneDeletion/IOSurfaceCapturer/Enabled/ImprovedRecoveryComponent/ImprovedRecoveryComponent/IncludeBetaForumMenuItem/Enabled/InspectDownloadedRarFiles/Enabled/InstanceID/Enabled/IntelligentSessionRestore/Enabled/LocalScreenCasting/Enabled/LowPriorityIframes2/Enabled/MacAllowBackgroundingProcesses/EnabledTaskPolicy/MacMDDownloadShelf/Enabled/MacV2Sandbox/Enabled/ModernMediaControls/Enabled/MojoChannel/Enabled/MseBufferByPts/Enabled/NTPBirthdayFeatures/NewTabPageAllBirthday_Enabled/NTPUseGoogleLocalNtp/Enabled/NativeCrxBindings/Enabled/NetworkQualityEstimator/Enabled/NewPrintPreview/Enabled/NoSearchDomainCheck/Enabled/OfferUploadCreditCards/Enabled/OmniboxBundledExperimentV1/DesktopExperiments/OverflowIconsForMediaControls/Enabled/PDFClickToOpen/Enabled/PWAFullCodeCache/Enabled/PassiveDocumentEventListeners/Enabled/PassiveEventListenersDueToFling/Enabled/PasswordGenerationRequirements/Enabled/PasswordProtectionForEnterprise/V1Enabled/PasswordSeparatedSigninFlow/Enabled/PasswordSmartBubble/3-Times/PermissionPromptUICocoa/BlockPromptsEnabled/PersistentHistograms/EnabledOnDisk5/PrioritizedSimpleCacheTasks/Enabled/QUIC/Enabled/RecurrentSSLInterstitial/RecurrentInterstitialWithUIEnabled/RemoveNavigationHistory/Enabled/RendererSchedulerWakeUpThrottling/RendererSchedulerWakeUpThrottling/ReportCertificateErrors/ShowAndPossiblySend/ReportingAPIAndNetworkErrorLogging/ReportingAndNELEnabled/ResourceLoadScheduler/Enabled_bg_limit_3_2/SSLCommonNameMismatchHandling/Enabled/SafeBrowsingAdSamplerTrigger/AdSampler_NormalRate/SafeBrowsingScoutTransitionStudy/CanShowScoutOptInGroup2/SafeBrowsingSuspiciousSiteTrigger/Enabled/SafeBrowsingThreatDomDetailsTagAttributes/AdIdentifiers/SavePreviousDocumentResources/until-dcl/ScrollAnchorSerialization/Enabled/SecurityChip/Enabled/ServiceWorkerNavigationPreload/Enabled/ServiceWorkerPaymentApps/Enabled/ServiceWorkerScriptFullCodeCache/Enabled/ServiceWorkerScriptStreaming/Enabled/SettingsEnforcement/enforce_always_with_extensions_and_dse/Show109ObsoleteInfobar/Enabled/SignInPasswordPromo/Enable3/SimpleCacheTrial/ExperimentYes/SimplifyHttpsIndicator/EvToSecure/SitePerProcess/SitePerProcess_Enabled/SocketReadIfReady/Enabled/SpdyProxiesRequestsDelayable/Enabled/SpeculativeLaunchServiceWorker/Enabled/SupervisedUserCommittedInterstitials/SupervisedUserCommittedInterstitialsEnabled/TLS13Variant/Draft23V4/ThrottleDelayable/Enabled/TokenBinding/TokenBinding/TranslateRankerModel/20180123_launch/TranslateServerStudy/SmartRendering/TranslateUiLangTrial/DefaultTranslateLanguage/TranslateUserEvents/Enabled/TurnOffCompositorAnimation/Enabled/UKM/Enabled/UseHeuristicLanguageModel/Experiment/UsePdfCompositorServiceForPrint/UsePdfCompositorServiceForPrint/V8AsmJSToWasm/AsmJsToWebAssembly/V8ContextSnapshot/Enabled/V8Ignition/Future/VariationsHttpDisabled/VariationsHttpDisabled/VideoCaptureService/Enabled2/VideoSurfaceLayer/Enabled/ViewsBrowserWindows/Enabled/VizHitTestDrawQuad/Enabled/VsyncAlignedInput/Enable/WebBluetoothBlocklist/TestGroup/WebRTC-ApmGainController2Limiter/Enabled/WebRTC-Audio-NetEqPostponeDecodingAfterExpand/Enabled/WebRTC-EnableWebRtcEcdsa/Enabled/WebRTC-H264WithOpenH264FFmpeg/Enabled/WebRTC-LocalIPPermissionCheck/Enabled/WebRTC-NetEqOpusDtxDelayFix/Enabled/WebRTC-NewCpuLoadEstimator/Enabled/WebRTC-ProbingScreenshareBwe/1.0,2875,80,40,-60,3/WebRTC-SimulcastScreenshare/Enabled/WebRtcUseEchoCanceller3/WebRtcUseEchoCanceller3/WheelScrollLatchingAndAsyncWheelEvents/Enabled/WorkStealingInScriptRunner/Enabled2/use-new-media-cache/Enabled', u'--disable-features=VariationsHttpRetry<VariationsHttpDisabled,CustomizedTabLoadTimeout<BetterMultiTabsLoading,AutofillRestrictUnownedFieldsToFormlessCheckout<AutofillRestrictUnownedFieldsToFormlessCheckout,AppleScriptExecuteJavaScript<AppleScriptExecuteJavaScript', u'--enable-features=EnableCastDiscovery<EnableCastDiscovery,DelayRequestsOnMultiplexedConnections<DelayRequestsOnMultiplexedConnections,Show109ObsoleteInfobar<Show109ObsoleteInfobar,NewPrintPreview<NewPrintPreview,WebRtcUseEchoCanceller3<WebRtcUseEchoCanceller3,NetworkErrorLogging<ReportingAPIAndNetworkErrorLogging,AutofillExpandedPopupViews<AutofillExpandedPopupViews,AutofillResetFullServerCardsOnAuthError<AutofillResetFullServerCardsOnAuthError,MarkHttpAs<HTTPBadPhase3,CertificateTransparencyLogAuditing<CertificateTransparencyLogAuditing,UsePdfCompositorServiceForPrint<UsePdfCompositorServiceForPrint,OmniboxUIExperimentShowSuggestionFavicons<OmniboxBundledExperimentV1,DoodlesOnLocalNtp<NTPUseGoogleLocalNtp,OverflowIconsForMediaControls<OverflowIconsForMediaControls,PWAFullCodeCache<PWAFullCodeCache,IOSurfaceCapturer<IOSurfaceCapturer,AsyncWheelEvents<WheelScrollLatchingAndAsyncWheelEvents,IncludeBetaForumMenuItem<IncludeBetaForumMenuItem,SimplifyHttpsIndicator<SimplifyHttpsIndicator,UseGoogleLocalNtp<NTPUseGoogleLocalNtp,CanShowScoutOptIn<SafeBrowsingScoutTransitionStudy,DataReductionProxyRobustConnection<DataReductionProxyRobustConnection,NewTabPageIcons<NTPBirthdayFeatures,PasswordGenerationRequirements<PasswordGenerationRequirements,BlinkSchedulerWorkerThrottling<BlinkSchedulerDedicatedWorkerThrottling,UsePasswordSeparatedSigninFlow<PasswordSeparatedSigninFlow,UseSurfaceLayerForVideo<VideoSurfaceLayer,DecoupleTranslateLanguageFeature<DecoupleTranslateLanguage,GuestViewCrossProcessFrames<GuestViewCrossProcessFrames,BlinkSchedulerLowPriorityForBackgroundPages<BackgroundPagePriority,UseHeuristicLanguageModel<UseHeuristicLanguageModel,DefaultEnableGpuRasterization<DefaultEnableGpuRasterization,BlockTabUnders<BlockTabUnders,OmniboxDisplayTitleForCurrentUrl<OmniboxBundledExperimentV1,MseBufferByPts<MseBufferByPts,ImprovedRecoveryComponent<ImprovedRecoveryComponent,WebRTC-H264WithOpenH264FFmpeg<WebRTC-H264WithOpenH264FFmpeg,LowPriorityIframes<LowPriorityIframes2,NativeCrxBindings<NativeCrxBindings,ZeroSuggestRedirectToChrome<OmniboxBundledExperimentV1,SecurityChip<SecurityChip,SyncUserLanguageDetectionEvents<TranslateUserEvents,token-binding<TokenBinding,TranslateRankerAutoBlacklistOverride<TranslateRankerModel,ServiceWorkerScriptStreaming<ServiceWorkerScriptStreaming,CompositorImageAnimation<CompositorImageAnimation,TranslateRankerQuery<TranslateRankerModel,ClickToOpenPDFPlaceholder<PDFClickToOpen,OmniboxUIExperimentSwapTitleAndUrl<OmniboxBundledExperimentV1,ExpectCTReporting<ExpectCTReporting,Reporting<ReportingAPIAndNetworkErrorLogging,PrioritizedSimpleCacheTasks<PrioritizedSimpleCacheTasks,V8ContextSnapshot<V8ContextSnapshot,LocalScreenCasting<LocalScreenCasting,MacAllowBackgroundingProcesses<MacAllowBackgroundingProcesses,AudioServiceOutOfProcess<AudioService,NoSearchDomainCheck<NoSearchDomainCheck,IDBTombstoneDeletion<IDBTombstoneSweeper,BlinkSchedulerLowPriorityForHiddenFrame<FrameAndTaskTypePriorityDuringLoading,SavePreviousDocumentResources<SavePreviousDocumentResources,SocketReadIfReady<SocketReadIfReady,EnableEmojiContextMenu<EnableEmojiContextMenu,ThreatDomDetailsTagAttributes<SafeBrowsingThreatDomDetailsTagAttributes,StaggeredBackgroundTabOpening<BetterMultiTabsLoading,RemoveNavigationHistory<RemoveNavigationHistory,OmniboxBreakWordsAtUnderscores<OmniboxBundledExperimentV1,VizHitTestDrawQuad<VizHitTestDrawQuad,SafeBrowsingSuspiciousSiteTriggerQuota<SafeBrowsingSuspiciousSiteTrigger,ResourceLoadScheduler<ResourceLoadScheduler,SpeculativeLaunchServiceWorker<SpeculativeLaunchServiceWorker,PreferHtmlOverPlugins<Html5ByDefault,TouchpadAndWheelScrollLatching<WheelScrollLatchingAndAsyncWheelEvents,PersistentHistograms<PersistentHistograms,AudioServiceLaunchOnStartup<AudioService,SupervisedUserCommittedInterstitials<SupervisedUserCommittedInterstitials,BundledConnectionHelp<BundledConnectionHelp,AccountConsistency<AccountConsistencyVariations,BlinkSchedulerExperimentOnlyWhenLoading<FrameAndTaskTypePriorityDuringLoading,SafeBrowsingAdSamplerTrigger<SafeBrowsingAdSamplerTrigger,use-new-media-cache<use-new-media-cache,EnterprisePasswordProtectionV1<PasswordProtectionForEnterprise,ZeroSuggestSwapTitleAndUrl<OmniboxBundledExperimentV1,OmniboxTailSuggestions<OmniboxBundledExperimentV1,StaggeredBackgroundTabOpeningExperiment<BetterMultiTabsLoading,PasswordGenerationRequirementsDomainOverrides<PasswordGenerationRequirements,ServiceWorkerPaymentApps<ServiceWorkerPaymentApps,SafeBrowsingTriggerThrottlerDailyQuota<SafeBrowsingAdSamplerTrigger,PassiveEventListenersDueToFling<PassiveEventListenersDueToFling,TranslateRankerEnforcement<TranslateRankerModel,SyncUserTranslationEvents<TranslateUserEvents,GpuScheduler<GpuScheduler,AudioServiceAudioStreams<AudioService,PassiveDocumentEventListeners<PassiveDocumentEventListeners,AutomaticTabDiscarding<AutomaticTabDiscarding,MediaRouterUIRouteController<EnableNewMediaRouterRouteController,EnableDialLocalDiscovery<EnableDialLocalDiscovery,EmitGoogleSearchDomainMixingMetrics<EmitGoogleSearchDomainMixingMetrics,RecurrentInterstitialFeature<RecurrentSSLInterstitial,AutofillCreditCardBankNameDisplay<AutofillCreditCardBankNameDisplay,PageAlmostIdle<BetterMultiTabsLoading,HttpFormWarning<HttpFormWarning,DynamicExpectCT<DynamicExpectCT,BlinkSchedulerLowPriorityForAdFrame<AdFramePriority,ViewsBrowserWindows<ViewsBrowserWindows,NewTabPageBackgrounds<NTPBirthdayFeatures,WebRTC-EnableWebRtcEcdsa<WebRTC-EnableWebRtcEcdsa,AutofillUpstreamUpdatePromptExplanation<AutofillUpstreamUpdatePromptExplanation,SpdyProxiesRequestsDelayable<SpdyProxiesRequestsDelayable,AutofillUpstream<OfferUploadCreditCards,MacV2Sandbox<MacV2Sandbox,BackgroundVideoTrackOptimization<BackgroundVideoOptimizations,BlockPromptsIfDismissedOften<PermissionPromptUICocoa,OmniboxUIExperimentVerticalMargin<OmniboxBundledExperimentV1,NewEncodeCpuLoadEstimator<WebRTC-NewCpuLoadEstimator,V8Future<V8Ignition,AsmJsToWebAssembly<V8AsmJSToWasm,UseModernMediaControls<ModernMediaControls,ServiceWorkerScriptFullCodeCache<ServiceWorkerScriptFullCodeCache,ThrottleDelayable<ThrottleDelayable,RequestUnbufferedDispatch<VsyncAlignedInput,site-per-process<SitePerProcess,Ukm<UKM,MojoVideoCapture<VideoCaptureService,VsyncAlignedInput<VsyncAlignedInput,ServiceWorkerNavigationPreload<ServiceWorkerNavigationPreload,ScrollAnchorSerialization<ScrollAnchorSerialization,TurnOffCompositorAnimation<TurnOffCompositorAnimation,InspectDownloadedRarFiles<InspectDownloadedRarFiles,MacMDDownloadShelf<MacMDDownloadShelf,NewTabPageUIMd<NTPBirthdayFeatures,BlinkSchedulerHighPriorityInputOnCompositorThread<BlinkSchedulerHighPriorityInputOnCompositorThread,WorkStealingInScriptRunner<WorkStealingInScriptRunner', '--enable-net-benchmarking', '--metrics-recording-only', '--no-default-browser-check', '--no-first-run', '--enable-gpu-benchmarking', '--deny-permission-prompts', '--autoplay-policy=no-user-gesture-required', '--disable-background-networking', '--disable-component-extensions-with-background-pages', '--disable-default-apps', '--disable-search-geolocation-disclosure', '--proxy-server=socks://localhost:65083', '--ignore-certificate-errors-spki-list=PhrPvGIaAMmd29hj8BCZOq096yj7uMpRNHpn5PDxI6I=', '--remote-debugging-port=0', '--enable-crash-reporter-for-testing', '--disable-component-update', '--window-size=1280,1024', '--user-data-dir=/b/s/w/itO80c0O/tmpTU_XmP', '--trace-config-file=/b/s/w/itO80c0O/tmpsoGHCC/chrome-trace-config.json', 'about:blank']
(INFO) 2018-07-06 02:35:30,807 __init__._StartedForwarding:47  DoNothingForwarder started between 127.0.0.1:65090 and 65090
(INFO) 2018-07-06 02:35:30,808 chrome_browser_backend._GetDevToolsClient:118  Got devtools config: ws://127.0.0.1:65090/devtools/browser/5745660e-ff3c-415d-9864-c74e10da8d57
(INFO) 2018-07-06 02:35:31,409 browser._LogBrowserInfo:99  Browser started (pid=20832).
(INFO) 2018-07-06 02:35:31,410 browser._LogBrowserInfo:102  OS: mac highsierra
(INFO) 2018-07-06 02:35:31,432 browser._LogBrowserInfo:105  Detailed OS version: 10.13.3 17D47
(INFO) 2018-07-06 02:35:31,436 browser._LogBrowserInfo:109  Model: MacBookPro 11.5
(INFO) 2018-07-06 02:35:31,436 browser._LogBrowserInfo:111  Browser command line: /b/s/w/ir/out/Release/Google Chrome.app/Contents/MacOS/Google Chrome --force-fieldtrial-params=AccountConsistencyVariations.DiceMigration:method/dice_migration,AudioService.AudioProcess_LaunchOnStartup_v3:teardown_timeout_s/0,AutofillProfileOrderByFrecency.EnabledLimitTo3:limit/3,AutomaticTabDiscarding.Enabled_Once_10-gen2:MinimumProtectionTime/600,BackgroundVideoOptimizations.BackgroundOptimizationEnabled1sOrLessMediaSource:max_keyframe_distance_media_source_ms/1000/max_keyframe_distance_ms/0,BetterMultiTabsLoading.PageAlmostIdleSignalEnabled:mainThreadTaskLoadLowThreshold/25,BlinkSchedulerDedicatedWorkerThrottling.Enabled:max_budget_ms/1000/max_delay_ms/60000/recovery_rate/0%2E01,CSSExternalScanner.Enabled_ScanAndPreload:cssExternalScannerNoPreload/false/cssExternalScannerPreload/true,DataReductionProxyRobustConnection.Enabled:bypass_missing_via_disabled/true/warmup_fetch_callback_enabled/true,DelayRequestsOnMultiplexedConnections.Enabled:MaxEffectiveConnectionType/3G,HTTPBadPhase3.NotSecureWarning:treatment/warning,LowPriorityIframes2.Enabled:max_effective_connection_type_threshold/4G,NTPUseGoogleLocalNtp.Enabled:local_ntp_interactive_doodles/false,NetworkQualityEstimator.Enabled:hanging_request_http_rtt_upper_bound_http_rtt_multiplier/6/hanging_request_http_rtt_upper_bound_transport_rtt_multiplier/8/hanging_request_upper_bound_min_http_rtt_msec/500/lower_bound_http_rtt_transport_rtt_multiplier/1/rssi_weight_per_signal_strength_level/0%2E9/throughput_hanging_requests_cwnd_size_multiplier/1/upper_bound_http_rtt_transport_rtt_multiplier/5,OmniboxBundledExperimentV1.DesktopExperiments:UIVerticalMargin/10,PasswordGenerationRequirements.Enabled:prefix_length/0/timeout/5000/version/0,PasswordSmartBubble.3-Times:dismissal_count/3,PersistentHistograms.EnabledOnDisk5:send_unreported_metrics/yes/storage/MappedFile,RecurrentSSLInterstitial.RecurrentInterstitialWithUIEnabled:show_error_ui/true,RendererSchedulerWakeUpThrottling.RendererSchedulerWakeUpThrottling:wake_up_duration_ms/5,ReportCertificateErrors.ShowAndPossiblySend:sendingThreshold/1%2E0,ResourceLoadScheduler.Enabled_bg_limit_3_2:bg_limit/3/bg_sub_limit/2,SafeBrowsingAdSamplerTrigger.AdSampler_NormalRate:safe_browsing_ad_sampler_frequency_denominator/1000/trigger_type_and_quota_csv/2%2C10,SafeBrowsingSuspiciousSiteTrigger.Enabled:suspicious_site_trigger_quota/10,SafeBrowsingThreatDomDetailsTagAttributes.AdIdentifiers:tag_attribute_csv/div%2Cdata-google-query-id%2Cdiv%2Cid%2Ciframe%2Cid,SavePreviousDocumentResources.until-dcl:until/onDOMContentLoaded,SecurityChip.Enabled:animation/animate-nonsecure-only/visibility/show-all,SignInPasswordPromo.Enable3:dismissal_threshold/3,SimplifyHttpsIndicator.EvToSecure:treatment/ev-to-secure,TLS13Variant.Draft23V4:variant/draft23,ThrottleDelayable.Enabled:EffectiveConnectionType1/3G/MaxDelayableRequests1/14/NonDelayableWeight1/2%2E0,TranslateRankerModel.20180123_launch:translate-ranker-model-url/https%3A%2F%2Fwww%2Egstatic%2Ecom%2Fchrome%2Fintelligence%2Fassist%2Franker%2Fmodels%2Ftranslate%2Ftranslate_ranker_20180123%2Emodel,TranslateServerStudy.SmartRendering:server_params/smrd,UKM.Enabled:RecordSessionId/true,WebBluetoothBlocklist.TestGroup:blocklist_additions/********%3Ae%2Cfffd%3Ae%2Ced5f25a4%3Ae --force-fieldtrials=AccountConsistencyVariations/DiceMigration/AdFramePriority/LowPriorityAdFrameEnabled/AppleScriptExecuteJavaScript/Disabled/AudioService/AudioProcess_LaunchOnStartup_v3/AutofillCreditCardBankNameDisplay/BankName_Experiment/AutofillExpandedPopupViews/Enabled/AutofillFieldMetadata/Enabled/AutofillProfileOrderByFrecency/EnabledLimitTo3/AutofillResetFullServerCardsOnAuthError/Enabled/AutofillRestrictUnownedFieldsToFormlessCheckout/Disabled/AutofillSingleClick/Enabled/AutofillUpstreamUpdatePromptExplanation/Enabled/AutomaticTabDiscarding/Enabled_Once_10-gen2/BackgroundPagePriority/LowPriorityEnabled/BackgroundVideoOptimizations/BackgroundOptimizationEnabled1sOrLessMediaSource/BetterMultiTabsLoading/PageAlmostIdleSignalEnabled/BlinkSchedulerDedicatedWorkerThrottling/Enabled/BlinkSchedulerHighPriorityInputOnCompositorThread/Enabled/BlockTabUnders/Enabled/BrowserScheduler/RedirectWithDefaultInitParams/BundledConnectionHelp/HTTPHelpCenterEnabled/CSSExternalScanner/Enabled_ScanAndPreload/CertificateTransparencyLogAuditing/Enabled/ChromeSuggestionsTuningForceTopSites/Disabled/CompositorImageAnimation/CompositorImageAnimation/DataReductionProxyRobustConnection/Enabled/DecoupleTranslateLanguage/Experiment/DefaultEnableGpuRasterization/DefaultEnableGpuRasterization/DelayRequestsOnMultiplexedConnections/Enabled/DynamicExpectCT/DynamicExpectCTEnabled/EmitGoogleSearchDomainMixingMetrics/Enabled/EnableCastDiscovery/Enabled/EnableDialLocalDiscovery/Enabled/EnableEmojiContextMenu/Enabled/EnableMediaRouter/Enabled/EnableNewMediaRouterRouteController/Enabled/ExpectCTReporting/ExpectCTReportingEnabled/ExtensionDeveloperModeWarning/Enabled/ExtensionInstallVerification/Enforce/FrameAndTaskTypePriorityDuringLoading/LowPriorityForHiddenFrameDuringLoading/GoogleBrandedContextMenu/branded/GpuScheduler/Enabled/GuestViewCrossProcessFrames/Enabled/HTTPBadPhase3/NotSecureWarning/Html5ByDefault/Enabled/HttpFormWarning/Enabled/IDBTombstoneSweeper/TombstoneDeletion/IOSurfaceCapturer/Enabled/ImprovedRecoveryComponent/ImprovedRecoveryComponent/IncludeBetaForumMenuItem/Enabled/InspectDownloadedRarFiles/Enabled/InstanceID/Enabled/IntelligentSessionRestore/Enabled/LocalScreenCasting/Enabled/LowPriorityIframes2/Enabled/MacAllowBackgroundingProcesses/EnabledTaskPolicy/MacMDDownloadShelf/Enabled/MacV2Sandbox/Enabled/ModernMediaControls/Enabled/MojoChannel/Enabled/MseBufferByPts/Enabled/NTPBirthdayFeatures/NewTabPageAllBirthday_Enabled/NTPUseGoogleLocalNtp/Enabled/NativeCrxBindings/Enabled/NetworkQualityEstimator/Enabled/NewPrintPreview/Enabled/NoSearchDomainCheck/Enabled/OfferUploadCreditCards/Enabled/OmniboxBundledExperimentV1/DesktopExperiments/OverflowIconsForMediaControls/Enabled/PDFClickToOpen/Enabled/PWAFullCodeCache/Enabled/PassiveDocumentEventListeners/Enabled/PassiveEventListenersDueToFling/Enabled/PasswordGenerationRequirements/Enabled/PasswordProtectionForEnterprise/V1Enabled/PasswordSeparatedSigninFlow/Enabled/PasswordSmartBubble/3-Times/PermissionPromptUICocoa/BlockPromptsEnabled/PersistentHistograms/EnabledOnDisk5/PrioritizedSimpleCacheTasks/Enabled/QUIC/Enabled/RecurrentSSLInterstitial/RecurrentInterstitialWithUIEnabled/RemoveNavigationHistory/Enabled/RendererSchedulerWakeUpThrottling/RendererSchedulerWakeUpThrottling/ReportCertificateErrors/ShowAndPossiblySend/ReportingAPIAndNetworkErrorLogging/ReportingAndNELEnabled/ResourceLoadScheduler/Enabled_bg_limit_3_2/SSLCommonNameMismatchHandling/Enabled/SafeBrowsingAdSamplerTrigger/AdSampler_NormalRate/SafeBrowsingScoutTransitionStudy/CanShowScoutOptInGroup2/SafeBrowsingSuspiciousSiteTrigger/Enabled/SafeBrowsingThreatDomDetailsTagAttributes/AdIdentifiers/SavePreviousDocumentResources/until-dcl/ScrollAnchorSerialization/Enabled/SecurityChip/Enabled/ServiceWorkerNavigationPreload/Enabled/ServiceWorkerPaymentApps/Enabled/ServiceWorkerScriptFullCodeCache/Enabled/ServiceWorkerScriptStreaming/Enabled/SettingsEnforcement/enforce_always_with_extensions_and_dse/Show109ObsoleteInfobar/Enabled/SignInPasswordPromo/Enable3/SimpleCacheTrial/ExperimentYes/SimplifyHttpsIndicator/EvToSecure/SitePerProcess/SitePerProcess_Enabled/SocketReadIfReady/Enabled/SpdyProxiesRequestsDelayable/Enabled/SpeculativeLaunchServiceWorker/Enabled/SupervisedUserCommittedInterstitials/SupervisedUserCommittedInterstitialsEnabled/TLS13Variant/Draft23V4/ThrottleDelayable/Enabled/TokenBinding/TokenBinding/TranslateRankerModel/20180123_launch/TranslateServerStudy/SmartRendering/TranslateUiLangTrial/DefaultTranslateLanguage/TranslateUserEvents/Enabled/TurnOffCompositorAnimation/Enabled/UKM/Enabled/UseHeuristicLanguageModel/Experiment/UsePdfCompositorServiceForPrint/UsePdfCompositorServiceForPrint/V8AsmJSToWasm/AsmJsToWebAssembly/V8ContextSnapshot/Enabled/V8Ignition/Future/VariationsHttpDisabled/VariationsHttpDisabled/VideoCaptureService/Enabled2/VideoSurfaceLayer/Enabled/ViewsBrowserWindows/Enabled/VizHitTestDrawQuad/Enabled/VsyncAlignedInput/Enable/WebBluetoothBlocklist/TestGroup/WebRTC-ApmGainController2Limiter/Enabled/WebRTC-Audio-NetEqPostponeDecodingAfterExpand/Enabled/WebRTC-EnableWebRtcEcdsa/Enabled/WebRTC-H264WithOpenH264FFmpeg/Enabled/WebRTC-LocalIPPermissionCheck/Enabled/WebRTC-NetEqOpusDtxDelayFix/Enabled/WebRTC-NewCpuLoadEstimator/Enabled/WebRTC-ProbingScreenshareBwe/1.0,2875,80,40,-60,3/WebRTC-SimulcastScreenshare/Enabled/WebRtcUseEchoCanceller3/WebRtcUseEchoCanceller3/WheelScrollLatchingAndAsyncWheelEvents/Enabled/WorkStealingInScriptRunner/Enabled2/use-new-media-cache/Enabled --disable-features=VariationsHttpRetry<VariationsHttpDisabled,CustomizedTabLoadTimeout<BetterMultiTabsLoading,AutofillRestrictUnownedFieldsToFormlessCheckout<AutofillRestrictUnownedFieldsToFormlessCheckout,AppleScriptExecuteJavaScript<AppleScriptExecuteJavaScript --enable-features=EnableCastDiscovery<EnableCastDiscovery,DelayRequestsOnMultiplexedConnections<DelayRequestsOnMultiplexedConnections,Show109ObsoleteInfobar<Show109ObsoleteInfobar,NewPrintPreview<NewPrintPreview,WebRtcUseEchoCanceller3<WebRtcUseEchoCanceller3,NetworkErrorLogging<ReportingAPIAndNetworkErrorLogging,AutofillExpandedPopupViews<AutofillExpandedPopupViews,AutofillResetFullServerCardsOnAuthError<AutofillResetFullServerCardsOnAuthError,MarkHttpAs<HTTPBadPhase3,CertificateTransparencyLogAuditing<CertificateTransparencyLogAuditing,UsePdfCompositorServiceForPrint<UsePdfCompositorServiceForPrint,OmniboxUIExperimentShowSuggestionFavicons<OmniboxBundledExperimentV1,DoodlesOnLocalNtp<NTPUseGoogleLocalNtp,OverflowIconsForMediaControls<OverflowIconsForMediaControls,PWAFullCodeCache<PWAFullCodeCache,IOSurfaceCapturer<IOSurfaceCapturer,AsyncWheelEvents<WheelScrollLatchingAndAsyncWheelEvents,IncludeBetaForumMenuItem<IncludeBetaForumMenuItem,SimplifyHttpsIndicator<SimplifyHttpsIndicator,UseGoogleLocalNtp<NTPUseGoogleLocalNtp,CanShowScoutOptIn<SafeBrowsingScoutTransitionStudy,DataReductionProxyRobustConnection<DataReductionProxyRobustConnection,NewTabPageIcons<NTPBirthdayFeatures,PasswordGenerationRequirements<PasswordGenerationRequirements,BlinkSchedulerWorkerThrottling<BlinkSchedulerDedicatedWorkerThrottling,UsePasswordSeparatedSigninFlow<PasswordSeparatedSigninFlow,UseSurfaceLayerForVideo<VideoSurfaceLayer,DecoupleTranslateLanguageFeature<DecoupleTranslateLanguage,GuestViewCrossProcessFrames<GuestViewCrossProcessFrames,BlinkSchedulerLowPriorityForBackgroundPages<BackgroundPagePriority,UseHeuristicLanguageModel<UseHeuristicLanguageModel,DefaultEnableGpuRasterization<DefaultEnableGpuRasterization,BlockTabUnders<BlockTabUnders,OmniboxDisplayTitleForCurrentUrl<OmniboxBundledExperimentV1,MseBufferByPts<MseBufferByPts,ImprovedRecoveryComponent<ImprovedRecoveryComponent,WebRTC-H264WithOpenH264FFmpeg<WebRTC-H264WithOpenH264FFmpeg,LowPriorityIframes<LowPriorityIframes2,NativeCrxBindings<NativeCrxBindings,ZeroSuggestRedirectToChrome<OmniboxBundledExperimentV1,SecurityChip<SecurityChip,SyncUserLanguageDetectionEvents<TranslateUserEvents,token-binding<TokenBinding,TranslateRankerAutoBlacklistOverride<TranslateRankerModel,ServiceWorkerScriptStreaming<ServiceWorkerScriptStreaming,CompositorImageAnimation<CompositorImageAnimation,TranslateRankerQuery<TranslateRankerModel,ClickToOpenPDFPlaceholder<PDFClickToOpen,OmniboxUIExperimentSwapTitleAndUrl<OmniboxBundledExperimentV1,ExpectCTReporting<ExpectCTReporting,Reporting<ReportingAPIAndNetworkErrorLogging,PrioritizedSimpleCacheTasks<PrioritizedSimpleCacheTasks,V8ContextSnapshot<V8ContextSnapshot,LocalScreenCasting<LocalScreenCasting,MacAllowBackgroundingProcesses<MacAllowBackgroundingProcesses,AudioServiceOutOfProcess<AudioService,NoSearchDomainCheck<NoSearchDomainCheck,IDBTombstoneDeletion<IDBTombstoneSweeper,BlinkSchedulerLowPriorityForHiddenFrame<FrameAndTaskTypePriorityDuringLoading,SavePreviousDocumentResources<SavePreviousDocumentResources,SocketReadIfReady<SocketReadIfReady,EnableEmojiContextMenu<EnableEmojiContextMenu,ThreatDomDetailsTagAttributes<SafeBrowsingThreatDomDetailsTagAttributes,StaggeredBackgroundTabOpening<BetterMultiTabsLoading,RemoveNavigationHistory<RemoveNavigationHistory,OmniboxBreakWordsAtUnderscores<OmniboxBundledExperimentV1,VizHitTestDrawQuad<VizHitTestDrawQuad,SafeBrowsingSuspiciousSiteTriggerQuota<SafeBrowsingSuspiciousSiteTrigger,ResourceLoadScheduler<ResourceLoadScheduler,SpeculativeLaunchServiceWorker<SpeculativeLaunchServiceWorker,PreferHtmlOverPlugins<Html5ByDefault,TouchpadAndWheelScrollLatching<WheelScrollLatchingAndAsyncWheelEvents,PersistentHistograms<PersistentHistograms,AudioServiceLaunchOnStartup<AudioService,SupervisedUserCommittedInterstitials<SupervisedUserCommittedInterstitials,BundledConnectionHelp<BundledConnectionHelp,AccountConsistency<AccountConsistencyVariations,BlinkSchedulerExperimentOnlyWhenLoading<FrameAndTaskTypePriorityDuringLoading,SafeBrowsingAdSamplerTrigger<SafeBrowsingAdSamplerTrigger,use-new-media-cache<use-new-media-cache,EnterprisePasswordProtectionV1<PasswordProtectionForEnterprise,ZeroSuggestSwapTitleAndUrl<OmniboxBundledExperimentV1,OmniboxTailSuggestions<OmniboxBundledExperimentV1,StaggeredBackgroundTabOpeningExperiment<BetterMultiTabsLoading,PasswordGenerationRequirementsDomainOverrides<PasswordGenerationRequirements,ServiceWorkerPaymentApps<ServiceWorkerPaymentApps,SafeBrowsingTriggerThrottlerDailyQuota<SafeBrowsingAdSamplerTrigger,PassiveEventListenersDueToFling<PassiveEventListenersDueToFling,TranslateRankerEnforcement<TranslateRankerModel,SyncUserTranslationEvents<TranslateUserEvents,GpuScheduler<GpuScheduler,AudioServiceAudioStreams<AudioService,PassiveDocumentEventListeners<PassiveDocumentEventListeners,AutomaticTabDiscarding<AutomaticTabDiscarding,MediaRouterUIRouteController<EnableNewMediaRouterRouteController,EnableDialLocalDiscovery<EnableDialLocalDiscovery,EmitGoogleSearchDomainMixingMetrics<EmitGoogleSearchDomainMixingMetrics,RecurrentInterstitialFeature<RecurrentSSLInterstitial,AutofillCreditCardBankNameDisplay<AutofillCreditCardBankNameDisplay,PageAlmostIdle<BetterMultiTabsLoading,HttpFormWarning<HttpFormWarning,DynamicExpectCT<DynamicExpectCT,BlinkSchedulerLowPriorityForAdFrame<AdFramePriority,ViewsBrowserWindows<ViewsBrowserWindows,NewTabPageBackgrounds<NTPBirthdayFeatures,WebRTC-EnableWebRtcEcdsa<WebRTC-EnableWebRtcEcdsa,AutofillUpstreamUpdatePromptExplanation<AutofillUpstreamUpdatePromptExplanation,SpdyProxiesRequestsDelayable<SpdyProxiesRequestsDelayable,AutofillUpstream<OfferUploadCreditCards,MacV2Sandbox<MacV2Sandbox,BackgroundVideoTrackOptimization<BackgroundVideoOptimizations,BlockPromptsIfDismissedOften<PermissionPromptUICocoa,OmniboxUIExperimentVerticalMargin<OmniboxBundledExperimentV1,NewEncodeCpuLoadEstimator<WebRTC-NewCpuLoadEstimator,V8Future<V8Ignition,AsmJsToWebAssembly<V8AsmJSToWasm,UseModernMediaControls<ModernMediaControls,ServiceWorkerScriptFullCodeCache<ServiceWorkerScriptFullCodeCache,ThrottleDelayable<ThrottleDelayable,RequestUnbufferedDispatch<VsyncAlignedInput,site-per-process<SitePerProcess,Ukm<UKM,MojoVideoCapture<VideoCaptureService,VsyncAlignedInput<VsyncAlignedInput,ServiceWorkerNavigationPreload<ServiceWorkerNavigationPreload,ScrollAnchorSerialization<ScrollAnchorSerialization,TurnOffCompositorAnimation<TurnOffCompositorAnimation,InspectDownloadedRarFiles<InspectDownloadedRarFiles,MacMDDownloadShelf<MacMDDownloadShelf,NewTabPageUIMd<NTPBirthdayFeatures,BlinkSchedulerHighPriorityInputOnCompositorThread<BlinkSchedulerHighPriorityInputOnCompositorThread,WorkStealingInScriptRunner<WorkStealingInScriptRunner --enable-net-benchmarking --metrics-recording-only --no-default-browser-check --no-first-run --enable-gpu-benchmarking --deny-permission-prompts --autoplay-policy=no-user-gesture-required --disable-background-networking --disable-component-extensions-with-background-pages --disable-default-apps --disable-search-geolocation-disclosure --proxy-server=socks://localhost:65083 --ignore-certificate-errors-spki-list=PhrPvGIaAMmd29hj8BCZOq096yj7uMpRNHpn5PDxI6I= --remote-debugging-port=0 --enable-crash-reporter-for-testing --disable-component-update --window-size=1280,1024 --user-data-dir=/b/s/w/itO80c0O/tmpTU_XmP --trace-config-file=/b/s/w/itO80c0O/tmpsoGHCC/chrome-trace-config.json --flag-switches-begin --flag-switches-end about:blank
(INFO) 2018-07-06 02:35:31,436 browser._LogBrowserInfo:114  GPU device 0: VENDOR = 0x1002 (ATI), DEVICE = 0x6821
(INFO) 2018-07-06 02:35:31,436 browser._LogBrowserInfo:114  GPU device 1: VENDOR = 0x8086 (Intel), DEVICE = 0xd26
(INFO) 2018-07-06 02:35:31,436 browser._LogBrowserInfo:116  GPU Attributes:
(INFO) 2018-07-06 02:35:31,437 browser._LogBrowserInfo:118    amd_switchable      : True
(INFO) 2018-07-06 02:35:31,437 browser._LogBrowserInfo:118    can_support_threaded_texture_mailbox: False
(INFO) 2018-07-06 02:35:31,437 browser._LogBrowserInfo:118    direct_composition  : False
(INFO) 2018-07-06 02:35:31,437 browser._LogBrowserInfo:118    direct_rendering    : True
(INFO) 2018-07-06 02:35:31,437 browser._LogBrowserInfo:118    encrypted_only      : False
(INFO) 2018-07-06 02:35:31,437 browser._LogBrowserInfo:118    gl_extensions       : GL_ARB_blend_func_extended GL_ARB_draw_buffers_blend GL_ARB_draw_indirect GL_ARB_ES2_compatibility GL_ARB_explicit_attrib_location GL_ARB_gpu_shader_fp64 GL_ARB_gpu_shader5 GL_ARB_instanced_arrays GL_ARB_internalformat_query GL_ARB_occlusion_query2 GL_ARB_sample_shading GL_ARB_sampler_objects GL_ARB_separate_shader_objects GL_ARB_shader_bit_encoding GL_ARB_shader_subroutine GL_ARB_shading_language_include GL_ARB_tessellation_shader GL_ARB_texture_buffer_object_rgb32 GL_ARB_texture_cube_map_array GL_ARB_texture_gather GL_ARB_texture_query_lod GL_ARB_texture_rgb10_a2ui GL_ARB_texture_storage GL_ARB_texture_swizzle GL_ARB_timer_query GL_ARB_transform_feedback2 GL_ARB_transform_feedback3 GL_ARB_vertex_attrib_64bit GL_ARB_vertex_type_2_10_10_10_rev GL_ARB_viewport_array GL_EXT_debug_label GL_EXT_debug_marker GL_EXT_framebuffer_multisample_blit_scaled GL_EXT_texture_compression_s3tc GL_EXT_texture_filter_anisotropic GL_EXT_texture_sRGB_decode GL_APPLE_client_storage GL_APPLE_container_object_shareable GL_APPLE_flush_render GL_APPLE_object_purgeable GL_APPLE_rgb_422 GL_APPLE_row_bytes GL_APPLE_texture_range GL_ATI_texture_mirror_once GL_NV_texture_barrier
(INFO) 2018-07-06 02:35:31,437 browser._LogBrowserInfo:118    gl_renderer         : Intel Iris Pro OpenGL Engine
(INFO) 2018-07-06 02:35:31,437 browser._LogBrowserInfo:118    gl_reset_notification_strategy: 0
(INFO) 2018-07-06 02:35:31,437 browser._LogBrowserInfo:118    gl_vendor           : Intel Inc.
(INFO) 2018-07-06 02:35:31,437 browser._LogBrowserInfo:118    gl_version          : 4.1 INTEL-10.30.14
(INFO) 2018-07-06 02:35:31,437 browser._LogBrowserInfo:118    gl_ws_extensions    : 
(INFO) 2018-07-06 02:35:31,437 browser._LogBrowserInfo:118    gl_ws_vendor        : 
(INFO) 2018-07-06 02:35:31,437 browser._LogBrowserInfo:118    gl_ws_version       : 
(INFO) 2018-07-06 02:35:31,437 browser._LogBrowserInfo:118    in_process_gpu      : False
(INFO) 2018-07-06 02:35:31,438 browser._LogBrowserInfo:118    initialization_time : 0.03645
(INFO) 2018-07-06 02:35:31,438 browser._LogBrowserInfo:118    jpeg_decode_accelerator_supported: False
(INFO) 2018-07-06 02:35:31,438 browser._LogBrowserInfo:118    max_framerate_denominator: 1
(INFO) 2018-07-06 02:35:31,438 browser._LogBrowserInfo:118    max_framerate_numerator: 30
(INFO) 2018-07-06 02:35:31,438 browser._LogBrowserInfo:118    max_msaa_samples    : 8
(INFO) 2018-07-06 02:35:31,438 browser._LogBrowserInfo:118    max_resolution_height: 2160
(INFO) 2018-07-06 02:35:31,438 browser._LogBrowserInfo:118    max_resolution_width: 4096
(INFO) 2018-07-06 02:35:31,438 browser._LogBrowserInfo:118    min_resolution_height: 16
(INFO) 2018-07-06 02:35:31,438 browser._LogBrowserInfo:118    min_resolution_width: 16
(INFO) 2018-07-06 02:35:31,438 browser._LogBrowserInfo:118    oop_rasterization_supported: True
(INFO) 2018-07-06 02:35:31,439 browser._LogBrowserInfo:118    optimus             : False
(INFO) 2018-07-06 02:35:31,439 browser._LogBrowserInfo:118    passthrough_cmd_decoder: False
(INFO) 2018-07-06 02:35:31,439 browser._LogBrowserInfo:118    pixel_shader_version: 4.10
(INFO) 2018-07-06 02:35:31,439 browser._LogBrowserInfo:118    process_crash_count : 0
(INFO) 2018-07-06 02:35:31,439 browser._LogBrowserInfo:118    profile             : 3
(INFO) 2018-07-06 02:35:31,439 browser._LogBrowserInfo:118    sandboxed           : True
(INFO) 2018-07-06 02:35:31,439 browser._LogBrowserInfo:118    software_rendering  : False
(INFO) 2018-07-06 02:35:31,439 browser._LogBrowserInfo:118    supports_overlays   : False
(INFO) 2018-07-06 02:35:31,439 browser._LogBrowserInfo:118    vertex_shader_version: 4.10
(INFO) 2018-07-06 02:35:31,439 browser._LogBrowserInfo:118    video_decode_accelerator_flags: 0
(INFO) 2018-07-06 02:35:31,439 browser._LogBrowserInfo:120  Feature Status:
(INFO) 2018-07-06 02:35:31,440 browser._LogBrowserInfo:122    2d_canvas           : enabled
(INFO) 2018-07-06 02:35:31,440 browser._LogBrowserInfo:122    flash_3d            : enabled
(INFO) 2018-07-06 02:35:31,440 browser._LogBrowserInfo:122    flash_stage3d       : enabled
(INFO) 2018-07-06 02:35:31,440 browser._LogBrowserInfo:122    flash_stage3d_baseline: enabled
(INFO) 2018-07-06 02:35:31,440 browser._LogBrowserInfo:122    gpu_compositing     : enabled
(INFO) 2018-07-06 02:35:31,440 browser._LogBrowserInfo:122    multiple_raster_threads: enabled_on
(INFO) 2018-07-06 02:35:31,440 browser._LogBrowserInfo:122    native_gpu_memory_buffers: enabled
(INFO) 2018-07-06 02:35:31,440 browser._LogBrowserInfo:122    oop_rasterization   : disabled_off
(INFO) 2018-07-06 02:35:31,440 browser._LogBrowserInfo:122    protected_video_decode: unavailable_off
(INFO) 2018-07-06 02:35:31,440 browser._LogBrowserInfo:122    rasterization       : enabled
(INFO) 2018-07-06 02:35:31,440 browser._LogBrowserInfo:122    skia_deferred_display_list: disabled_off
(INFO) 2018-07-06 02:35:31,440 browser._LogBrowserInfo:122    skia_renderer       : disabled_off
(INFO) 2018-07-06 02:35:31,441 browser._LogBrowserInfo:122    surface_synchronization: enabled_on
(INFO) 2018-07-06 02:35:31,441 browser._LogBrowserInfo:122    video_decode        : enabled
(INFO) 2018-07-06 02:35:31,441 browser._LogBrowserInfo:122    viz_display_compositor: disabled_off
(INFO) 2018-07-06 02:35:31,441 browser._LogBrowserInfo:122    webgl               : enabled
(INFO) 2018-07-06 02:35:31,441 browser._LogBrowserInfo:122    webgl2              : enabled
(INFO) 2018-07-06 02:35:31,441 browser._LogBrowserInfo:124  Driver Bug Workarounds:
(INFO) 2018-07-06 02:35:31,441 browser._LogBrowserInfo:126    add_and_true_to_loop_condition
(INFO) 2018-07-06 02:35:31,441 browser._LogBrowserInfo:126    adjust_src_dst_region_for_blitframebuffer
(INFO) 2018-07-06 02:35:31,441 browser._LogBrowserInfo:126    avoid_stencil_buffers
(INFO) 2018-07-06 02:35:31,441 browser._LogBrowserInfo:126    decode_encode_srgb_for_generatemipmap
(INFO) 2018-07-06 02:35:31,441 browser._LogBrowserInfo:126    disable_2d_canvas_auto_flush
(INFO) 2018-07-06 02:35:31,441 browser._LogBrowserInfo:126    disable_framebuffer_cmaa
(INFO) 2018-07-06 02:35:31,441 browser._LogBrowserInfo:126    disable_webgl_rgb_multisampling_usage
(INFO) 2018-07-06 02:35:31,442 browser._LogBrowserInfo:126    dont_use_loops_to_initialize_variables
(INFO) 2018-07-06 02:35:31,442 browser._LogBrowserInfo:126    emulate_abs_int_function
(INFO) 2018-07-06 02:35:31,442 browser._LogBrowserInfo:126    get_frag_data_info_bug
(INFO) 2018-07-06 02:35:31,442 browser._LogBrowserInfo:126    init_two_cube_map_levels_before_copyteximage
(INFO) 2018-07-06 02:35:31,442 browser._LogBrowserInfo:126    msaa_is_slow
(INFO) 2018-07-06 02:35:31,442 browser._LogBrowserInfo:126    pack_parameters_workaround_with_pack_buffer
(INFO) 2018-07-06 02:35:31,442 browser._LogBrowserInfo:126    rebind_transform_feedback_before_resume
(INFO) 2018-07-06 02:35:31,442 browser._LogBrowserInfo:126    regenerate_struct_names
(INFO) 2018-07-06 02:35:31,442 browser._LogBrowserInfo:126    remove_invariant_and_centroid_for_essl3
(INFO) 2018-07-06 02:35:31,442 browser._LogBrowserInfo:126    reset_teximage2d_base_level
(INFO) 2018-07-06 02:35:31,442 browser._LogBrowserInfo:126    rewrite_texelfetchoffset_to_texelfetch
(INFO) 2018-07-06 02:35:31,443 browser._LogBrowserInfo:126    scalarize_vec_and_mat_constructor_args
(INFO) 2018-07-06 02:35:31,443 browser._LogBrowserInfo:126    set_zero_level_before_generating_mipmap
(INFO) 2018-07-06 02:35:31,443 browser._LogBrowserInfo:126    unfold_short_circuit_as_ternary_operation
(INFO) 2018-07-06 02:35:31,443 browser._LogBrowserInfo:126    unpack_alignment_workaround_with_unpack_buffer
(INFO) 2018-07-06 02:35:31,443 browser._LogBrowserInfo:126    unpack_image_height_workaround_with_unpack_buffer
(INFO) 2018-07-06 02:35:31,443 browser._LogBrowserInfo:126    use_intermediary_for_copy_texture_image
(INFO) 2018-07-06 02:35:31,443 browser._LogBrowserInfo:126    use_unused_standard_shared_blocks
(INFO) 2018-07-06 02:35:31,456 ts_proxy_server._IssueCommand:97  Issuing command to ts_proxy_server: set rtt 0
(INFO) 2018-07-06 02:35:31,456 ts_proxy_server._IssueCommand:97  Issuing command to ts_proxy_server: set inkbps 0
(INFO) 2018-07-06 02:35:31,456 ts_proxy_server._IssueCommand:97  Issuing command to ts_proxy_server: set outkbps 0
(INFO) 2018-07-06 02:35:31,457 cache_temperature.EnsurePageCacheTemperature:179  PageCacheTemperature: any
(INFO) 2018-07-06 02:35:31,488 cloud_storage._GetLocked:340  Downloading gs://chromium-telemetry/62bb07f739452c44b3618b9b3f3de6251bf73322 to /b/s/w/ir/tools/perf/page_sets/login_helpers/../data/credentials.json
(INFO) 2018-07-06 02:37:27,527 chrome_tracing_agent.RecordClockSyncMarker:180  Chrome version: 3484
(INFO) 2018-07-06 02:37:35,455 trace_data.Serialize:190  Trace sizes in bytes: {'traceEvents': 165730923, 'telemetry': 118724, 'tabIds': 72}
(INFO) 2018-07-06 02:37:49,523 trace_data.Serialize:199  trace2html finished in 14.07 seconds.
(INFO) 2018-07-06 02:37:49,524 timeline_based_measurement._ComputeTimelineBasedMetrics:310  Starting to compute metrics on trace
(INFO) 2018-07-06 02:38:01,876 timeline_based_measurement._ComputeTimelineBasedMetrics:316  Processing resulting traces took 12.352 seconds
(INFO) 2018-07-06 02:38:02,001 browser.Close:207  Closing browser (pid=20832) ...
(INFO) 2018-07-06 02:38:02,145 browser.Close:220  Browser is closed.
[       OK ] memory.long_running_idle_gmail_tbmv2/https://mail.google.com/mail/ (151850 ms)
(INFO) 2018-07-06 02:38:02,176 cloud_storage.Insert:378  Uploading /b/s/w/itO80c0O/tmpx2LYTG.html to gs://chrome-telemetry-output/https___mail_google_com_mail__2018-07-06_02-35-30_81487.html
View generated trace files online at https://console.developers.google.com/m/cloudstorage/b/chrome-telemetry-output/o/https___mail_google_com_mail__2018-07-06_02-35-30_81487.html for story https://mail.google.com/mail/
(INFO) 2018-07-06 02:38:06,047 cloud_storage.Insert:378  Uploading /b/s/w/itO80c0O/tmpMD_hwstelemetry/artifacts/telemetry_test_OYVag to gs://chrome-telemetry-output/490daf97-8100-11e8-a35d-787b8ab93ad2
Uploading logs of page https://mail.google.com/mail/ to https://console.developers.google.com/m/cloudstorage/b/chrome-telemetry-output/o/490daf97-8100-11e8-a35d-787b8ab93ad2 (1 out of 1)
(INFO) 2018-07-06 02:38:07,565 memory_debug.LogHostMemoryUsage:60  Used 4.3 GiB out of 16.0 GiB memory available.
(INFO) 2018-07-06 02:38:07,566 memory_debug.LogHostMemoryUsage:61  Memory usage of top 10 processes groups
(INFO) 2018-07-06 02:38:07,579 memory_debug.LogHostMemoryUsage:77  - python2.7 - 119.3 MiB - pids: ['18011', '18016', '20818']
(INFO) 2018-07-06 02:38:07,579 memory_debug.LogHostMemoryUsage:77  - Python - 104.5 MiB - pids: ['291', '18005', '18006']
(INFO) 2018-07-06 02:38:07,579 memory_debug.LogHostMemoryUsage:77  - NotificationCenter - 46.0 MiB - pids: ['293']
(INFO) 2018-07-06 02:38:07,579 memory_debug.LogHostMemoryUsage:77  - Finder - 43.4 MiB - pids: ['254']
(INFO) 2018-07-06 02:38:07,579 memory_debug.LogHostMemoryUsage:77  - python - 36.8 MiB - pids: ['301']
(INFO) 2018-07-06 02:38:07,579 memory_debug.LogHostMemoryUsage:77  - routined - 33.1 MiB - pids: ['286']
(INFO) 2018-07-06 02:38:07,579 memory_debug.LogHostMemoryUsage:77  - suggestd - 32.9 MiB - pids: ['344']
(INFO) 2018-07-06 02:38:07,579 memory_debug.LogHostMemoryUsage:77  - SystemUIServer - 32.9 MiB - pids: ['252']
(INFO) 2018-07-06 02:38:07,579 memory_debug.LogHostMemoryUsage:77  - CalendarAgent - 32.6 MiB - pids: ['298']
(INFO) 2018-07-06 02:38:07,579 memory_debug.LogHostMemoryUsage:77  - soagent - 30.8 MiB - pids: ['307']
(INFO) 2018-07-06 02:38:07,580 memory_debug.LogHostMemoryUsage:78  Current process:
(INFO) 2018-07-06 02:38:07,580 memory_debug._LogProcessInfo:41  73.8 MiB (pid=20818)
[  PASSED  ] 1 test.

(WARNING) 2018-07-06 02:38:07,623 histogram_set_json_output_formatter.Format:27  Found existing histograms json but failed to parse it.
(WARNING) 2018-07-06 02:38:07,662 histogram_set_json_output_formatter.Format:27  Found existing histograms json but failed to parse it.
(WARNING) 2018-07-06 02:38:07,700 histogram_set_json_output_formatter.Format:27  Found existing histograms json but failed to parse it.
(WARNING) 2018-07-06 02:38:07,737 histogram_set_json_output_formatter.Format:27  Found existing histograms json but failed to parse it.
(WARNING) 2018-07-06 02:38:07,770 histogram_set_json_output_formatter.Format:27  Found existing histograms json but failed to parse it.
(WARNING) 2018-07-06 02:38:07,807 histogram_set_json_output_formatter.Format:27  Found existing histograms json but failed to parse it.
(WARNING) 2018-07-06 02:38:07,844 histogram_set_json_output_formatter.Format:27  Found existing histograms json but failed to parse it.
(WARNING) 2018-07-06 02:38:07,877 histogram_set_json_output_formatter.Format:27  Found existing histograms json but failed to parse it.
(WARNING) 2018-07-06 02:38:07,914 histogram_set_json_output_formatter.Format:27  Found existing histograms json but failed to parse it.
(WARNING) 2018-07-06 02:38:07,950 histogram_set_json_output_formatter.Format:27  Found existing histograms json but failed to parse it.
(WARNING) 2018-07-06 02:38:07,987 histogram_set_json_output_formatter.Format:27  Found existing histograms json but failed to parse it.
(WARNING) 2018-07-06 02:38:08,019 histogram_set_json_output_formatter.Format:27  Found existing histograms json but failed to parse it.
(WARNING) 2018-07-06 02:38:08,056 histogram_set_json_output_formatter.Format:27  Found existing histograms json but failed to parse it.
(WARNING) 2018-07-06 02:38:08,089 histogram_set_json_output_formatter.Format:27  Found existing histograms json but failed to parse it.
(WARNING) 2018-07-06 02:38:08,124 histogram_set_json_output_formatter.Format:27  Found existing histograms json but failed to parse it.
(WARNING) 2018-07-06 02:38:08,161 histogram_set_json_output_formatter.Format:27  Found existing histograms json but failed to parse it.
(WARNING) 2018-07-06 02:38:08,193 histogram_set_json_output_formatter.Format:27  Found existing histograms json but failed to parse it.
(WARNING) 2018-07-06 02:38:08,243 histogram_set_json_output_formatter.Format:27  Found existing histograms json but failed to parse it.
View result at file:///b/s/w/itO80c0O/tmpMD_hwstelemetry/histograms.json
View result at file:///b/s/w/itO80c0O/tmpMD_hwstelemetry/histograms.json
View result at file:///b/s/w/itO80c0O/tmpMD_hwstelemetry/histograms.json
View result at file:///b/s/w/itO80c0O/tmpMD_hwstelemetry/histograms.json
View result at file:///b/s/w/itO80c0O/tmpMD_hwstelemetry/histograms.json
View result at file:///b/s/w/itO80c0O/tmpMD_hwstelemetry/histograms.json
View result at file:///b/s/w/itO80c0O/tmpMD_hwstelemetry/histograms.json
View result at file:///b/s/w/itO80c0O/tmpMD_hwstelemetry/histograms.json
View result at file:///b/s/w/itO80c0O/tmpMD_hwstelemetry/histograms.json
View result at file:///b/s/w/itO80c0O/tmpMD_hwstelemetry/histograms.json
View result at file:///b/s/w/itO80c0O/tmpMD_hwstelemetry/histograms.json
View result at file:///b/s/w/itO80c0O/tmpMD_hwstelemetry/histograms.json
View result at file:///b/s/w/itO80c0O/tmpMD_hwstelemetry/histograms.json
View result at file:///b/s/w/itO80c0O/tmpMD_hwstelemetry/histograms.json
View result at file:///b/s/w/itO80c0O/tmpMD_hwstelemetry/histograms.json
View result at file:///b/s/w/itO80c0O/tmpMD_hwstelemetry/histograms.json
View result at file:///b/s/w/itO80c0O/tmpMD_hwstelemetry/histograms.json
View result at file:///b/s/w/itO80c0O/tmpMD_hwstelemetry/histograms.json
View result at file:///b/s/w/itO80c0O/tmpMD_hwstelemetry/histograms.json
View result at file:///b/s/w/itO80c0O/tmpMD_hwstelemetry/test-results.json
