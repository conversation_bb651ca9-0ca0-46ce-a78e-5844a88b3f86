# Copyright 2015 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

COMMON_PERF_DATA = [
  "//third_party/crossbench/.vpython3",
  "//third_party/crossbench/cb.py",
  "//third_party/crossbench/crossbench/",
  "//third_party/crossbench/config/",
]

if (!is_android) {
  group("perf") {
    testonly = true
    data = COMMON_PERF_DATA
    data_deps = [
      ":perf_without_chrome",
      "//tools/perf/chrome_telemetry_build:telemetry_chrome_test",
    ]
    if (is_linux || is_mac || is_win) {
      data_deps += [ "//chrome/test/chromedriver:chromedriver_server" ]
    }
  }
} else {
  template("perf_android_template") {
    forward_variables_from(invoker, [ "telemetry_target_suffix" ])
    group(target_name) {
      testonly = true
      data = COMMON_PERF_DATA
      data_deps = [
        ":perf_without_chrome",
        "//chrome/test/chromedriver:chromedriver_server($host_toolchain)",
        "//tools/perf/chrome_telemetry_build:telemetry_chrome_test${telemetry_target_suffix}",
      ]
    }
  }

  import("//tools/perf/chrome_telemetry_build/android_browser_types.gni")
  foreach(_target_suffix, telemetry_android_browser_target_suffixes) {
    perf_android_template("perf${_target_suffix}") {
      telemetry_target_suffix = _target_suffix
    }
  }
}

if (is_android) {
  import("//build/util/generate_wrapper.gni")

  generate_wrapper("run_benchmark_wrapper") {
    executable = "run_benchmark"
    wrapper_script = "$root_build_dir/bin/run_benchmark"
    executable_args = [
      "--chromium-output-directory",
      "@WrappedPath(.)",
    ]
  }
}

# Group for running benchmarks without building Chrome.
group("perf_without_chrome") {
  testonly = true
  data_deps = [
    "//tools/perf/chrome_telemetry_build:telemetry_chrome_test_without_chrome",
  ]

  data = [
    "//tools/perf/",
    "//.vpython3",

    # Field trial config
    "//tools/variations/",
    "//testing/variations/",

    # Field trial dependencies
    "//tools/json_comment_eater/",
    "//tools/json_to_struct/",
    "//components/variations/service/generate_ui_string_overrider.py",

    # For blink_perf benchmarks.
    "//third_party/blink/perf_tests/",

    # For Speedometer benchmarks.
    "//third_party/speedometer",

    # For Jetstream benchmarks.
    "//third_party/jetstream",

    # For smoothness.tough_canvas_cases
    "//chrome/test/data/perf/",

    # For image_decoding.measurement
    "//chrome/test/data/image_decoding/",

    # For Pylib used by VR tests
    "//build/android/pylib/",

    # For Crossbench WPR network
    "//third_party/webpagereplay/",
  ]

  # Runs a script which generates the ad tagging ruleset.
  if (!is_ios) {
    data_deps += [ "//components/subresource_filter/tools:index_ruleset" ]
  }
}

# This group makes visible those targets in subdirectories that are not
# explicitly depended on.
group("gn_all") {
  testonly = true
  deps = [ "//tools/perf/core/perfetto_binary_roller:upload_trace_processor" ]
}
