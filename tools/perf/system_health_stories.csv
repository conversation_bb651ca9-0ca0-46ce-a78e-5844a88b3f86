AUTOGENERATED FILE DO NOT EDIT
See //tools/perf/core/perf_data_generator.py to make changes
Story,Description,Platforms,Tags
background:media:imgur:2019,,mobile,2019
background:news:nytimes:2019,,mobile,"2019,javascript_heavy"
background:search:google:2019,,mobile,2019
background:social:facebook:2019,,mobile,"2019,health_check"
background:tools:gmail:2019,,mobile,"2019,health_check"
browse:chrome:newtab:2019,Story that loads new tab page and performs searches.,mobile,"2019,emerging_market"
browse:chrome:omnibox:2019,Story that peforms search by using omnibox search provider,mobile,"2019,emerging_market"
browse:media:facebook_photos:2019,Load a photo page from <PERSON><PERSON><PERSON>'s facebook page then navigate a few next,mobile,"2019,emerging_market"
browse:media:flickr_infinite_scroll:2019,,mobile,"2019,infinite_scroll"
browse:media:googleplaystore:2019,"Navigate to the movies page of Google Play Store, scroll to the bottom,",mobile,"2019,emerging_market,images"
browse:media:googleplaystore:2021,"Navigate to the movies page of Google Play Store, scroll to the bottom,",desktop,"2018,images"
browse:media:imgur,,desktop,2016
browse:media:imgur:2019,,mobile,"2019,emerging_market"
browse:media:pinterest:2018,,desktop,2018
browse:media:tiktok_infinite_scroll:2021,,mobile,"2021,infinite_scroll"
browse:media:tumblr:2018,,desktop,2018
browse:media:youtube:2019,Load a typical YouTube video then navigate to a next few videos. Stop and,"desktop,mobile","2019,emerging_market,javascript_heavy"
browse:media:youtubetv:2019,Load a typical YouTube TV video then navigate to a next few videos. Stop,desktop,2019
browse:media:youtubetv_watch:2020,Load a typical YouTube TV video then navigate to a next few videos. Stop,desktop,2020
browse:news:businessinsider:2021,A newsite where we've seen janky performance in bug reports,mobile,"2021,javascript_heavy"
browse:news:cnn:2021,The second top website in http://www.alexa.com/topsites/category/News,"desktop,mobile","2021,health_check,javascript_heavy"
browse:news:cricbuzz:2019,,mobile,"2019,emerging_market"
browse:news:flipboard:2020,,desktop,2020
browse:news:globo:2019,,mobile,"2019,emerging_market"
browse:news:hackernews:2020,,desktop,2020
browse:news:nytimes:2019,The third top website in http://www.alexa.com/topsites/category/News,mobile,2019
browse:news:nytimes:2020,The third top website in http://www.alexa.com/topsites/category/News,desktop,2020
browse:news:qq:2019,,mobile,"2019,international"
browse:news:reddit:2019,,mobile,"2019,health_check"
browse:news:reddit:2020,The top website in http://www.alexa.com/topsites/category/News,desktop,2020
browse:news:toi:2019,,mobile,"2019,emerging_market"
browse:news:washingtonpost:2019,Progressive website,mobile,2019
browse:search:amp:2018,Story for Google's Accelerated Mobile Pages (AMP).,mobile,2018
browse:search:amp:sxg:2019,Story for Google's Signed Exchange (SXG) Accelerated Mobile Pages (AMP).,mobile,2019
browse:search:google:2020,A typical google search story:,desktop,2020
browse:search:google_india:2021,A typical google search story in India:,desktop,"2021,international"
browse:shopping:amazon:2019,,mobile,"2019,emerging_market"
browse:shopping:avito:2019,,mobile,"2019,emerging_market"
browse:shopping:flipkart:2019,,mobile,"2019,emerging_market"
browse:shopping:lazada:2019,,mobile,"2019,emerging_market"
browse:social:facebook:2019,,mobile,"2019,emerging_market"
browse:social:facebook_infinite_scroll:2018,,"desktop,mobile","2018,infinite_scroll"
browse:social:instagram:2019,,mobile,"2019,emerging_market"
browse:social:pinterest_infinite_scroll:2021,,mobile,"2021,health_check,infinite_scroll"
browse:social:tumblr_infinite_scroll:2018,,"desktop,mobile","2018,health_check,infinite_scroll,javascript_heavy"
browse:social:twitter:2018,,desktop,2018
browse:social:twitter:2019,,mobile,"2019,health_check"
browse:social:twitter_infinite_scroll:2018,,desktop,"2018,infinite_scroll"
browse:tech:discourse_infinite_scroll:2018,,"desktop,mobile","2018,infinite_scroll"
browse:tools:autocad:2021,"AutoCAD desktop story,",desktop,"2021,keyboard_input,wasm,webgl"
browse:tools:docs_scrolling,Google Docs scrolling story:,desktop,"2020,javascript_heavy"
browse:tools:gmail-compose:2020,,desktop,2020
browse:tools:gmail-labelclick:2020,,desktop,2020
browse:tools:gmail-openconversation:2020,,desktop,2020
browse:tools:gmail-search:2020,,desktop,2020
browse:tools:maps:2019,Google maps story:,"desktop,mobile","2019,emerging_market,health_check,javascript_heavy,webgl"
browse:tools:photoshop:2021,"Photoshop desktop story,",desktop,"2021,wasm"
browse:tools:photoshop_warm:2021,"Photoshop desktop story, measuring warm startup,",desktop,"2021,wasm"
browse:tools:sheets:2019,,desktop,"2019,health_check,javascript_heavy"
browse_accessibility:media:youtube,Tests interacting with the YouTube home page.,desktop,"2016,accessibility,keyboard_input"
browse_accessibility:tech:codesearch:2018,Tests scrolling an element within a page.,desktop,"2018,accessibility,scroll"
load:chrome:blank,Story that loads the about:blank page.,"desktop,mobile",2016
load:games:alphabetty:2018,,desktop,2018
load:games:bubbles:2020,"Load ""smarty bubbles"" game on famobi.com","desktop,mobile",2020
load:games:lazors,,"desktop,mobile",2016
load:games:miniclip:2018,,desktop,2018
load:games:spychase:2018,,"desktop,mobile",2018
load:media:9gag,,desktop,2016
load:media:dailymotion:2019,,"desktop,mobile",2019
load:media:facebook_feed:desktop:2020,Load facebook main page,desktop,2020
load:media:facebook_feed:mobile:2020,Load a page of national park,mobile,2020
load:media:facebook_photos:2018,Load a page of rihanna's facebook with a photo.,desktop,2018
load:media:facebook_photos:2019,Load a page of rihanna's facebook with a photo.,mobile,"2019,emerging_market"
load:media:facebook_photos:desktop:2020,Load a page of rihanna's facebook with a photo.,desktop,2020
load:media:facebook_photos:mobile:2020,Load a page of rihanna's facebook with a photo.,mobile,2020
load:media:flickr:2018,,"desktop,mobile",2018
load:media:google_images:2018,,"desktop,mobile",2018
load:media:imgur:2018,,"desktop,mobile",2018
load:media:soundcloud:2018,,"desktop,mobile",2018
load:media:youtube:2018,,"desktop,mobile","2018,emerging_market,health_check"
load:media:youtubelivingroom:2020,,desktop,"2020,health_check"
load:news:bbc:2018,,desktop,2018
load:news:bbc:2019,,mobile,2019
load:news:cnn:2020,,"desktop,mobile","2020,health_check,javascript_heavy"
load:news:flipboard,,desktop,2016
load:news:hackernews:2018,,desktop,2018
load:news:irctc:2019,,mobile,"2019,emerging_market"
load:news:nytimes:2018,,desktop,2018
load:news:nytimes:2019,,mobile,2019
load:news:qq:2018,,desktop,"2018,international"
load:news:qq:2019,,mobile,"2019,international"
load:news:reddit:2018,,desktop,2018
load:news:reddit:2019,,mobile,2019
load:news:washingtonpost:2019,,mobile,2019
load:news:wikipedia:2018,,"desktop,mobile","2018,emerging_market"
load:search:amazon:2018,,desktop,2018
load:search:amazon:2019,,mobile,2019
load:search:baidu:2018,,"desktop,mobile","2018,international"
load:search:ebay:2018,,"desktop,mobile",2018
load:search:flipkart:2018,,desktop,"2018,international"
load:search:google:2018,,"desktop,mobile",2018
load:search:naver:2023,,mobile,"2023,international"
load:search:taobao:2018,,desktop,"2018,international"
load:search:taobao:2019,,mobile,"2019,international"
load:search:yahoo:2018,,"desktop,mobile",2018
load:search:yandex:2018,,"desktop,mobile","2018,international"
load:social:instagram:2018,,desktop,"2018,health_check"
load:social:instagram:2019,,mobile,2019
load:social:pinterest:2019,,"desktop,mobile","2019,javascript_heavy"
load:social:twitter:2019,,mobile,2019
load:social:vk:2018,,desktop,"2018,health_check,international"
load:tools:chat:2020,,desktop,2020
load:tools:docs:2019,Load a typical google doc page (2019).,"desktop,mobile",2019
load:tools:drive:2019,,"desktop,mobile","2019,javascript_heavy"
load:tools:dropbox:2019,,mobile,2019
load:tools:gmail:2019,,"desktop,mobile","2019,health_check"
load:tools:stackoverflow:2018,Load a typical question & answer page of stackoverflow.com,"desktop,mobile",2018
load:tools:weather:2019,,"desktop,mobile","2019,health_check,javascript_heavy"
load_accessibility:media:wikipedia:2018,"Wikipedia page on Accessibility. Long, but very simple, clean layout.",desktop,"2018,accessibility"
load_accessibility:shopping:amazon:2018,Amazon results page. Good example of a site with a data table.,desktop,"2018,accessibility"
load_accessibility:tech:codesearch:2025,web_contents_impl.cc. Good example of a very large page.,desktop,"2025,accessibility"
long_running:tools:gmail-background,,"desktop,mobile",2016
long_running:tools:gmail-foreground,,"desktop,mobile","2016,health_check"
play:media:google_play_music,"Browse the songs list in music.google.com, then play a song.",desktop,"2016,health_check"
play:media:soundcloud:2018,"Load soundcloud.com, search for ""Smooth Jazz"", then play a song.",desktop,2018
