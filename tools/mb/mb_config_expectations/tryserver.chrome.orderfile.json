{"android-arm32-orderfile": {"args_file": "//tools/pgo/override_args.gni", "gn_args": {"debuggable_apks": false, "devtools_instrumentation_dumping": true, "enable_chrome_android_internal": false, "enable_proguard_obfuscation": false, "ffmpeg_branding": "Chrome", "is_chrome_branded": true, "is_official_build": true, "proprietary_codecs": true, "symbol_level": 1, "target_cpu": "arm", "target_os": "android", "use_order_profiling": true, "use_reclient": false, "use_remoteexec": true}}, "android-arm64-orderfile": {"args_file": "//tools/pgo/override_args.gni", "gn_args": {"debuggable_apks": false, "devtools_instrumentation_dumping": true, "enable_chrome_android_internal": false, "enable_proguard_obfuscation": false, "ffmpeg_branding": "Chrome", "is_chrome_branded": true, "is_official_build": true, "proprietary_codecs": true, "symbol_level": 1, "target_cpu": "arm64", "target_os": "android", "use_order_profiling": true, "use_reclient": false, "use_remoteexec": true}}}