// Copyright 2020 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

function __f_0(__v_10, __v_11) {
    let __v_4 = 4;
    let __v_5 = 5;
    let __v_6 = 6;
    let __v_7 = 7;
    console.log(__v_4);
    console.log(__v_5);
    console.log(__v_6);
    console.log(__v_7);
    for (let __v_9 = 0; __v_9 < 10; __v_9++) {
        console.log(__v_4);
    }
    let __v_8 = 0;
    while (__v_8 < 10) {
        __v_8++;
    }
}
let __v_0 = 1;
let __v_1 = 2;
let __v_2 = 3;
let __v_3 = 4;
console.log(__v_0);
console.log(__v_1);
console.log(__v_2);
console.log(__v_3);
__f_0();
let __v_10 = __v_0;
let {prop: __v_11} = {};
