# WebVR Samples
Simple examples of using the WebVR API.

These examples are intended to demonstrate basic use of the WebVR API without framework support.
The code makes use of some simple WebGL utilities (js/wglu) and the glMatrix library to reduce
complexity of the WebGL code, but neither are necessary for using WebVR.

You can run these samples live at https://webvr.info/samples/

# Experience Prerequisites
These samples use WebGL and matrix math to demonstrate the WebVR API. While most of it is hidden
behind libraries and utility functions for brevity, it's assumed that you have at least a
conceptual knowledge of both.

# Further Reading
 - [WebVR Spec](https://mozvr.github.io/webvr-spec/)
 - [How to Get WebVR](http://webvr.info/)

# Attributions
WebVR image used with the permission of [<PERSON><PERSON><PERSON>](https://www.clicktorelease.com/).

["Afterglow of a sunset"](https://commons.wikimedia.org/wiki/File:Afterglow_of_a_sunset.jpg) image produced by the Masato OHTA and licensed under the [Creative Commons Attribution 2.0 Generic](https://creativecommons.org/licenses/by/2.0/deed.en) license

