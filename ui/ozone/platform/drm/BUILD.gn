# Copyright 2014 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/cast.gni")
import("//build/config/chromeos/ui_mode.gni")
import("//build/config/linux/pkg_config.gni")
import("//build/config/logging.gni")
import("//build/config/ozone.gni")
import("//gpu/vulkan/features.gni")
import("//testing/test.gni")
import("//ui/gl/features.gni")

visibility = [ "//ui/ozone/*" ]

source_set("gbm") {
  sources = [
    "client_native_pixmap_factory_drm.cc",
    "client_native_pixmap_factory_drm.h",
    "common/display_types.h",
    "common/drm_util.cc",
    "common/drm_util.h",
    "common/drm_wrapper.cc",
    "common/drm_wrapper.h",
    "common/hardware_display_controller_info.cc",
    "common/hardware_display_controller_info.h",
    "common/scoped_drm_types.cc",
    "common/scoped_drm_types.h",
    "common/tile_property.h",
    "gpu/crtc_commit_request.cc",
    "gpu/crtc_commit_request.h",
    "gpu/crtc_controller.cc",
    "gpu/crtc_controller.h",
    "gpu/drm_device.cc",
    "gpu/drm_device.h",
    "gpu/drm_device_generator.cc",
    "gpu/drm_device_generator.h",
    "gpu/drm_device_manager.cc",
    "gpu/drm_device_manager.h",
    "gpu/drm_display.cc",
    "gpu/drm_display.h",
    "gpu/drm_dumb_buffer.cc",
    "gpu/drm_dumb_buffer.h",
    "gpu/drm_framebuffer.cc",
    "gpu/drm_framebuffer.h",
    "gpu/drm_gpu_display_manager.cc",
    "gpu/drm_gpu_display_manager.h",
    "gpu/drm_gpu_util.cc",
    "gpu/drm_gpu_util.h",
    "gpu/drm_overlay_candidates.cc",
    "gpu/drm_overlay_candidates.h",
    "gpu/drm_overlay_manager.cc",
    "gpu/drm_overlay_manager.h",
    "gpu/drm_overlay_manager_gpu.cc",
    "gpu/drm_overlay_manager_gpu.h",
    "gpu/drm_overlay_plane.cc",
    "gpu/drm_overlay_plane.h",
    "gpu/drm_overlay_validator.cc",
    "gpu/drm_overlay_validator.h",
    "gpu/drm_thread.cc",
    "gpu/drm_thread.h",
    "gpu/drm_thread_proxy.cc",
    "gpu/drm_thread_proxy.h",
    "gpu/drm_window.cc",
    "gpu/drm_window.h",
    "gpu/drm_window_proxy.cc",
    "gpu/drm_window_proxy.h",
    "gpu/gbm_overlay_surface.cc",
    "gpu/gbm_overlay_surface.h",
    "gpu/gbm_pixmap.cc",
    "gpu/gbm_pixmap.h",
    "gpu/gbm_surface_factory.cc",
    "gpu/gbm_surface_factory.h",
    "gpu/gbm_surfaceless.cc",
    "gpu/gbm_surfaceless.h",
    "gpu/hardware_display_controller.cc",
    "gpu/hardware_display_controller.h",
    "gpu/hardware_display_plane.cc",
    "gpu/hardware_display_plane.h",
    "gpu/hardware_display_plane_atomic.cc",
    "gpu/hardware_display_plane_atomic.h",
    "gpu/hardware_display_plane_manager.cc",
    "gpu/hardware_display_plane_manager.h",
    "gpu/hardware_display_plane_manager_atomic.cc",
    "gpu/hardware_display_plane_manager_atomic.h",
    "gpu/hardware_display_plane_manager_legacy.cc",
    "gpu/hardware_display_plane_manager_legacy.h",
    "gpu/inter_thread_messaging_proxy.cc",
    "gpu/inter_thread_messaging_proxy.h",
    "gpu/page_flip_request.cc",
    "gpu/page_flip_request.h",
    "gpu/page_flip_watchdog.cc",
    "gpu/page_flip_watchdog.h",
    "gpu/proxy_helpers.cc",
    "gpu/proxy_helpers.h",
    "gpu/screen_manager.cc",
    "gpu/screen_manager.h",
    "host/drm_cursor.cc",
    "host/drm_cursor.h",
    "host/drm_device_connector.cc",
    "host/drm_device_connector.h",
    "host/drm_display_host.cc",
    "host/drm_display_host.h",
    "host/drm_display_host_manager.cc",
    "host/drm_display_host_manager.h",
    "host/drm_native_display_delegate.cc",
    "host/drm_native_display_delegate.h",
    "host/drm_window_host.cc",
    "host/drm_window_host.h",
    "host/drm_window_host_manager.cc",
    "host/drm_window_host_manager.h",
    "host/gpu_thread_adapter.h",
    "host/gpu_thread_observer.h",
    "host/host_cursor_proxy.cc",
    "host/host_cursor_proxy.h",
    "host/host_drm_device.cc",
    "host/host_drm_device.h",
    "ozone_platform_drm.cc",
    "ozone_platform_drm.h",
  ]

  deps = [
    "//base",
    "//build/config/linux/libdrm",
    "//gpu/vulkan:buildflags",
    "//ipc",
    "//media:media_buildflags",
    "//mojo/public/cpp/system",
    "//services/service_manager/public/cpp",
    "//skia",
    "//skia:skcms",
    "//third_party/angle:includes",
    "//third_party/libsync",
    "//third_party/minigbm",
    "//ui/base",
    "//ui/base/cursor",
    "//ui/base/cursor/mojom:cursor_type",
    "//ui/base/ime",
    "//ui/display",
    "//ui/display/types",
    "//ui/display/util",
    "//ui/events",
    "//ui/events/devices",
    "//ui/events/ozone",
    "//ui/events/ozone/evdev",
    "//ui/events/ozone/layout",
    "//ui/events/platform",
    "//ui/gfx",
    "//ui/gfx/geometry",
    "//ui/gfx/linux:drm",
    "//ui/gfx/linux:gbm",
    "//ui/gl",
    "//ui/ozone:ozone_base",
    "//ui/ozone/common",
    "//ui/ozone/platform/drm/mojom",
    "//ui/platform_window",
  ]

  if (!is_castos && !use_static_angle) {
    data_deps = [
      "//third_party/angle:libEGL",
      "//third_party/angle:libGLESv2",
    ]
  }

  if (is_chromeos) {
    deps += [
      "//ash/constants",
      "//ui/base/ime/ash",
    ]
  }

  if (enable_vulkan) {
    sources += [
      "gpu/vulkan_implementation_gbm.cc",
      "gpu/vulkan_implementation_gbm.h",
    ]
    deps += [ "//gpu/vulkan" ]
  }

  defines = [
    "OZONE_IMPLEMENTATION",

    # Enable VLOG(1) so that they are included in field feedbacks.
    "ENABLED_VLOG_LEVEL=1",
  ]
}

source_set("gbm_unittests") {
  testonly = true
  sources = [
    "common/drm_util_unittest.cc",
    "common/hardware_display_controller_info_unittest.cc",
    "gpu/drm_display_unittest.cc",
    "gpu/drm_gpu_display_manager_unittest.cc",
    "gpu/drm_gpu_util_unittest.cc",
    "gpu/drm_overlay_manager_unittest.cc",
    "gpu/drm_overlay_validator_unittest.cc",
    "gpu/drm_thread_unittest.cc",
    "gpu/drm_window_unittest.cc",
    "gpu/fake_drm_device.cc",
    "gpu/fake_drm_device.h",
    "gpu/fake_drm_device_generator.cc",
    "gpu/fake_drm_device_generator.h",
    "gpu/hardware_display_controller_unittest.cc",
    "gpu/hardware_display_plane_manager_unittest.cc",
    "gpu/mock_drm_device.cc",
    "gpu/mock_drm_device.h",
    "gpu/mock_drm_modifiers_filter.cc",
    "gpu/mock_drm_modifiers_filter.h",
    "gpu/proxy_helpers_unittest.cc",
    "gpu/screen_manager_unittest.cc",
  ]

  deps = [
    ":gbm",
    "//base/test:test_support",
    "//skia",
    "//testing/gtest",

    # We're using this instead of the config to ensure that our tests built for
    # linux have a controllable dependency instead of relying on whatever is on
    # the system.
    "//third_party/libdrm",
    "//ui/base/ime",
    "//ui/display:test_support",
    "//ui/gfx",
    "//ui/gfx/linux:drm",
    "//ui/gfx/linux:gbm",
    "//ui/gfx/linux:test_support",
    "//ui/ozone:platform",
    "//ui/ozone/common",
  ]
}

source_set("drm_integration_tests") {
  testonly = true

  sources = [
    "test/integration_test_helpers.cc",
    "test/integration_test_helpers.h",
    "test/vkms_tests.cc",
  ]

  deps = [
    ":gbm",
    "//base/test:test_support",
    "//build/config/linux/libdrm",
    "//skia",
    "//ui/display/types:types",
    "//ui/gfx/linux:gbm",
    "//ui/gfx/linux:test_support",
    "//ui/ozone:platform",
    "//ui/ozone/common",
  ]
}

source_set("ui_controls") {
  testonly = true

  sources = [
    "test/ui_controls_system_input_injector.cc",
    "test/ui_controls_system_input_injector.h",
  ]

  deps = [
    "//base/test:test_support",
    "//ui/aura:test_support",
    "//ui/base:test_support",
    "//ui/ozone:platform",
    "//ui/ozone/common",
  ]

  visibility += [ "//chrome/test:test_support" ]
}
