/* Copyright 2023 The Chromium Authors
 * Use of this source code is governed by a BSD_style license that can be
 * found in the LICENSE file. */

/*
 * Chrome OS typography styles for GM3.
 */
{
  "options": {
    "CSS": {
      "prefix": "cros"
    }
  },
  "typography": {
    "font_families": {
      font_family_google_sans_regular: "'GSR', 'Google Sans', 'Roboto', sans-serif",
      font_family_google_sans_medium: "'GSM', 'Google Sans', 'Roboto', sans-serif",
      font_family_google_sans_bold: "'GSB', 'Google Sans', 'Roboto', sans-serif",
      font_family_google_sans_text_regular: "'GSTR', 'Google Sans', 'Roboto', sans-serif",
      font_family_google_sans_text_medium: "'GSTM', 'Google Sans', 'Roboto', sans-serif",
      font_family_google_sans_text_bold: "'GSTB', 'Google Sans', 'Roboto', sans-serif",
    },
    "font_faces": {
      face_gsr: {family: "GSR", local_src: "Google Sans Regular"},
      face_gsm: {family: "GSM", local_src: "Google Sans Medium"},
      face_gsb: {family: "GSB", local_src: "Google Sans Bold"},
      face_gstr: {family: "GSTR", local_src: "Google Sans Text Regular"},
      face_gstm: {family: "GSTM", local_src: "Google Sans Text Medium"},
      face_gstb: {family: "GSTB", local_src: "Google Sans Text Bold"},
    },
    "typefaces": {
      "display_0": {
        "font_family": '$font_family_google_sans_medium',
        "font_size": 52,
        "font_weight": 500,
        "line_height": 60
      },
      "display_0-regular": {
        "font_family": '$font_family_google_sans_regular',
        "font_size": 52,
        "font_weight": 400,
        "line_height": 60
      },
      "display_1": {
        "font_family": '$font_family_google_sans_medium',
        "font_size": 44,
        "font_weight": 500,
        "line_height": 52
      },
      "display_2": {
        "font_family": '$font_family_google_sans_medium',
        "font_size": 36,
        "font_weight": 500,
        "line_height": 44
      },
      "display_3": {
        "font_family": '$font_family_google_sans_medium',
        "font_size": 32,
        "font_weight": 500,
        "line_height": 40
      },
      "display_3-regular": {
        "font_family": '$font_family_google_sans_regular',
        "font_size": 32,
        "font_weight": 400,
        "line_height": 40
      },
      "display_4": {
        "font_family": '$font_family_google_sans_medium',
        "font_size": 28,
        "font_weight": 500,
        "line_height": 36
      },
      "display_5": {
        "font_family": '$font_family_google_sans_medium',
        "font_size": 24,
        "font_weight": 500,
        "line_height": 32
      },
      "display_6": {
        "font_family": '$font_family_google_sans_medium',
        "font_size": 22,
        "font_weight": 500,
        "line_height": 28
      },
      "display_6-regular": {
        "font_family": '$font_family_google_sans_regular',
        "font_size": 22,
        "font_weight": 400,
        "line_height": 28
      },
      "display_7": {
        "font_family": '$font_family_google_sans_medium',
        "font_size": 18,
        "font_weight": 500,
        "line_height": 24
      },
      "title_1": {
        "font_family": '$font_family_google_sans_text_medium',
        "font_size": 16,
        "font_weight": 500,
        "line_height": 24
      },
      "title_2": {
        "font_family": '$font_family_google_sans_text_bold',
        "font_size": 13,
        "font_weight": 700,
        "line_height": 20
      },
      "headline_1": {
        "font_family": '$font_family_google_sans_text_medium',
        "font_size": 15,
        "font_weight": 500,
        "line_height": 22
      },
      "button_1": {
        "font_family": '$font_family_google_sans_text_medium',
        "font_size": 14,
        "font_weight": 500,
        "line_height": 20
      },
      "button_2": {
        "font_family": '$font_family_google_sans_text_medium',
        "font_size": 13,
        "font_weight": 500,
        "line_height": 20
      },
      "body_0": {
        "font_family": '$font_family_google_sans_text_regular',
        "font_size": 16,
        "font_weight": 400,
        "line_height": 24
      },
      "body_1": {
        "font_family": '$font_family_google_sans_text_regular',
        "font_size": 14,
        "font_weight": 400,
        "line_height": 20
      },
      "body_2": {
        "font_family": '$font_family_google_sans_text_regular',
        "font_size": 13,
        "font_weight": 400,
        "line_height": 20
      },
      "annotation_1": {
        "font_family": '$font_family_google_sans_text_regular',
        "font_size": 12,
        "font_weight": 400,
        "line_height": 18
      },
      "annotation_2": {
        "font_family": '$font_family_google_sans_text_regular',
        "font_size": 11,
        "font_weight": 400,
        "line_height": 16
      },
      "label_1": {
        "font_family": '$font_family_google_sans_text_medium',
        "font_size": 10,
        "font_weight": 500,
        "line_height": 10
      },
      "label_2": {
        "font_family": '$font_family_google_sans_text_regular',
        "font_size": 10,
        "font_weight": 400,
        "line_height": 10
      }
    }
  }
}
