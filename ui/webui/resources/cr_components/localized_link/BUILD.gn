# Copyright 2023 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//ui/webui/resources/tools/build_webui.gni")

assert(!is_android && !is_ios)

build_webui("build") {
  grd_prefix = "cr_components_localized_link"

  ts_files = [
    "localized_link.html.ts",
    "localized_link.ts",
  ]

  css_files = [ "localized_link.css" ]

  ts_out_dir =
      "$root_gen_dir/ui/webui/resources/tsc/cr_components/localized_link"
  ts_composite = true
  ts_deps = [
    "//third_party/lit/v3_0:build_ts",
    "//ui/webui/resources/cr_elements:build_ts",
    "//ui/webui/resources/js:build_ts",
  ]
  webui_context_type = "relative"
  generate_grdp = true
  grd_resource_path_prefix = rebase_path(".", "//ui/webui/resources")
}
