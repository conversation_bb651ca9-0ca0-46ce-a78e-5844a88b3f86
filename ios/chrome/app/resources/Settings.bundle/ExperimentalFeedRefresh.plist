<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>PreferenceSpecifiers</key>
	<array>
		<dict>
			<key>Title</key>
			<string>Refresh Timestamps</string>
			<key>Type</key>
			<string>PSGroupSpecifier</string>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSTitleValueSpecifier</string>
			<key>Title</key>
			<string>feedModel: didBeginReload:</string>
			<key>Key</key>
			<string>FeedModelDidBeginReloadTimestamp</string>
			<key>DefaultValue</key>
			<string></string>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSTitleValueSpecifier</string>
			<key>Title</key>
			<string>refresh FeedModel:</string>
			<key>Key</key>
			<string>RefreshFeedModelTimestamp</string>
			<key>DefaultValue</key>
			<string></string>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSTitleValueSpecifier</string>
			<key>Title</key>
			<string>Forced foreground</string>
			<key>Key</key>
			<string>FeedLastForcedForegroundRefreshTimestamp</string>
			<key>DefaultValue</key>
			<string></string>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSTitleValueSpecifier</string>
			<key>Title</key>
			<string>Non forced foreground</string>
			<key>Key</key>
			<string>FeedLastNonForcedForegroundRefreshTimestamp</string>
			<key>DefaultValue</key>
			<string></string>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSTitleValueSpecifier</string>
			<key>Title</key>
			<string>Visible foreground</string>
			<key>Key</key>
			<string>FeedLastVisibleForegroundRefreshTimestamp</string>
			<key>DefaultValue</key>
			<string></string>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSTitleValueSpecifier</string>
			<key>Title</key>
			<string>Non visible foreground</string>
			<key>Key</key>
			<string>FeedLastNonVisibleForegroundRefreshTimestamp</string>
			<key>DefaultValue</key>
			<string></string>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSTitleValueSpecifier</string>
			<key>Title</key>
			<string>Background refresh</string>
			<key>Key</key>
			<string>FeedLastBackgroundRefreshTimestamp</string>
			<key>DefaultValue</key>
			<string></string>
		</dict>
		<dict>
			<key>Title</key>
			<string>Background Refresh</string>
			<key>Type</key>
			<string>PSGroupSpecifier</string>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
			<key>Title</key>
			<string>Notify on successful background refresh</string>
			<key>Key</key>
			<string>FeedBackgroundRefreshNotificationEnabled</string>
			<key>DefaultValue</key>
			<false/>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
			<key>Title</key>
			<string>Use overrides below</string>
			<key>Key</key>
			<string>FeedOverrideDefaultsEnabled</string>
			<key>DefaultValue</key>
			<false/>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSTextFieldSpecifier</string>
			<key>Title</key>
			<string>Max cache age in seconds</string>
			<key>Key</key>
			<string>FeedMaxCacheAgeInSeconds</string>
			<key>DefaultValue</key>
			<string></string>
			<key>KeyboardType</key>
			<string>NumberPad</string>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSTextFieldSpecifier</string>
			<key>Title</key>
			<string>Refresh interval in seconds</string>
			<key>Key</key>
			<string>FeedBackgroundRefreshIntervalInSeconds</string>
			<key>DefaultValue</key>
			<string></string>
			<key>KeyboardType</key>
			<string>NumberPad</string>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
			<key>Title</key>
			<string>Enable Following Feed background refresh</string>
			<key>Key</key>
			<string>FollowingFeedBackgroundRefreshEnabled</string>
			<key>DefaultValue</key>
			<false/>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
			<key>Title</key>
			<string>Enable server-driven schedule</string>
			<key>Key</key>
			<string>FeedServerDrivenBackgroundRefreshScheduleEnabled</string>
			<key>DefaultValue</key>
			<false/>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
			<key>Title</key>
			<string>Enable recurring schedule</string>
			<key>Key</key>
			<string>FeedRecurringBackgroundRefreshScheduleEnabled</string>
			<key>DefaultValue</key>
			<false/>
		</dict>
	</array>
</dict>
</plist>
