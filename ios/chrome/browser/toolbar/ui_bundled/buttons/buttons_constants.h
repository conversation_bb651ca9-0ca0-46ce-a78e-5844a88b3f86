// Copyright 2024 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#ifndef IOS_CHROME_BROWSER_TOOLBAR_UI_BUNDLED_BUTTONS_BUTTONS_CONSTANTS_H_
#define IOS_CHROME_BROWSER_TOOLBAR_UI_BUNDLED_BUTTONS_BUTTONS_CONSTANTS_H_

#import <Foundation/Foundation.h>

// The accessibility ID of the blue dot view for the toolbar button.
extern NSString* const kToolbarButtonBlueDotViewID;

#endif  // IOS_CHROME_BROWSER_TOOLBAR_UI_BUNDLED_BUTTONS_BUTTONS_CONSTANTS_H_
