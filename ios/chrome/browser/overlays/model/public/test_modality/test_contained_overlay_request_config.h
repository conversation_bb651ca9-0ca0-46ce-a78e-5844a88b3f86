// Copyright 2020 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#ifndef IOS_CHROME_BROWSER_OVERLAYS_MODEL_PUBLIC_TEST_MODALITY_TEST_CONTAINED_OVERLAY_REQUEST_CONFIG_H_
#define IOS_CHROME_BROWSER_OVERLAYS_MODEL_PUBLIC_TEST_MODALITY_TEST_CONTAINED_OVERLAY_REQUEST_CONFIG_H_

#include "ios/chrome/browser/overlays/model/public/overlay_request_config.h"

// An OverlayRequestConfig to use in tests for contained UIViewControllers.
DEFINE_STATELESS_OVERLAY_REQUEST_CONFIG(TestContainedOverlay);

#endif  // IOS_CHROME_BROWSER_OVERLAYS_MODEL_PUBLIC_TEST_MODALITY_TEST_CONTAINED_OVERLAY_REQUEST_CONFIG_H_
