// Copyright 2022 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#ifndef IOS_CHROME_BROWSER_OVERLAYS_UI_BUNDLED_INFOBAR_BANNER_SYNC_ERROR_SYNC_ERROR_INFOBAR_BANNER_OVERLAY_MEDIATOR_H_
#define IOS_CHROME_BROWSER_OVERLAYS_UI_BUNDLED_INFOBAR_BANNER_SYNC_ERROR_SYNC_ERROR_INFOBAR_BANNER_OVERLAY_MEDIATOR_H_

#import "ios/chrome/browser/overlays/ui_bundled/infobar_banner/infobar_banner_overlay_mediator.h"

// Mediator that configures an infobar banner for a sync error infobar.
@interface SyncErrorInfobarBannerOverlayMediator : InfobarBannerOverlayMediator
@end

#endif  // IOS_CHROME_BROWSER_OVERLAYS_UI_BUNDLED_INFOBAR_BANNER_SYNC_ERROR_SYNC_ERROR_INFOBAR_BANNER_OVERLAY_MEDIATOR_H_
