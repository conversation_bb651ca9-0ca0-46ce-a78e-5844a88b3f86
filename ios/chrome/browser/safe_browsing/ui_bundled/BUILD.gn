# Copyright 2022 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

source_set("ui_bundled") {
  sources = [
    "safe_browsing_coordinator.h",
    "safe_browsing_coordinator.mm",
  ]
  deps = [
    "//components/safe_browsing/core/common",
    "//ios/chrome/browser/infobars/model",
    "//ios/chrome/browser/safe_browsing/model",
    "//ios/chrome/browser/safe_browsing/model:infobar_delegate",
    "//ios/chrome/browser/settings/ui_bundled/privacy:ui",
    "//ios/chrome/browser/shared/coordinator/chrome_coordinator",
    "//ios/chrome/browser/shared/model/browser",
    "//ios/chrome/browser/shared/model/web_state_list",
    "//ios/chrome/browser/shared/public/commands",
    "//ios/components/security_interstitials/safe_browsing",
  ]
}
