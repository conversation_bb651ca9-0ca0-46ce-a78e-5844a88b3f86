// Copyright 2023 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#import "ios/chrome/browser/account_picker/ui_bundled/account_picker_confirmation/account_picker_confirmation_screen_constants.h"

NSString* const kAccountPickerPrimaryButtonAccessibilityIdentifier =
    @"AccountPickerPrimaryButtonAccessibilityIdentifier";

NSString* const kAccountPickerCancelButtonAccessibilityIdentifier =
    @"AccountPickerCancelButtonAccessibilityIdentifier";
