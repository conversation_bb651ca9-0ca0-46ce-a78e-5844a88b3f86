# Copyright 2024 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

source_set("coordinator") {
  sources = [
    "docking_promo_coordinator.h",
    "docking_promo_coordinator.mm",
    "docking_promo_mediator.h",
    "docking_promo_mediator.mm",
  ]
  deps = [
    "//components/feature_engagement/public",
    "//components/prefs",
    "//ios/chrome/app/application_delegate:app_state",
    "//ios/chrome/app/profile",
    "//ios/chrome/app/strings",
    "//ios/chrome/browser/default_browser/model:utils",
    "//ios/chrome/browser/docking_promo/model:utils",
    "//ios/chrome/browser/docking_promo/resources",
    "//ios/chrome/browser/docking_promo/ui",
    "//ios/chrome/browser/feature_engagement/model",
    "//ios/chrome/browser/first_run/ui_bundled:screen_delegate",
    "//ios/chrome/browser/promos_manager/model",
    "//ios/chrome/browser/promos_manager/model:constants",
    "//ios/chrome/browser/promos_manager/model:factory",
    "//ios/chrome/browser/promos_manager/ui_bundled:promos",
    "//ios/chrome/browser/shared/coordinator/chrome_coordinator",
    "//ios/chrome/browser/shared/coordinator/scene:scene_state_header",
    "//ios/chrome/browser/shared/model/application_context",
    "//ios/chrome/browser/shared/model/browser",
    "//ios/chrome/browser/shared/model/prefs:pref_names",
    "//ios/chrome/browser/shared/public/commands",
    "//ios/chrome/browser/shared/public/features",
    "//ios/chrome/browser/shared/ui/util",
    "//ios/chrome/browser/start_surface/ui_bundled",
    "//ios/chrome/common/ui/confirmation_alert",
    "//ui/base",
  ]
  frameworks = [
    "UIKit.framework",
    "Foundation.framework",
  ]
}

source_set("unit_tests") {
  testonly = true
  sources = [ "docking_promo_mediator_unittest.mm" ]
  deps = [
    ":coordinator",
    "//base/test:test_support",
    "//components/prefs:test_support",
    "//components/startup_metric_utils",
    "//ios/chrome/app/application_delegate:app_state",
    "//ios/chrome/app/strings",
    "//ios/chrome/browser/default_browser/model:test_support",
    "//ios/chrome/browser/docking_promo/ui",
    "//ios/chrome/browser/first_run/model",
    "//ios/chrome/browser/promos_manager/model:test_support",
    "//ios/chrome/browser/shared/coordinator/scene:scene_state_header",
    "//ios/chrome/browser/shared/coordinator/scene/test",
    "//ios/chrome/browser/shared/model/prefs:pref_names",
    "//ios/chrome/browser/shared/public/commands",
    "//ios/chrome/browser/shared/public/features",
    "//ios/chrome/test:test_support",
    "//testing/gtest",
    "//third_party/ocmock",
    "//ui/base",
  ]
}
