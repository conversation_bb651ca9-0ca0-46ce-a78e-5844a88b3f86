# Copyright 2025 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

source_set("eg2_tests") {
  configs += [ "//build/config/ios:xctest_config" ]
  testonly = true
  sources = [ "forced_signin_egtest.mm" ]
  deps = [
    "//base",
    "//base/test:test_support",
    "//build:branding_buildflags",
    "//components/policy:policy_code_generate",
    "//components/policy/core/common:common_constants",
    "//components/signin/ios/browser:features",
    "//components/signin/public/base",
    "//components/sync/base:features",
    "//ios/chrome/app/strings",
    "//ios/chrome/browser/authentication/ui_bundled:eg_test_support+eg2",
    "//ios/chrome/browser/authentication/ui_bundled/account_menu:constants",
    "//ios/chrome/browser/authentication/ui_bundled/signin:constants",
    "//ios/chrome/browser/authentication/ui_bundled/views:views_constants",
    "//ios/chrome/browser/first_run/ui_bundled:constants",
    "//ios/chrome/browser/first_run/ui_bundled:eg_test_support+eg2",
    "//ios/chrome/browser/metrics/model:eg_test_support+eg2",
    "//ios/chrome/browser/ntp/ui_bundled:constants",
    "//ios/chrome/browser/ntp/ui_bundled:feature_flags",
    "//ios/chrome/browser/policy/model:eg_test_support+eg2",
    "//ios/chrome/browser/policy/model:policy_util",
    "//ios/chrome/browser/settings/ui_bundled:eg_test_support+eg2",
    "//ios/chrome/browser/settings/ui_bundled/google_services:constants",
    "//ios/chrome/browser/settings/ui_bundled/google_services/manage_accounts:constants",
    "//ios/chrome/browser/shared/model/prefs:pref_names",
    "//ios/chrome/browser/shared/public/features",
    "//ios/chrome/browser/signin/model:fake_system_identity",
    "//ios/chrome/browser/signin/model:test_constants",
    "//ios/chrome/common:string_util",
    "//ios/chrome/common/ui/promo_style:constants",
    "//ios/chrome/test/earl_grey:eg_test_support+eg2",
    "//ios/testing/earl_grey:eg_test_support+eg2",
    "//net",
    "//net:test_support",
    "//ui/base",
  ]
  frameworks = [ "UIKit.framework" ]
}
