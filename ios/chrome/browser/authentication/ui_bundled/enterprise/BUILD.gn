# Copyright 2021 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

source_set("enterprise_utils") {
  sources = [
    "enterprise_utils.h",
    "enterprise_utils.mm",
  ]
  deps = [
    "//base",
    "//components/policy:policy_code_generate",
    "//components/prefs",
    "//components/signin/public/base",
    "//components/sync/base",
    "//components/sync/service",
    "//ios/chrome/browser/policy/model:policy_util",
    "//ios/chrome/browser/shared/model/application_context",
    "//ios/chrome/browser/shared/model/prefs:pref_names",
    "//ios/chrome/browser/signin/model:system_identity",
    "//ios/chrome/browser/signin/model:system_identity_manager",
    "//ios/chrome/browser/sync/model",
  ]
}
