// Copyright 2021 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#import "ios/chrome/browser/context_menu/ui_bundled/link_preview/link_preview_constants.h"

NSString* const kPreviewURLBarIdentifier = @"kPreviewURLBarIdentifier";
NSString* const kPreviewOriginIdentifier = @"kPreviewOriginIdentifier";
NSString* const kPreviewProgressBarIdentifier =
    @"kPreviewProgressBarIdentifier";
NSString* const kPreviewWebStateViewIdentifier =
    @"kPreviewWebStateViewIdentifier";
