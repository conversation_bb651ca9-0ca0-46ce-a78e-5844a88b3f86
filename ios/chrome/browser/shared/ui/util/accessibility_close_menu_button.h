// Copyright 2012 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#ifndef IOS_CHROME_BROWSER_SHARED_UI_UTIL_ACCESSIBILITY_CLOSE_MENU_BUTTON_H_
#define IOS_CHROME_BROWSER_SHARED_UI_UTIL_ACCESSIBILITY_CLOSE_MENU_BUTTON_H_

#import <UIKit/UIKit.h>

// UIButton allowing the user to close the menu with VoiceOver.
@interface AccessibilityCloseMenuButton : UIButton
@end

#endif  // IOS_CHROME_BROWSER_SHARED_UI_UTIL_ACCESSIBILITY_CLOSE_MENU_BUTTON_H_
