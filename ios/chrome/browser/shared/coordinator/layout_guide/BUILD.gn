# Copyright 2023 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

source_set("layout_guide_scene_agent") {
  sources = [
    "layout_guide_scene_agent.h",
    "layout_guide_scene_agent.mm",
  ]
  public_deps = [ "//base" ]
  deps = [
    "//ios/chrome/browser/shared/coordinator/scene:observing_scene_agent",
    "//ios/chrome/browser/shared/ui/util:util_swift",
  ]
  frameworks = [ "UIKit.framework" ]
}

source_set("layout_guide") {
  sources = [
    "layout_guide_util.h",
    "layout_guide_util.mm",
  ]
  deps = [
    ":layout_guide_scene_agent",
    "//ios/chrome/browser/shared/coordinator/scene:scene_state_header",
    "//ios/chrome/browser/shared/model/profile",
    "//ios/chrome/browser/shared/ui/util:util_swift",
  ]
}

source_set("unit_tests") {
  testonly = true
  sources = [ "layout_guide_scene_agent_unittest.mm" ]
  deps = [
    ":layout_guide_scene_agent",
    "//testing/gtest",
  ]
}
