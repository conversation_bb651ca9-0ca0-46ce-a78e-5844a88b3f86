// Copyright 2023 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#ifndef IOS_CHROME_BROWSER_WHATS_NEW_PUBLIC_CONSTANTS_H_
#define IOS_CHROME_BROWSER_WHATS_NEW_PUBLIC_CONSTANTS_H_

@class NSString;

// Key to store whether a user interacted with What's New M116 from the overflow
// menu.
extern NSString* const kWhatsNewM116UsageEntryKey;

#endif  // IOS_CHROME_BROWSER_WHATS_NEW_PUBLIC_CONSTANTS_H_
