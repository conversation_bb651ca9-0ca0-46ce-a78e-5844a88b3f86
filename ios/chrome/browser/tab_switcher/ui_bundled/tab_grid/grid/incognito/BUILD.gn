# Copyright 2023 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

source_set("incognito") {
  sources = [
    "incognito_grid_coordinator.h",
    "incognito_grid_coordinator.mm",
    "incognito_grid_mediator.h",
    "incognito_grid_mediator.mm",
  ]

  deps = [
    ":incognito_grid_mediator_delegate",
    ":incognito_ui",
    "//base",
    "//components/feature_engagement/public",
    "//components/prefs",
    "//components/prefs/ios",
    "//components/signin/public/identity_manager",
    "//components/supervised_user/core/browser",
    "//components/supervised_user/core/common",
    "//components/supervised_user/core/common:features",
    "//ios/chrome/browser/feature_engagement/model",
    "//ios/chrome/browser/incognito_reauth/ui_bundled:features",
    "//ios/chrome/browser/incognito_reauth/ui_bundled:incognito_reauth_scene_agent",
    "//ios/chrome/browser/incognito_reauth/ui_bundled:incognito_reauth_util",
    "//ios/chrome/browser/policy/model:policy_util",
    "//ios/chrome/browser/shared/coordinator/chrome_coordinator",
    "//ios/chrome/browser/shared/model/browser",
    "//ios/chrome/browser/shared/model/profile",
    "//ios/chrome/browser/shared/model/web_state_list",
    "//ios/chrome/browser/shared/public/commands",
    "//ios/chrome/browser/shared/public/features",
    "//ios/chrome/browser/snapshots/model",
    "//ios/chrome/browser/supervised_user/model:capabilities",
    "//ios/chrome/browser/tab_switcher/ui_bundled/tab_grid:tab_grid_idle_status_handler",
    "//ios/chrome/browser/tab_switcher/ui_bundled/tab_grid:tab_grid_metrics",
    "//ios/chrome/browser/tab_switcher/ui_bundled/tab_grid:tab_grid_mode",
    "//ios/chrome/browser/tab_switcher/ui_bundled/tab_grid:tab_grid_paging",
    "//ios/chrome/browser/tab_switcher/ui_bundled/tab_grid:tab_grid_ui",
    "//ios/chrome/browser/tab_switcher/ui_bundled/tab_grid:tab_grid_ui_constants",
    "//ios/chrome/browser/tab_switcher/ui_bundled/tab_grid/grid",
    "//ios/chrome/browser/tab_switcher/ui_bundled/tab_grid/grid:grid_consumer",
    "//ios/chrome/browser/tab_switcher/ui_bundled/tab_grid/grid:grid_mediator",
    "//ios/chrome/browser/tab_switcher/ui_bundled/tab_grid/grid:grid_toolbars_mutator",
    "//ios/chrome/browser/tab_switcher/ui_bundled/tab_grid/grid:grid_ui",
    "//ios/chrome/browser/tab_switcher/ui_bundled/tab_grid/grid:tab_group_grid_ui",
    "//ios/chrome/browser/tab_switcher/ui_bundled/tab_grid/tab_context_menu",
    "//ios/chrome/browser/tab_switcher/ui_bundled/tab_grid/tab_groups",
    "//ios/chrome/browser/tab_switcher/ui_bundled/tab_grid/tab_groups:tab_groups_ui",
    "//ios/chrome/browser/tab_switcher/ui_bundled/tab_grid/toolbars",
    "//ios/web/public",
  ]
}

source_set("incognito_ui") {
  sources = [
    "incognito_grid_view_controller.h",
    "incognito_grid_view_controller.mm",
  ]

  deps = [
    "//base",
    "//ios/chrome/browser/incognito_reauth/ui_bundled:features",
    "//ios/chrome/browser/incognito_reauth/ui_bundled:incognito_reauth_commands",
    "//ios/chrome/browser/incognito_reauth/ui_bundled:incognito_reauth_util",
    "//ios/chrome/browser/incognito_reauth/ui_bundled:ui",
    "//ios/chrome/browser/shared/public/commands",
    "//ios/chrome/browser/tab_switcher/ui_bundled/tab_grid/grid:grid_ui",
    "//ios/chrome/browser/tabs/model/inactive_tabs:features",
    "//ios/chrome/common:timing",
    "//ios/chrome/common/ui/util",
  ]
}

source_set("unit_tests") {
  testonly = true

  sources = [ "incognito_grid_mediator_unittest.mm" ]

  deps = [
    ":incognito",
    "//components/policy/core/common:common_constants",
    "//components/sync_preferences:test_support",
    "//ios/chrome/browser/policy/model:policy_util",
    "//ios/chrome/browser/shared/model/browser/test:test_support",
    "//ios/chrome/browser/shared/model/profile/test",
    "//ios/chrome/browser/shared/model/web_state_list",
    "//ios/chrome/browser/tab_switcher/ui_bundled/tab_grid:tab_grid_mode",
    "//ios/chrome/browser/tab_switcher/ui_bundled/tab_grid/grid:test_fixture",
    "//ios/chrome/browser/tab_switcher/ui_bundled/tab_grid/toolbars",
    "//ios/chrome/browser/tab_switcher/ui_bundled/tab_grid/toolbars/test:fakes",
    "//ios/chrome/browser/tab_switcher/ui_bundled/test:fakes",
    "//ios/web/public",
  ]
}

source_set("incognito_grid_mediator_delegate") {
  sources = [ "incognito_grid_mediator_delegate.h" ]

  deps = [ "//base" ]
}
