# Copyright 2023 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

source_set("widget_promo_instructions") {
  sources = [
    "widget_promo_instructions_coordinator.h",
    "widget_promo_instructions_coordinator.mm",
  ]
  deps = [
    ":widget_promo_instructions_ui",
    "//base",
    "//ios/chrome/browser/settings/ui_bundled:settings_root",
    "//ios/chrome/browser/settings/ui_bundled/password/reauthentication",
    "//ios/chrome/browser/settings/ui_bundled/password/reauthentication:reauthentication_delegate",
    "//ios/chrome/browser/shared/coordinator/chrome_coordinator",
    "//ios/chrome/common/ui/confirmation_alert",
  ]
}

source_set("widget_promo_instructions_ui") {
  sources = [
    "widget_promo_instructions_view_controller.h",
    "widget_promo_instructions_view_controller.mm",
  ]
  deps = [
    ":widget_promo_instructions_constants",
    "//components/strings",
    "//ios/chrome/app/strings",
    "//ios/chrome/browser/settings/ui_bundled/password/widget_promo_instructions/resources",
    "//ios/chrome/browser/shared/ui/util",
    "//ios/chrome/common/ui/colors",
    "//ios/chrome/common/ui/confirmation_alert",
    "//ios/chrome/common/ui/instruction_view",
    "//ui/base",
  ]
}

source_set("widget_promo_instructions_constants") {
  sources = [
    "widget_promo_instructions_constants.h",
    "widget_promo_instructions_constants.mm",
  ]
  deps = []
}
