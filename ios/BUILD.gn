# Copyright 2016 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

assert(!is_component_build, "component build is unsupported on iOS")

import("//build/config/cronet/config.gni")
import("//build/config/gclient_args.gni")
import("//build/config/ios/ios_sdk.gni")
import("//ios/features.gni")

# This list all targets that needs to be build as part of "gn_all" on iOS.
# This list should generally only include executables, but since some code
# has not yet been upstreamed it will sometimes also include targets that
# are not used upstream to ensure they are not broken inadvertently.
group("all") {
  testonly = true
  if (is_webkit_only_build) {
    deps = [ "//ios/third_party/webkit" ]
  } else if (ios_build_chrome) {
    deps = [
      # List all the targets that need to be built on iOS by default.
      "//ios/chrome/app:chrome",
      "//ios/web/shell:ios_web_shell",
      "//ios/web_view/shell:ios_web_view_shell",

      # List all the test targets that need to be built on iOS by default.
      "//ios/chrome/test:all_tests",
      "//ios/chrome/test/earl_grey2:all_tests",
      "//ios/chrome/test/wpt:all_tests",
      "//ios/components:all_tests",
      "//ios/net:all_tests",
      "//ios/testing:all_tests",
      "//ios/web:all_tests",
      "//ios/web/shell/test:all_tests",
      "//ios/web_view:all_tests",

      # List all support targets that need to be build on iOS by default.
      "//third_party/breakpad:dump_syms",
      "//third_party/breakpad:symupload",
    ]
  } else {
    deps = []
  }

  deps += [ "//ios/intents/app:intents" ]
}
