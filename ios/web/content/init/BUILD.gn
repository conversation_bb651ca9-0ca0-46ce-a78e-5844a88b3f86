# Copyright 2023 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/ios/ios_sdk.gni")

source_set("init") {
  sources = [
    "ios_content_client.cc",
    "ios_content_client.h",
    "ios_content_renderer_client.cc",
    "ios_content_renderer_client.h",
  ]

  sources += [
    "ios_browser_main_parts.h",
    "ios_browser_main_parts.mm",
    "ios_content_browser_client.cc",
    "ios_content_browser_client.h",
    "ios_content_main_runner.cc",
    "ios_content_main_runner.h",
    "ios_main_delegate.h",
    "ios_main_delegate.mm",
    "ios_renderer_main_delegate.cc",
    "ios_renderer_main_delegate.h",
  ]

  deps = [
    "//build:blink_buildflags",
    "//components/embedder_support:user_agent",
    "//components/embedder_support/origin_trials",
    "//components/js_injection/renderer",
    "//components/performance_manager",
    "//components/version_info",
    "//content/public/app",
    "//content/public/browser",
    "//content/public/common/",
    "//content/public/renderer",
    "//ios/web/content:ui",
    "//ios/web/public",
    "//ios/web/public/init",
  ]
}
