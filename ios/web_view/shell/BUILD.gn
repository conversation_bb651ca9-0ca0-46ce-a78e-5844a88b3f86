# Copyright 2016 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/buildflag_header.gni")
import("//build/config/ios/ios_sdk.gni")
import("//build/config/ios/rules.gni")

declare_args() {
  # The bundle identifier. Overriding this will affect the provisioning profile
  # used, and hence will affect the app's capabilities.
  ios_web_view_shell_bundle_identifier =
      "$ios_app_bundle_id_prefix.ios-web-view-shell"

  # Authorization service implementation used in ios_web_view_shell. Uses a fake
  # implementation by default. Override this with a real implementation to make
  # Sync feature work in the shell. The real implementation must provide
  # implementation of ShellAuthService class.
  ios_web_view_shell_auth_service =
      "//ios/web_view/shell:shell_auth_service_fake_impl"

  # Credit card verification requires risk data to be passed to wallet servers.
  # Override this with a real implementation to allow credit cards to be
  # unmasked. The real implementation must provide an implementation of
  # ShellRiskDataLoader class.
  ios_web_view_shell_risk_data_loader =
      "//ios/web_view/shell:shell_risk_data_loader_fake_impl"

  # Trusted vault requires 1p access to the authorization services.
  # Override this with a real implementation to enable trusted vault.
  ios_web_view_shell_trusted_vault_provider =
      "//ios/web_view/shell:shell_trusted_vault_provider_fake_impl"

  # Path to an entitlements file used in ios_web_view_shell. Can be overridden
  # to provide an alternative.
  ios_web_view_shell_entitlements_path = "//build/config/ios/entitlements.plist"
}

ios_app_bundle("ios_web_view_shell") {
  info_plist = "Info.plist"

  deps = [ ":shell" ]
  bundle_deps = [
    "//ios/web_view:web_view+bundle",
    "//ios/web_view/shell/resources",
    "//ios/web_view/shell/resources:launchscreen",
  ]
  entitlements_path = ios_web_view_shell_entitlements_path
  bundle_identifier = ios_web_view_shell_bundle_identifier

  assert_no_deps = [
    "//ios/third_party/material_components_ios:material_components_ios+bundle",
    "//ios/third_party/material_components_ios:material_components_ios+link",
  ]
}

source_set("shell_auth_service_interface") {
  sources = [ "shell_auth_service.h" ]

  deps = [ "//ios/web_view:web_view+link" ]
}

source_set("shell_auth_service_fake_impl") {
  sources = [ "shell_auth_service_fake.m" ]

  deps = [
    ":shell_auth_service_interface",
    "//ios/web_view:web_view+link",
  ]
}

source_set("shell_risk_data_loader_interface") {
  sources = [ "shell_risk_data_loader.h" ]

  deps = [ "//ios/web_view:web_view+link" ]
}

source_set("shell_risk_data_loader_fake_impl") {
  sources = [ "shell_risk_data_loader_fake.m" ]

  deps = [
    ":shell_risk_data_loader_interface",
    "//ios/web_view:web_view+link",
  ]
}

source_set("shell_trusted_vault_provider_interface") {
  sources = [ "shell_trusted_vault_provider.h" ]

  deps = [
    ":shell_auth_service_interface",
    "//ios/web_view:web_view+link",
  ]
}

source_set("shell_trusted_vault_provider_fake_impl") {
  sources = [ "shell_trusted_vault_provider_fake.m" ]

  deps = [
    ":shell_trusted_vault_provider_interface",
    "//ios/web_view:web_view+link",
  ]
}

source_set("shell") {
  sources = [
    "shell_app_delegate.h",
    "shell_app_delegate.m",
    "shell_autofill_delegate.h",
    "shell_autofill_delegate.m",
    "shell_exe_main.m",
    "shell_translation_delegate.h",
    "shell_translation_delegate.m",
    "shell_view_controller.h",
    "shell_view_controller.m",
  ]

  deps = [
    ":shell_auth_service_interface",
    ":shell_risk_data_loader_interface",
    ":shell_trusted_vault_provider_interface",
    "//base",
    "//ios/third_party/webkit",
    "//ios/web_view:web_view+link",
    ios_web_view_shell_auth_service,
    ios_web_view_shell_risk_data_loader,
    ios_web_view_shell_trusted_vault_provider,
  ]

  libs = [ "resolv" ]
  frameworks = [
    "CFNetwork.framework",
    "CoreFoundation.framework",
    "CoreGraphics.framework",
    "CoreText.framework",
    "Foundation.framework",
    "ImageIO.framework",
    "MobileCoreServices.framework",
    "Security.framework",
    "SystemConfiguration.framework",
    "UIKit.framework",
    "UniformTypeIdentifiers.framework",
  ]
}
