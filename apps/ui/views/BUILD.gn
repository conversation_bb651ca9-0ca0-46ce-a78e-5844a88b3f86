# Copyright 2017 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/ui.gni")
import("//extensions/buildflags/buildflags.gni")

assert(enable_extensions)

source_set("views") {
  sources = [
    "app_window_frame_view.cc",
    "app_window_frame_view.h",
  ]

  configs += [ "//build/config/compiler:wexit_time_destructors" ]

  deps = [
    "//cc/paint",
    "//chrome/app/theme:theme_resources",
    "//extensions/browser",
    "//extensions/common",
    "//skia",
    "//ui/gfx",
    "//ui/strings",
    "//ui/views",
  ]
}
