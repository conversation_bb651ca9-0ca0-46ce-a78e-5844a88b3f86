// Copyright 2019 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

namespace string {
extern runtime StringEscapeQuotes(Context, String): String;

// https://tc39.github.io/ecma262/#sec-createhtml
transitioning builtin CreateHTML(
    implicit context: Context)(receiver: JSAny, methodName: String,
    tagName: String, attr: String, attrValue: JSAny): String {
  const tagContents: String = ToThisString(receiver, methodName);
  let result = '<' + tagName;
  if (attr != kEmptyString) {
    const attrStringValue: String =
        StringEscapeQuotes(context, ToString_Inline(attrValue));
    result = result + ' ' + attr + '=\"' + attrStringValue + '\"';
  }

  return result + '>' + tagContents + '</' + tagName + '>';
}

// https://tc39.github.io/ecma262/#sec-string.prototype.anchor
transitioning javascript builtin StringPrototypeAnchor(
    js-implicit context: NativeContext, receiver: JSAny)(
    ...arguments): String {
  return CreateHTML(
      receiver, 'String.prototype.anchor', 'a', 'name', arguments[0]);
}

// https://tc39.github.io/ecma262/#sec-string.prototype.big
transitioning javascript builtin StringPrototypeBig(
    js-implicit context: NativeContext, receiver: JSAny)(
    ...arguments): String {
  return CreateHTML(
      receiver, 'String.prototype.big', 'big', kEmptyString, kEmptyString);
}

// https://tc39.github.io/ecma262/#sec-string.prototype.blink
transitioning javascript builtin StringPrototypeBlink(
    js-implicit context: NativeContext, receiver: JSAny)(
    ...arguments): String {
  return CreateHTML(
      receiver, 'String.prototype.blink', 'blink', kEmptyString, kEmptyString);
}

// https://tc39.github.io/ecma262/#sec-string.prototype.bold
transitioning javascript builtin StringPrototypeBold(
    js-implicit context: NativeContext, receiver: JSAny)(
    ...arguments): String {
  return CreateHTML(
      receiver, 'String.prototype.bold', 'b', kEmptyString, kEmptyString);
}

// https://tc39.github.io/ecma262/#sec-string.prototype.fontcolor
transitioning javascript builtin StringPrototypeFontcolor(
    js-implicit context: NativeContext, receiver: JSAny)(
    ...arguments): String {
  return CreateHTML(
      receiver, 'String.prototype.fontcolor', 'font', 'color', arguments[0]);
}

// https://tc39.github.io/ecma262/#sec-string.prototype.fontsize
transitioning javascript builtin StringPrototypeFontsize(
    js-implicit context: NativeContext, receiver: JSAny)(
    ...arguments): String {
  return CreateHTML(
      receiver, 'String.prototype.fontsize', 'font', 'size', arguments[0]);
}

// https://tc39.github.io/ecma262/#sec-string.prototype.fixed
transitioning javascript builtin StringPrototypeFixed(
    js-implicit context: NativeContext, receiver: JSAny)(
    ...arguments): String {
  return CreateHTML(
      receiver, 'String.prototype.fixed', 'tt', kEmptyString, kEmptyString);
}

// https://tc39.github.io/ecma262/#sec-string.prototype.italics
transitioning javascript builtin StringPrototypeItalics(
    js-implicit context: NativeContext, receiver: JSAny)(
    ...arguments): String {
  return CreateHTML(
      receiver, 'String.prototype.italics', 'i', kEmptyString, kEmptyString);
}

// https://tc39.github.io/ecma262/#sec-string.prototype.link
transitioning javascript builtin StringPrototypeLink(
    js-implicit context: NativeContext, receiver: JSAny)(
    ...arguments): String {
  return CreateHTML(
      receiver, 'String.prototype.link', 'a', 'href', arguments[0]);
}

// https://tc39.github.io/ecma262/#sec-string.prototype.small
transitioning javascript builtin StringPrototypeSmall(
    js-implicit context: NativeContext, receiver: JSAny)(
    ...arguments): String {
  return CreateHTML(
      receiver, 'String.prototype.small', 'small', kEmptyString, kEmptyString);
}

// https://tc39.github.io/ecma262/#sec-string.prototype.strike
transitioning javascript builtin StringPrototypeStrike(
    js-implicit context: NativeContext, receiver: JSAny)(
    ...arguments): String {
  return CreateHTML(
      receiver, 'String.prototype.strike', 'strike', kEmptyString,
      kEmptyString);
}

// https://tc39.github.io/ecma262/#sec-string.prototype.sub
transitioning javascript builtin StringPrototypeSub(
    js-implicit context: NativeContext, receiver: JSAny)(
    ...arguments): String {
  return CreateHTML(
      receiver, 'String.prototype.sub', 'sub', kEmptyString, kEmptyString);
}

// https://tc39.github.io/ecma262/#sec-string.prototype.sup
transitioning javascript builtin StringPrototypeSup(
    js-implicit context: NativeContext, receiver: JSAny)(
    ...arguments): String {
  return CreateHTML(
      receiver, 'String.prototype.sup', 'sup', kEmptyString, kEmptyString);
}
}
