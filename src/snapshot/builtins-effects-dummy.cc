// Copyright 2025 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#include "src/builtins/builtins.h"

namespace v8::internal {

// This BuiltinCanAllocate is only linked in mksnapshot. V8/D8 use the version
// generated by mksnapshot in gen/src/builtins-generated/builtins-effects.cc.
// During Mksnapshots, builtins should for now be conservative and assume that
// all builtins can allocate (hence this function returning `true`).
// TODO(dmercadier): try to compile builtins in an order such that callees are
// compiled before callers, so that we can make use of the CanAllocate
// information for callees when computing callers.
bool BuiltinCanAllocate(Builtin builtin) { return true; }

}  // namespace v8::internal
