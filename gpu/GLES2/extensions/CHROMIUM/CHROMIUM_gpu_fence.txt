Name

    CHROMIUM_gpu_fence

Name Strings

    GL_CHROMIUM_gpu_fence

Version

    Last Modifed Date: November 28, 2017

Dependencies

    OpenGL ES 2.0 is required.

Overview

    This extension supports cross-process GL context synchronization.

    A "gpu fence" represents a synchronization point in a GL stream that can be
    duplicated into an associated gpu fence in another unrelated GL stream. A
    server wait on the duplicate ensures that the pre-fence commands on the
    source stream have executed. The gpu fence is a reference to a
    platform-dependent external GL fence object.

    IPC transport is based on a "gpu fence handle" which can be converted to
    and from a gpu fence, but the details of this are out of scope for this
    extension.

    See also the ANDROID_native_fence_sync extension which is used to
    implement the GLFenceEGLNativeSync class on Android.

New Procedures and Functions

    The command

        GLuint CreateClientGpuFenceCHROMIUM(ClientGpuFence source)

    creates a waitable service-side gpu fence that signals when the supplied
    external gpu fence is signalled. It returns a gpu fence name.

    The command

        GLuint CreateGpuFenceCHROMIUM()

    creates a new service side gpu fence backed by a new synchronization point
    inserted into the command stream. It returns a gpu fence name. This
    creation method is intended for use with a platform-specific mechanism that
    supports extracting a client gpu fence for external use.

    The command

        void WaitGpuFenceCHROMIUM(GLuint gpu_fence_id)

    issues a server wait. Use this on a gpu fence that was created from an
    external gpu fence to ensure that operations are synchronized with the source
    gpu fence that provided the gpu fence handle.

    The command

        void DestroyGpuFenceCHROMIUM(GLuint gpu_fence_id)

    destroys the specified service side gpu fence and invalidates the gpu_fence
    name. A gpu fence that was duplicated from it remains valid, and it is not
    necessary to wait for the duplicated fence to be used before destroying the
    service side gpu fence.

New Tokens

    None.

Errors

    INVALID_VALUE is generated if the <source> parameter of
    CreateClientGpuFenceCHROMIUM is not a valid ClientGpuFence.

    INVALID_VALUE is generated if the <gpu_fence_id> parameter of
    WaitGpuFenceCHROMIUM or DestroyGpuFenceCHROMIUM is not a valid gpu fence
    name.

New State

    None.

Issues

    Q: Should there be a GetGpuFenceCHROMIUM GLES call to get a ClientGpuFence?

    A: This was excluded and left as a platform-specific non-GLES mechanism for
       flexibility. A synchronous call to get an externally-usable
       ClientGpuFence would be counterproductive since the intent of this
       extension is to sequence asynchronous operations. A callback-based
       method is most natural, but would not be a good fit to the GLES API. A
       sample implementation would be context_support->GetGpuFence(EGLint
       gpu_fence, Callback<void(std::unique_ptr<GpuFence>)>), where the supplied
       callback receives a client gpu fence that it can use to create an
       external fence object for use with a local server wait.

Revision History

    11/28/2017    Documented the extension
