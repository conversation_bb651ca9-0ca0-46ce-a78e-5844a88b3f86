// Copyright 2017 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#ifndef GPU_CONFIG_GPU_CONTROL_LIST_TESTING_DATA_H_
#define GPU_CONFIG_GPU_CONTROL_LIST_TESTING_DATA_H_

#include "gpu/config/gpu_control_list_testing_autogen.h"
#include "gpu/config/gpu_control_list_testing_entry_enums_autogen.h"

namespace gpu {
enum TestFeatureType { TEST_FEATURE_0 = 0, TEST_FEATURE_1, TEST_FEATURE_2 };
}  // namespace gpu

#endif  // GPU_CONFIG_GPU_CONTROL_LIST_TESTING_DATA_H_
