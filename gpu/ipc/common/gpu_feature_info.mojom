// Copyright 2017 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// gpu/config/gpu_feature_info.h
module gpu.mojom;

import "ui/gfx/mojom/buffer_types.mojom";

// gpu::GpuFeatureStatus
enum GpuFeatureStatus {
  Enabled,
  Blocklisted,
  Disabled,
  Software,
  Undefined,
  Max
};

// gpu::GpuFeatureInfo
struct GpuFeatureInfo {
  // The array should have one entry for each GpuFeatureType. The size of the
  // array should be gpu::NUMBER_OF_GPU_FEATURE_TYPES. This is enforced during
  // deserialization.
  array<GpuFeatureStatus, 12> status_values;

  // The array contains a set of workaround IDs that apply in the current
  // platform (OS, GPU, driver, etc). The IDs correspond to the enums defined
  // in gpu/config/gpu_driver_bug_workaround_type.h.
  array<int32> enabled_gpu_driver_bug_workarounds;

  // GL extensions disabled by GpuDriverBugWorkarounds, separated by ' '.
  string disabled_extensions;

  // WebGL extensions disabled by GpuDriverBugWorkarounds, separated by ' '.
  string disabled_webgl_extensions;

  // The array contains a list of gpu blocklist entry indices that apply in the
  // current platform. The entries are defined in
  // gpu/config/software_rendering_list.json.
  array<uint32> applied_gpu_blocklist_entries;

  // The array constains a list of gpu driver bug list entry indices that apply
  // in the current platform. The entries are defined in
  // gpu/config/gpu_driver_bug_list.json.
  array<uint32> applied_gpu_driver_bug_list_entries;

  // BufferFormats that can be allocated and then bound, if known and provided
  // by the platform.
  array<gfx.mojom.BufferFormat>
      supported_buffer_formats_for_allocation_and_texturing;
};
