// Copyright 2024 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#include "gpu/command_buffer/common/shared_image_capabilities.h"

namespace gpu {

SharedImageCapabilities::SharedImageCapabilities() = default;
SharedImageCapabilities::SharedImageCapabilities(
    const SharedImageCapabilities& other) = default;
SharedImageCapabilities::~SharedImageCapabilities() = default;

}  // namespace gpu
