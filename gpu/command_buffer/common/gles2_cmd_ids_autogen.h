// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// This file is auto-generated from
// gpu/command_buffer/build_gles2_cmd_buffer.py
// It's formatted by clang-format using chromium coding style:
//    clang-format -i -style=chromium filename
// DO NOT EDIT!

#ifndef GPU_COMMAND_BUFFER_COMMON_GLES2_CMD_IDS_AUTOGEN_H_
#define GPU_COMMAND_BUFFER_COMMON_GLES2_CMD_IDS_AUTOGEN_H_

#define GLES2_COMMAND_LIST(OP)                                           \
  OP(ActiveTexture)                                            /* 256 */ \
  OP(AttachShader)                                             /* 257 */ \
  OP(BindAttribLocationBucket)                                 /* 258 */ \
  OP(BindBuffer)                                               /* 259 */ \
  OP(BindBufferBase)                                           /* 260 */ \
  OP(BindBufferRange)                                          /* 261 */ \
  OP(BindFramebuffer)                                          /* 262 */ \
  OP(BindRenderbuffer)                                         /* 263 */ \
  OP(BindSampler)                                              /* 264 */ \
  OP(BindTexture)                                              /* 265 */ \
  OP(BindTransformFeedback)                                    /* 266 */ \
  OP(BlendColor)                                               /* 267 */ \
  OP(BlendEquation)                                            /* 268 */ \
  OP(BlendEquationSeparate)                                    /* 269 */ \
  OP(BlendFunc)                                                /* 270 */ \
  OP(BlendFuncSeparate)                                        /* 271 */ \
  OP(BufferData)                                               /* 272 */ \
  OP(BufferSubData)                                            /* 273 */ \
  OP(CheckFramebufferStatus)                                   /* 274 */ \
  OP(Clear)                                                    /* 275 */ \
  OP(ClearBufferfi)                                            /* 276 */ \
  OP(ClearBufferfvImmediate)                                   /* 277 */ \
  OP(ClearBufferivImmediate)                                   /* 278 */ \
  OP(ClearBufferuivImmediate)                                  /* 279 */ \
  OP(ClearColor)                                               /* 280 */ \
  OP(ClearDepthf)                                              /* 281 */ \
  OP(ClearStencil)                                             /* 282 */ \
  OP(ClientWaitSync)                                           /* 283 */ \
  OP(ColorMask)                                                /* 284 */ \
  OP(CompileShader)                                            /* 285 */ \
  OP(CompressedTexImage2DBucket)                               /* 286 */ \
  OP(CompressedTexImage2D)                                     /* 287 */ \
  OP(CompressedTexSubImage2DBucket)                            /* 288 */ \
  OP(CompressedTexSubImage2D)                                  /* 289 */ \
  OP(CompressedTexImage3DBucket)                               /* 290 */ \
  OP(CompressedTexImage3D)                                     /* 291 */ \
  OP(CompressedTexSubImage3DBucket)                            /* 292 */ \
  OP(CompressedTexSubImage3D)                                  /* 293 */ \
  OP(CopyBufferSubData)                                        /* 294 */ \
  OP(CopyTexImage2D)                                           /* 295 */ \
  OP(CopyTexSubImage2D)                                        /* 296 */ \
  OP(CopyTexSubImage3D)                                        /* 297 */ \
  OP(CreateProgram)                                            /* 298 */ \
  OP(CreateShader)                                             /* 299 */ \
  OP(CullFace)                                                 /* 300 */ \
  OP(DeleteBuffersImmediate)                                   /* 301 */ \
  OP(DeleteFramebuffersImmediate)                              /* 302 */ \
  OP(DeleteProgram)                                            /* 303 */ \
  OP(DeleteRenderbuffersImmediate)                             /* 304 */ \
  OP(DeleteSamplersImmediate)                                  /* 305 */ \
  OP(DeleteSync)                                               /* 306 */ \
  OP(DeleteShader)                                             /* 307 */ \
  OP(DeleteTexturesImmediate)                                  /* 308 */ \
  OP(DeleteTransformFeedbacksImmediate)                        /* 309 */ \
  OP(DepthFunc)                                                /* 310 */ \
  OP(DepthMask)                                                /* 311 */ \
  OP(DepthRangef)                                              /* 312 */ \
  OP(DetachShader)                                             /* 313 */ \
  OP(Disable)                                                  /* 314 */ \
  OP(DisableVertexAttribArray)                                 /* 315 */ \
  OP(DrawArrays)                                               /* 316 */ \
  OP(DrawElements)                                             /* 317 */ \
  OP(Enable)                                                   /* 318 */ \
  OP(EnableVertexAttribArray)                                  /* 319 */ \
  OP(FenceSync)                                                /* 320 */ \
  OP(Finish)                                                   /* 321 */ \
  OP(Flush)                                                    /* 322 */ \
  OP(FramebufferRenderbuffer)                                  /* 323 */ \
  OP(FramebufferTexture2D)                                     /* 324 */ \
  OP(FramebufferTextureLayer)                                  /* 325 */ \
  OP(FrontFace)                                                /* 326 */ \
  OP(GenBuffersImmediate)                                      /* 327 */ \
  OP(GenerateMipmap)                                           /* 328 */ \
  OP(GenFramebuffersImmediate)                                 /* 329 */ \
  OP(GenRenderbuffersImmediate)                                /* 330 */ \
  OP(GenSamplersImmediate)                                     /* 331 */ \
  OP(GenTexturesImmediate)                                     /* 332 */ \
  OP(GenTransformFeedbacksImmediate)                           /* 333 */ \
  OP(GetActiveAttrib)                                          /* 334 */ \
  OP(GetActiveUniform)                                         /* 335 */ \
  OP(GetActiveUniformBlockiv)                                  /* 336 */ \
  OP(GetActiveUniformBlockName)                                /* 337 */ \
  OP(GetActiveUniformsiv)                                      /* 338 */ \
  OP(GetAttachedShaders)                                       /* 339 */ \
  OP(GetAttribLocation)                                        /* 340 */ \
  OP(GetBooleanv)                                              /* 341 */ \
  OP(GetBooleani_v)                                            /* 342 */ \
  OP(GetBufferParameteri64v)                                   /* 343 */ \
  OP(GetBufferParameteriv)                                     /* 344 */ \
  OP(GetError)                                                 /* 345 */ \
  OP(GetFloatv)                                                /* 346 */ \
  OP(GetFragDataLocation)                                      /* 347 */ \
  OP(GetFramebufferAttachmentParameteriv)                      /* 348 */ \
  OP(GetInteger64v)                                            /* 349 */ \
  OP(GetIntegeri_v)                                            /* 350 */ \
  OP(GetInteger64i_v)                                          /* 351 */ \
  OP(GetIntegerv)                                              /* 352 */ \
  OP(GetInternalformativ)                                      /* 353 */ \
  OP(GetProgramiv)                                             /* 354 */ \
  OP(GetProgramInfoLog)                                        /* 355 */ \
  OP(GetRenderbufferParameteriv)                               /* 356 */ \
  OP(GetSamplerParameterfv)                                    /* 357 */ \
  OP(GetSamplerParameteriv)                                    /* 358 */ \
  OP(GetShaderiv)                                              /* 359 */ \
  OP(GetShaderInfoLog)                                         /* 360 */ \
  OP(GetShaderPrecisionFormat)                                 /* 361 */ \
  OP(GetShaderSource)                                          /* 362 */ \
  OP(GetString)                                                /* 363 */ \
  OP(GetSynciv)                                                /* 364 */ \
  OP(GetTexParameterfv)                                        /* 365 */ \
  OP(GetTexParameteriv)                                        /* 366 */ \
  OP(GetTransformFeedbackVarying)                              /* 367 */ \
  OP(GetUniformBlockIndex)                                     /* 368 */ \
  OP(GetUniformfv)                                             /* 369 */ \
  OP(GetUniformiv)                                             /* 370 */ \
  OP(GetUniformuiv)                                            /* 371 */ \
  OP(GetUniformIndices)                                        /* 372 */ \
  OP(GetUniformLocation)                                       /* 373 */ \
  OP(GetVertexAttribfv)                                        /* 374 */ \
  OP(GetVertexAttribiv)                                        /* 375 */ \
  OP(GetVertexAttribIiv)                                       /* 376 */ \
  OP(GetVertexAttribIuiv)                                      /* 377 */ \
  OP(GetVertexAttribPointerv)                                  /* 378 */ \
  OP(Hint)                                                     /* 379 */ \
  OP(InvalidateFramebufferImmediate)                           /* 380 */ \
  OP(InvalidateSubFramebufferImmediate)                        /* 381 */ \
  OP(IsBuffer)                                                 /* 382 */ \
  OP(IsEnabled)                                                /* 383 */ \
  OP(IsFramebuffer)                                            /* 384 */ \
  OP(IsProgram)                                                /* 385 */ \
  OP(IsRenderbuffer)                                           /* 386 */ \
  OP(IsSampler)                                                /* 387 */ \
  OP(IsShader)                                                 /* 388 */ \
  OP(IsSync)                                                   /* 389 */ \
  OP(IsTexture)                                                /* 390 */ \
  OP(IsTransformFeedback)                                      /* 391 */ \
  OP(LineWidth)                                                /* 392 */ \
  OP(LinkProgram)                                              /* 393 */ \
  OP(PauseTransformFeedback)                                   /* 394 */ \
  OP(PixelStorei)                                              /* 395 */ \
  OP(PolygonOffset)                                            /* 396 */ \
  OP(ReadBuffer)                                               /* 397 */ \
  OP(ReadPixels)                                               /* 398 */ \
  OP(ReleaseShaderCompiler)                                    /* 399 */ \
  OP(RenderbufferStorage)                                      /* 400 */ \
  OP(ResumeTransformFeedback)                                  /* 401 */ \
  OP(SampleCoverage)                                           /* 402 */ \
  OP(SamplerParameterf)                                        /* 403 */ \
  OP(SamplerParameterfvImmediate)                              /* 404 */ \
  OP(SamplerParameteri)                                        /* 405 */ \
  OP(SamplerParameterivImmediate)                              /* 406 */ \
  OP(Scissor)                                                  /* 407 */ \
  OP(ShaderBinary)                                             /* 408 */ \
  OP(ShaderSourceBucket)                                       /* 409 */ \
  OP(MultiDrawBeginCHROMIUM)                                   /* 410 */ \
  OP(MultiDrawEndCHROMIUM)                                     /* 411 */ \
  OP(MultiDrawArraysCHROMIUM)                                  /* 412 */ \
  OP(MultiDrawArraysInstancedCHROMIUM)                         /* 413 */ \
  OP(MultiDrawArraysInstancedBaseInstanceCHROMIUM)             /* 414 */ \
  OP(MultiDrawElementsCHROMIUM)                                /* 415 */ \
  OP(MultiDrawElementsInstancedCHROMIUM)                       /* 416 */ \
  OP(MultiDrawElementsInstancedBaseVertexBaseInstanceCHROMIUM) /* 417 */ \
  OP(StencilFunc)                                              /* 418 */ \
  OP(StencilFuncSeparate)                                      /* 419 */ \
  OP(StencilMask)                                              /* 420 */ \
  OP(StencilMaskSeparate)                                      /* 421 */ \
  OP(StencilOp)                                                /* 422 */ \
  OP(StencilOpSeparate)                                        /* 423 */ \
  OP(TexImage2D)                                               /* 424 */ \
  OP(TexImage3D)                                               /* 425 */ \
  OP(TexParameterf)                                            /* 426 */ \
  OP(TexParameterfvImmediate)                                  /* 427 */ \
  OP(TexParameteri)                                            /* 428 */ \
  OP(TexParameterivImmediate)                                  /* 429 */ \
  OP(TexStorage3D)                                             /* 430 */ \
  OP(TexSubImage2D)                                            /* 431 */ \
  OP(TexSubImage3D)                                            /* 432 */ \
  OP(TransformFeedbackVaryingsBucket)                          /* 433 */ \
  OP(Uniform1f)                                                /* 434 */ \
  OP(Uniform1fvImmediate)                                      /* 435 */ \
  OP(Uniform1i)                                                /* 436 */ \
  OP(Uniform1ivImmediate)                                      /* 437 */ \
  OP(Uniform1ui)                                               /* 438 */ \
  OP(Uniform1uivImmediate)                                     /* 439 */ \
  OP(Uniform2f)                                                /* 440 */ \
  OP(Uniform2fvImmediate)                                      /* 441 */ \
  OP(Uniform2i)                                                /* 442 */ \
  OP(Uniform2ivImmediate)                                      /* 443 */ \
  OP(Uniform2ui)                                               /* 444 */ \
  OP(Uniform2uivImmediate)                                     /* 445 */ \
  OP(Uniform3f)                                                /* 446 */ \
  OP(Uniform3fvImmediate)                                      /* 447 */ \
  OP(Uniform3i)                                                /* 448 */ \
  OP(Uniform3ivImmediate)                                      /* 449 */ \
  OP(Uniform3ui)                                               /* 450 */ \
  OP(Uniform3uivImmediate)                                     /* 451 */ \
  OP(Uniform4f)                                                /* 452 */ \
  OP(Uniform4fvImmediate)                                      /* 453 */ \
  OP(Uniform4i)                                                /* 454 */ \
  OP(Uniform4ivImmediate)                                      /* 455 */ \
  OP(Uniform4ui)                                               /* 456 */ \
  OP(Uniform4uivImmediate)                                     /* 457 */ \
  OP(UniformBlockBinding)                                      /* 458 */ \
  OP(UniformMatrix2fvImmediate)                                /* 459 */ \
  OP(UniformMatrix2x3fvImmediate)                              /* 460 */ \
  OP(UniformMatrix2x4fvImmediate)                              /* 461 */ \
  OP(UniformMatrix3fvImmediate)                                /* 462 */ \
  OP(UniformMatrix3x2fvImmediate)                              /* 463 */ \
  OP(UniformMatrix3x4fvImmediate)                              /* 464 */ \
  OP(UniformMatrix4fvImmediate)                                /* 465 */ \
  OP(UniformMatrix4x2fvImmediate)                              /* 466 */ \
  OP(UniformMatrix4x3fvImmediate)                              /* 467 */ \
  OP(UseProgram)                                               /* 468 */ \
  OP(ValidateProgram)                                          /* 469 */ \
  OP(VertexAttrib1f)                                           /* 470 */ \
  OP(VertexAttrib1fvImmediate)                                 /* 471 */ \
  OP(VertexAttrib2f)                                           /* 472 */ \
  OP(VertexAttrib2fvImmediate)                                 /* 473 */ \
  OP(VertexAttrib3f)                                           /* 474 */ \
  OP(VertexAttrib3fvImmediate)                                 /* 475 */ \
  OP(VertexAttrib4f)                                           /* 476 */ \
  OP(VertexAttrib4fvImmediate)                                 /* 477 */ \
  OP(VertexAttribI4i)                                          /* 478 */ \
  OP(VertexAttribI4ivImmediate)                                /* 479 */ \
  OP(VertexAttribI4ui)                                         /* 480 */ \
  OP(VertexAttribI4uivImmediate)                               /* 481 */ \
  OP(VertexAttribIPointer)                                     /* 482 */ \
  OP(VertexAttribPointer)                                      /* 483 */ \
  OP(Viewport)                                                 /* 484 */ \
  OP(WaitSync)                                                 /* 485 */ \
  OP(BlitFramebufferCHROMIUM)                                  /* 486 */ \
  OP(RenderbufferStorageMultisampleCHROMIUM)                   /* 487 */ \
  OP(RenderbufferStorageMultisampleAdvancedAMD)                /* 488 */ \
  OP(RenderbufferStorageMultisampleEXT)                        /* 489 */ \
  OP(FramebufferTexture2DMultisampleEXT)                       /* 490 */ \
  OP(TexStorage2DEXT)                                          /* 491 */ \
  OP(GenQueriesEXTImmediate)                                   /* 492 */ \
  OP(DeleteQueriesEXTImmediate)                                /* 493 */ \
  OP(QueryCounterEXT)                                          /* 494 */ \
  OP(BeginQueryEXT)                                            /* 495 */ \
  OP(BeginTransformFeedback)                                   /* 496 */ \
  OP(EndQueryEXT)                                              /* 497 */ \
  OP(EndTransformFeedback)                                     /* 498 */ \
  OP(SetDisjointValueSyncCHROMIUM)                             /* 499 */ \
  OP(InsertEventMarkerEXT)                                     /* 500 */ \
  OP(PushGroupMarkerEXT)                                       /* 501 */ \
  OP(PopGroupMarkerEXT)                                        /* 502 */ \
  OP(GenVertexArraysOESImmediate)                              /* 503 */ \
  OP(DeleteVertexArraysOESImmediate)                           /* 504 */ \
  OP(IsVertexArrayOES)                                         /* 505 */ \
  OP(BindVertexArrayOES)                                       /* 506 */ \
  OP(FramebufferParameteri)                                    /* 507 */ \
  OP(BindImageTexture)                                         /* 508 */ \
  OP(DispatchCompute)                                          /* 509 */ \
  OP(DispatchComputeIndirect)                                  /* 510 */ \
  OP(DrawArraysIndirect)                                       /* 511 */ \
  OP(DrawElementsIndirect)                                     /* 512 */ \
  OP(GetProgramInterfaceiv)                                    /* 513 */ \
  OP(GetProgramResourceIndex)                                  /* 514 */ \
  OP(GetProgramResourceName)                                   /* 515 */ \
  OP(GetProgramResourceiv)                                     /* 516 */ \
  OP(GetProgramResourceLocation)                               /* 517 */ \
  OP(MemoryBarrierEXT)                                         /* 518 */ \
  OP(MemoryBarrierByRegion)                                    /* 519 */ \
  OP(GetMaxValueInBufferCHROMIUM)                              /* 520 */ \
  OP(EnableFeatureCHROMIUM)                                    /* 521 */ \
  OP(MapBufferRange)                                           /* 522 */ \
  OP(UnmapBuffer)                                              /* 523 */ \
  OP(FlushMappedBufferRange)                                   /* 524 */ \
  OP(GetRequestableExtensionsCHROMIUM)                         /* 525 */ \
  OP(RequestExtensionCHROMIUM)                                 /* 526 */ \
  OP(GetProgramInfoCHROMIUM)                                   /* 527 */ \
  OP(GetUniformBlocksCHROMIUM)                                 /* 528 */ \
  OP(GetTransformFeedbackVaryingsCHROMIUM)                     /* 529 */ \
  OP(GetUniformsES3CHROMIUM)                                   /* 530 */ \
  OP(DescheduleUntilFinishedCHROMIUM)                          /* 531 */ \
  OP(GetTranslatedShaderSourceANGLE)                           /* 532 */ \
  OP(CopyTextureCHROMIUM)                                      /* 533 */ \
  OP(CopySubTextureCHROMIUM)                                   /* 534 */ \
  OP(DrawArraysInstancedANGLE)                                 /* 535 */ \
  OP(DrawArraysInstancedBaseInstanceANGLE)                     /* 536 */ \
  OP(DrawElementsInstancedANGLE)                               /* 537 */ \
  OP(DrawElementsInstancedBaseVertexBaseInstanceANGLE)         /* 538 */ \
  OP(VertexAttribDivisorANGLE)                                 /* 539 */ \
  OP(BindUniformLocationCHROMIUMBucket)                        /* 540 */ \
  OP(TraceBeginCHROMIUM)                                       /* 541 */ \
  OP(TraceEndCHROMIUM)                                         /* 542 */ \
  OP(DiscardFramebufferEXTImmediate)                           /* 543 */ \
  OP(LoseContextCHROMIUM)                                      /* 544 */ \
  OP(DrawBuffersEXTImmediate)                                  /* 545 */ \
  OP(FlushDriverCachesCHROMIUM)                                /* 546 */ \
  OP(SetActiveURLCHROMIUM)                                     /* 547 */ \
  OP(ContextVisibilityHintCHROMIUM)                            /* 548 */ \
  OP(BlendBarrierKHR)                                          /* 549 */ \
  OP(BindFragDataLocationIndexedEXTBucket)                     /* 550 */ \
  OP(BindFragDataLocationEXTBucket)                            /* 551 */ \
  OP(GetFragDataIndexEXT)                                      /* 552 */ \
  OP(InitializeDiscardableTextureCHROMIUM)                     /* 553 */ \
  OP(UnlockDiscardableTextureCHROMIUM)                         /* 554 */ \
  OP(LockDiscardableTextureCHROMIUM)                           /* 555 */ \
  OP(WindowRectanglesEXTImmediate)                             /* 556 */ \
  OP(CreateGpuFenceINTERNAL)                                   /* 557 */ \
  OP(WaitGpuFenceCHROMIUM)                                     /* 558 */ \
  OP(DestroyGpuFenceCHROMIUM)                                  /* 559 */ \
  OP(SetReadbackBufferShadowAllocationINTERNAL)                /* 560 */ \
  OP(FramebufferTextureMultiviewOVR)                           /* 561 */ \
  OP(MaxShaderCompilerThreadsKHR)                              /* 562 */ \
  OP(CreateAndTexStorage2DSharedImageINTERNALImmediate)        /* 563 */ \
  OP(BeginSharedImageAccessDirectCHROMIUM)                     /* 564 */ \
  OP(EndSharedImageAccessDirectCHROMIUM)                       /* 565 */ \
  OP(CopySharedImageINTERNALImmediate)                         /* 566 */ \
  OP(CopySharedImageToTextureINTERNALImmediate)                /* 567 */ \
  OP(ReadbackARGBImagePixelsINTERNAL)                          /* 568 */ \
  OP(WritePixelsYUVINTERNAL)                                   /* 569 */ \
  OP(EnableiOES)                                               /* 570 */ \
  OP(DisableiOES)                                              /* 571 */ \
  OP(BlendEquationiOES)                                        /* 572 */ \
  OP(BlendEquationSeparateiOES)                                /* 573 */ \
  OP(BlendFunciOES)                                            /* 574 */ \
  OP(BlendFuncSeparateiOES)                                    /* 575 */ \
  OP(ColorMaskiOES)                                            /* 576 */ \
  OP(IsEnablediOES)                                            /* 577 */ \
  OP(ProvokingVertexANGLE)                                     /* 578 */ \
  OP(FramebufferMemorylessPixelLocalStorageANGLE)              /* 579 */ \
  OP(FramebufferTexturePixelLocalStorageANGLE)                 /* 580 */ \
  OP(FramebufferPixelLocalClearValuefvANGLEImmediate)          /* 581 */ \
  OP(FramebufferPixelLocalClearValueivANGLEImmediate)          /* 582 */ \
  OP(FramebufferPixelLocalClearValueuivANGLEImmediate)         /* 583 */ \
  OP(BeginPixelLocalStorageANGLEImmediate)                     /* 584 */ \
  OP(EndPixelLocalStorageANGLEImmediate)                       /* 585 */ \
  OP(PixelLocalStorageBarrierANGLE)                            /* 586 */ \
  OP(FramebufferPixelLocalStorageInterruptANGLE)               /* 587 */ \
  OP(FramebufferPixelLocalStorageRestoreANGLE)                 /* 588 */ \
  OP(GetFramebufferPixelLocalStorageParameterfvANGLE)          /* 589 */ \
  OP(GetFramebufferPixelLocalStorageParameterivANGLE)          /* 590 */ \
  OP(ClipControlEXT)                                           /* 591 */ \
  OP(PolygonModeANGLE)                                         /* 592 */ \
  OP(PolygonOffsetClampEXT)                                    /* 593 */

enum CommandId {
  kOneBeforeStartPoint =
      cmd::kLastCommonId,  // All GLES2 commands start after this.
#define GLES2_CMD_OP(name) k##name,
  GLES2_COMMAND_LIST(GLES2_CMD_OP)
#undef GLES2_CMD_OP
      kNumCommands,
  kFirstGLES2Command = kOneBeforeStartPoint + 1
};

#endif  // GPU_COMMAND_BUFFER_COMMON_GLES2_CMD_IDS_AUTOGEN_H_
