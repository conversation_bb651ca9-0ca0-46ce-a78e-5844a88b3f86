# Copyright 2016 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/toolchain/concurrent_links.gni")
import("//build/toolchain/rbe.gni")

declare_args() {
  # Pool for non remote tasks.
  action_pool_depth = -1
}

if (current_toolchain == default_toolchain) {
  if (action_pool_depth == -1 || use_remoteexec) {
    action_pool_depth = exec_script("get_cpu_count.py", [], "value")
  }

  pool("link_pool") {
    depth = concurrent_links
  }

  pool("action_pool") {
    depth = action_pool_depth
  }
}
