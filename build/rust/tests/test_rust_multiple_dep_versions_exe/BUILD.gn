# Copyright 2021 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/rust/rust_executable.gni")
import("//build/rust/rust_static_library.gni")

# The exe depends on lib v1. But it also transitively depends on lib v2.
# The code in the exe should use v1, and the code in the transitive lib should
# use v2.
rust_executable("test_rust_multiple_dep_versions_exe") {
  crate_root = "main.rs"
  sources = [ "main.rs" ]
  deps = [
    ":transitive_v2",
    "//build/rust/tests/test_rust_multiple_dep_versions_exe/v1:test_lib",
  ]
}

rust_static_library("transitive_v2") {
  crate_root = "transitive_lib.rs"
  sources = [ "transitive_lib.rs" ]
  deps =
      [ "//build/rust/tests/test_rust_multiple_dep_versions_exe/v2:test_lib" ]
}
