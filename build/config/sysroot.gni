# Copyright 2013 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

# This header file defines the "sysroot" variable which is the absolute path
# of the sysroot. If no sysroot applies, the variable will be an empty string.

declare_args() {
  # The path of the sysroot that is applied when compiling using the target
  # toolchain.
  target_sysroot = ""

  # The path to directory containing linux sysroot images.
  target_sysroot_dir = "//build/linux"

  # The path of the sysroot for the current toolchain. If empty, default
  # sysroot is used.
  sysroot = ""

  # Controls default is_linux sysroot. If set to true, and sysroot
  # is empty, default sysroot is calculated.
  use_sysroot =
      current_cpu == "x86" || current_cpu == "x64" || current_cpu == "arm" ||
      current_cpu == "arm64" || current_cpu == "mipsel" ||
      current_cpu == "mips64el" || (current_cpu == "riscv64" && is_android)
}

if (sysroot == "") {
  if (current_os == target_os && current_cpu == target_cpu &&
      target_sysroot != "") {
    sysroot = target_sysroot
  } else if (is_android) {
    import("//build/config/android/config.gni")

    # Android uses unified headers, and thus a single compile time sysroot
    sysroot = "$android_toolchain_root/sysroot"
  } else if ((is_linux || is_chromeos) && use_sysroot) {
    # By default build against a sysroot image downloaded from Cloud Storage
    # during gclient runhooks.
    if (current_cpu == "x64") {
      sysroot = "$target_sysroot_dir/debian_bullseye_amd64-sysroot"
    } else if (current_cpu == "x86") {
      sysroot = "$target_sysroot_dir/debian_bullseye_i386-sysroot"
    } else if (current_cpu == "mipsel") {
      sysroot = "$target_sysroot_dir/debian_bullseye_mipsel-sysroot"
    } else if (current_cpu == "mips64el") {
      sysroot = "$target_sysroot_dir/debian_bullseye_mips64el-sysroot"
    } else if (current_cpu == "arm") {
      sysroot = "$target_sysroot_dir/debian_bullseye_armhf-sysroot"
    } else if (current_cpu == "arm64") {
      sysroot = "$target_sysroot_dir/debian_bullseye_arm64-sysroot"
    } else {
      assert(false, "No linux sysroot for cpu: $target_cpu")
    }

    if (sysroot != "") {
      _script_arch = current_cpu
      if (_script_arch == "x86") {
        _script_arch = "i386"
      } else if (_script_arch == "x64") {
        _script_arch = "amd64"
      }
      assert(
          path_exists(sysroot),
          "Missing sysroot ($sysroot). To fix, run: build/linux/sysroot_scripts/install-sysroot.py --arch=$_script_arch")
    }
  } else if (is_mac) {
    import("//build/config/mac/mac_sdk.gni")
    sysroot = mac_sdk_path
  } else if (is_ios) {
    import("//build/config/ios/ios_sdk.gni")
    sysroot = ios_sdk_path
  } else if (is_fuchsia) {
    import("//build/config/fuchsia/gn_configs.gni")
    sysroot = "${fuchsia_arch_root}/sysroot"
  }
}
