# Copyright 2024 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/zip.gni")

if (is_ios) {
  import("//build/config/ios/ios_sdk_overrides.gni")
}

# Compile a xib or storyboard file and add it to a bundle_data so that it is
# available at runtime in the bundle.
#
# Arguments
#
#   source:
#       string, path of the xib or storyboard to compile.
#
#   extension:
#       string, extension of the generated file or bundle.
#
#   bundle_files:
#       list of string, name of the files in the generated  bundle;
#       if empty, the output is expected to be a single file.
#
# Forwards all variables to the bundle_data target.
template("bundle_data_ib_file") {
  assert(defined(invoker.source), "source needs to be defined for $target_name")
  assert(defined(invoker.extension),
         "extension needs to be defined for $target_name")
  assert(defined(invoker.bundle_files),
         "bundle_files needs to be defined for $target_name")

  _target_name = target_name
  _compile_target =
      target_name + "_compile_" + get_path_info(invoker.source, "extension")

  _output_path = "$target_gen_dir/$_target_name/"
  _output_name = get_path_info(invoker.source, "name") + ".${invoker.extension}"

  if (is_ios) {
    _deployment_target = ios_deployment_target
    _target_devices = [
      "iphone",
      "ipad",
    ]
  } else {
    assert(false, "Unsupported platform: " + current_os)
  }

  action(_compile_target) {
    forward_variables_from(invoker,
                           "*",
                           [
                             "source",
                             "bundle_files",
                           ])

    script = "//build/config/apple/compile_ib_files.py"
    args = [
      "--input",
      rebase_path(invoker.source, root_build_dir),
      "--output",
      rebase_path("$_output_path/$_output_name", root_build_dir),
      "--minimum-deployment-target",
      _deployment_target,
      "--auto-activate-custom-fonts",
    ]

    foreach(target_device, _target_devices) {
      args += [
        "--target-device",
        target_device,
      ]
    }

    sources = [ invoker.source ]
    if (invoker.bundle_files == []) {
      outputs = [ "$_output_path/$_output_name" ]
    } else {
      outputs = []
      foreach(_bundle_file, invoker.bundle_files) {
        outputs += [ "$_output_path/$_output_name/$_bundle_file" ]
      }
    }
  }

  bundle_data(_target_name) {
    forward_variables_from(invoker, "*", [ "source" ])

    if (!defined(public_deps)) {
      public_deps = []
    }
    public_deps += [ ":$_compile_target" ]

    sources = get_target_outputs(":$_compile_target")
    if (invoker.bundle_files == []) {
      outputs = [ "{{bundle_resources_dir}}/{{source_file_part}}" ]
    } else {
      outputs =
          [ "{{bundle_resources_dir}}/$_output_name/{{source_file_part}}" ]
    }
  }
}

# Compile a xib file and add it to a bundle_data so that it is available at
# runtime in the bundle.
#
# Arguments
#
#   source:
#       string, path of the xib or storyboard to compile.
#
# Forwards all variables to the bundle_data target.
template("bundle_data_xib_file") {
  assert(defined(invoker.source), "source needs to be defined for $target_name")

  _extension = get_path_info(invoker.source, "extension")
  assert(_extension == "xib",
         "source must have the .xib extension for $target_name")

  bundle_data_ib_file(target_name) {
    forward_variables_from(invoker, "*", TESTONLY_AND_VISIBILITY)
    forward_variables_from(invoker, TESTONLY_AND_VISIBILITY)

    extension = "nib"
    bundle_files = []
  }
}

# Compile a storyboard file and add it to a bundle_data so that it is available
# at runtime in the bundle.
#
# Arguments
#
#   source:
#       string, path of the xib or storyboard to compile.
#
#   bundle_files
#       list of strings, name of the individual files in the generated bundle
#
# Forwards all variables to the bundle_data target.
template("bundle_data_storyboard_file") {
  assert(defined(invoker.source), "source needs to be defined for $target_name")

  assert(defined(invoker.bundle_files) && invoker.bundle_files != [],
         "bundle_files needs to be defined for $target_name")

  _extension = get_path_info(invoker.source, "extension")
  assert(_extension == "storyboard",
         "source must have the .storyboard extension for $target_name")

  bundle_data_ib_file(target_name) {
    forward_variables_from(invoker, "*", TESTONLY_AND_VISIBILITY)
    forward_variables_from(invoker, TESTONLY_AND_VISIBILITY)

    extension = "storyboardc"
  }
}

# Compile a strings file and add it to a bundle_data so that it is available
# at runtime in the bundle.
#
# Arguments
#
#   source:
#       string, path of the strings file to compile.
#
#   output:
#       string, path of the compiled file in the final bundle.
#
# Forwards all variables to the bundle_data target.
template("bundle_data_strings") {
  assert(defined(invoker.source), "source needs to be defined for $target_name")
  assert(defined(invoker.output), "output needs to be defined for $target_name")

  _source_extension = get_path_info(invoker.source, "extension")
  assert(_source_extension == "strings",
         "source must be a .strings for $target_name")

  _target_name = target_name
  _convert_target = target_name + "_compile_strings"

  convert_plist(_convert_target) {
    visibility = [ ":$_target_name" ]
    source = invoker.source
    output =
        "$target_gen_dir/$_target_name/" + get_path_info(invoker.source, "file")
    format = "binary1"
  }

  bundle_data(_target_name) {
    forward_variables_from(invoker,
                           "*",
                           [
                             "source",
                             "output",
                           ])

    if (!defined(public_deps)) {
      public_deps = []
    }
    public_deps += [ ":$_convert_target" ]

    sources = get_target_outputs(":$_convert_target")

    outputs = [ invoker.output ]
  }
}

# This template declares a bundle_data target that reference an assets
# catalog so that is is compiled to the asset catalog of the generated
# bundle.
#
# The target will ensure that only the files explicitly listed will be
# compiled into the final application (i.e. it allow listing some of
# the assets catalog content conditionally).
#
# The target requires that the files are located in a .xcassets bundle
# in the repository (or generated via a script). This ensures that the
# assets catalog is correctly visible in Xcode (though as usual, using
# Xcode to make change to the .xcassets bundle will not be reflected in
# the final build unless the target is updated in the gn configuration).
#
# Arguments
#
#     sources:
#       required, list of strings, path to the files contained in the
#       .xcassets bundle; this may contains a sub-set of the files on
#       disk if some assets are only compiled conditionally
#
#     catalog:
#       required, string, path to the .xcassets bundle; all path in
#       sources must be relative to this path or the compilation will
#       fail
#
# Example
#
#     bundle_data_xcassets("assets") {
#       catalog = "Assets.xcassets"
#       sources = [
#         "Assets.xcassets/Color.colorset/Contents.json",
#         "Assets.xcassets/Contents.json",
#       ]
#       if (includes_images) {
#         sources += [
#           "Assets.xcassets/Image.imageset/Contents.json",
#           "Assets.xcassets/Image.imageset/Image.svg",
#         ]
#       }
#     }
template("bundle_data_xcassets") {
  assert(defined(invoker.sources), "sources must be defined for $target_name")
  assert(defined(invoker.catalog), "catalog must be defined for $target_name")

  _target_name = target_name
  _target_zip = target_name + "_zip"

  zip(_target_zip) {
    _catalog_name = get_path_info(invoker.catalog, "file")
    _catalog_path = get_path_info(invoker.catalog, "dir")

    inputs = invoker.sources
    output = "$target_out_dir/$target_name/$_catalog_name"
    base_dir = _catalog_path
  }

  bundle_data(_target_name) {
    forward_variables_from(invoker,
                           "*",
                           [
                             "sources",
                             "deps",
                             "public_deps",
                           ])

    public_deps = [ ":$_target_zip" ]
    sources = get_target_outputs(":$_target_zip")
    outputs = [ "{{bundle_resources_dir}}/{{source_file_part}}" ]
  }
}
