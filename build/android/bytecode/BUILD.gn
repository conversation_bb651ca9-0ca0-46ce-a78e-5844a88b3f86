# Copyright 2017 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/android/rules.gni")

java_binary("trace_event_adder") {
  main_class = "org.chromium.bytecode.TraceEventAdder"
  deps = [ ":trace_event_adder_java" ]
  wrapper_script_name = "helper/trace_event_adder"
}

java_library("trace_event_adder_java") {
  visibility = [ ":*" ]
  sources = [
    "java/org/chromium/bytecode/ByteCodeRewriter.java",
    "java/org/chromium/bytecode/EmptyOverrideGeneratorClassAdapter.java",
    "java/org/chromium/bytecode/MethodCheckerClassAdapter.java",
    "java/org/chromium/bytecode/MethodDescription.java",
    "java/org/chromium/bytecode/ParentMethodCheckerClassAdapter.java",
    "java/org/chromium/bytecode/TraceEventAdder.java",
    "java/org/chromium/bytecode/TraceEventAdderClassAdapter.java",
    "java/org/chromium/bytecode/TraceEventAdderMethodAdapter.java",
    "java/org/chromium/bytecode/TypeUtils.java",
  ]
  deps = [
    "//third_party/android_deps:org_ow2_asm_asm_commons_java",
    "//third_party/android_deps:org_ow2_asm_asm_java",
    "//third_party/android_deps:org_ow2_asm_asm_util_java",
  ]
}
