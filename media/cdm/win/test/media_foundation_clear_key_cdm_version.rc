// Copyright (c) 2023 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// This file is a copy of chrome_version.rc.version in Chromium but modified.

#include <verrsrc.h>

/////////////////////////////////////////////////////////////////////////////
//
// Version
//

VS_VERSION_INFO VERSIONINFO
 FILEVERSION 1,0,0,0
 PRODUCTVERSION 1,0,0,0
 FILEFLAGSMASK 0x17L
#ifdef _DEBUG
 FILEFLAGS 0x1L
#else
 FILEFLAGS 0x0L
#endif
 FILEOS 0x4L
 FILETYPE 0x1L
 FILESUBTYPE 0x0L
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "040904b0"
        BEGIN
            VALUE "CompanyName", "Chromium"
            VALUE "FileDescription", "Media Foundation Clear Key Content Decryption Module"
            VALUE "FileVersion", "*******"
            VALUE "InternalName", "Media Foundation Clear Key CDM"
            VALUE "LegalCopyright", "Copyright 2023 The Chromium Authors. All rights reserved."
            VALUE "OriginalFilename", "MediaFoundation.ClearKey.CDM.dll"
            VALUE "ProductName", "Media Foundation Clear Key Content Decryption Module"
            VALUE "ProductVersion", "*******"
            VALUE "CompanyShortName", "Chromium"
            VALUE "ProductShortName", "Media Foundation Clear Key CDM"
            VALUE "LastChange", "0"
            VALUE "Official Build", "0"
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x409, 1200
    END
END
