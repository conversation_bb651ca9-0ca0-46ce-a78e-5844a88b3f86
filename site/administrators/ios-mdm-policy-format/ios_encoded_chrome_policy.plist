<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist  PUBLIC '-//Apple//DTD PLIST 1.0//EN'  'http://www.apple.com/DTDs/PropertyList-1.0.dtd'>
<plist version="1.0">
  <dict>
    <!-- EncodedChromePolicy contains a Property List file, encoded in Base64,
         which contains the same policies that can go in ChromePolicy.
         This key can be used by vendors that restrict the app configuration
         types to strings.
         The value of this string can be validated by running these
         commands in Mac OS X:

         # (first, copy-paste the string into a file named "policy.plist")
         # base64 -D < policy.plist > decoded_policy.plist
         # plutil -lint decoded_policy.plist

         plutil should indicate that decoded_policy.plist is valid,
         otherwise Chrome will reject the encoded string too.

         This command can be used to pretty-print the plist file:

         # plutil -convert xml1 decoded_policy.plist

         Note that <ChromePolicy> is the preferred key to configure Chrome.
         If <ChromePolicy> is present then <EncodedChromePolicy> is ignored. -->
    <key>EncodedChromePolicy</key>
    <string>********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************</string>
  </dict>
</plist>
