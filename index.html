<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <meta name="twitter:card" content="summary">
    <meta name="twitter:title" content="WebVR">
    <meta name="twitter:description" content="Bringing Virtual Reality to the Web">
    <meta name="twitter:image" content="https://webvr.info/images/webvr-logo-square.png">

    <link rel="stylesheet" href="stylesheets/stylesheet.css">
    <link rel="stylesheet" href="stylesheets/pygment_trac.css">
    <!--[if lt IE 9]>
    <script src="https://html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <title>WebVR - Bringing Virtual Reality to the Web</title>

    <link rel="icon" type="image/png" sizes="32x32" href="favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="96x96" href="favicon-96x96.png">


</head>
<body>
<div class="container" id="container">
    <header class="header">
        <div id="nav">
            <a href="./" class="selected">About</a>
            <a href="./developers">Developers</a>
        </div>

        <h1><a href="" class="wordmark"><span>WebVR</span></a></h1>
        <h2 class="tagline">Bringing Virtual Reality to the Web</h2>
    </header>

    <main class="main" id="main">
        <h3>
            <a id="what-is-webvr" class="anchor" href="#what-is-webvr" aria-hidden="true"><span class="octicon octicon-link"></span></a>
            What is WebVR?
        </h3>

        <p>
            WebVR is an open specification that makes it possible to experience VR in your browser.
            The goal is to make it easier for everyone to get into VR experiences, no matter what device you have
        </p>

        <h3>
            <a id="how-do-i-experience-webvr" class="anchor" href="#how-do-i-experience-webvr" aria-hidden="true"><span class="octicon octicon-link"></span></a>
            How do I experience WebVR?
        </h3>
        <p>
            You need two things to experience WebVR: a headset and a compatible browser.
        </p>

        <h3>
            <a id="got-a-headset" class="anchor" href="#got-a-headset" aria-hidden="true"><span class="octicon octicon-link"></span></a>
            I’ve already got a headset.
        </h3>
        <p>
            Select your headset below to find out what browsers work best.
        </p>

        <p>

            <div class="devices">
                <div class="device-col">
                    <div class="device-box" onclick="this.classList.toggle('open');">
                        <img src="images/Google%20Cardboard.png" alt="Google Cardboard">
                        <span class="carrot"></span>
                        <div class="device-box-header">Google Cardboard</div>
                        <span>
                            Works best with <a href="https://play.google.com/store/apps/details?id=com.android.chrome">Chrome</a> on Android devices.
                        </span>
                        <span>
                            You can still experience WebVR content in other browsers on Android and iOS, but it might not be as smooth since those browsers don’t fully support WebVR.
                        </span>
                    </div>
                    <div class="device-box" onclick="this.classList.toggle('open');">
                        <img src="images/DayDream.png" alt="Daydream">
                        <span class="carrot"></span>
                        <div class="device-box-header">Daydream</div>
                        <span>
                            Works with <a href="https://play.google.com/store/apps/details?id=com.android.chrome">Chrome</a> on Daydream-ready Android devices.
                        </span>
                    </div>
                    <div class="device-box" onclick="this.classList.toggle('open');">
                        <img src="images/GearVR.png" alt="Gear VR">
                        <span class="carrot"></span>
                        <div class="device-box-header">Samsung Gear VR</div>
                        <span>
                            Works with <a href="https://www.oculus.com/experiences/gear-vr/1290985657630933/">Oculus Carmel</a> and <a href="https://www.oculus.com/experiences/gear-vr/849609821813454/">Samsung Internet</a>
                        </span>
                    </div>
                    <div class="device-box" onclick="this.classList.toggle('open');">
                        <img src="images/Oculus.png" alt="Oculus">
                        <span class="carrot"></span>
                        <div class="device-box-header">Oculus Rift</div>
                        <span>
                            Works with <a href="https://webvr.rocks/firefox">Firefox Nightly</a> on Windows
                        </span>
                    </div>
                </div>
                <div class="device-col">
                    <div class="device-box" onclick="this.classList.toggle('open');">
                        <img src="images/HTC%20Vive.png" alt="HTC Vive">
                        <span class="carrot"></span>
                        <div class="device-box-header">HTC Vive</div>
                        <span>
                            Works with <a href="https://webvr.rocks/firefox">Firefox Nightly</a>,
                            <a href="https://blog.mozvr.com/webvr-servo-architecture-and-latency-optimizations/">Servo</a>
                            on Windows
                        </span>
                    </div>
                    <div class="device-box" onclick="this.classList.toggle('open');">
                        <img src="images/Playstation%20VR.png" alt="Playstation VR">
                        <span class="carrot"></span>
                        <div class="device-box-header">Playstation VR</div>
                        <span>
                            Does not currently support WebVR
                        </span>
                    </div>
                    <div class="device-box" onclick="this.classList.toggle('open');">
                        <img src="images/Microsoft.png" alt="Microsoft Windows">
                        <span class="carrot"></span>
                        <div class="device-box-header">Windows Mixed Reality headsets</div>
                        <span>
                            <a href="https://blogs.windows.com/msedgedev/2016/09/09/webvr-in-development-edge/">Microsoft Edge</a> has <a href="https://developer.microsoft.com/en-us/microsoft-edge/platform/status/webvr/">preview</a> support for WebVR.
                        </span>
                    </div>
                </div>
            </div>

        </p>

        <h3>
            <a id="no-headset" class="anchor" href="#no-headset" aria-hidden="true"><span class="octicon octicon-link"></span></a>
            I don’t have a headset.
        </h3>

        <p class="no-headset">
            <img src="images/mobile.png" alt="Mobile Phone">
            The easiest way to get started is with a basic headset like <a href="https://vr.google.com/cardboard/">Google Cardboard</a>.
            Just drop your phone in and you’re ready to go. You can also use your phone with more advanced headsets like
            <a href="https://www.samsung.com/global/galaxy/gear-vr/">Samsung Gear VR</a> and
            <a href="https://vr.google.com/daydream/">Google Daydream</a>.
        </p>
        <p class="no-headset">
            <img src="images/pc.png" alt="PC">
            For the best performance and most features, you can use a VR headset connected to a computer, like
            <a href="https://www.oculus.com/rift/">Oculus Rift</a> or <a href="https://www.vive.com/">HTC VIVE</a>.
            Those will allow for higher framerates, higher resolutions, and even let you walk around in VR.
        </p>
        <p class="no-headset">
            <img src="images/laptop.png" alt="Laptop or phone">
            Or, on some sites, you can just use your computer or phone without a headset.
            You won’t be able to see in 3D or interact as fully in most VR worlds, but you can still look around in 360 degrees.
        </p>
    </main>

    <footer class="footer">
    </footer>
</div>
</body>
</html>
