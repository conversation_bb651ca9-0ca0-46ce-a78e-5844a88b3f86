# iOS owners for changing Xcode constants only.
per-file lib/xcode.star=file://infra/config/groups/ios/OWNERS

# iOS owners for iOS builder configs.
per-file subprojects/chromium/ci/chromium.fyi.star=file://infra/config/groups/ios/OWNERS
per-file subprojects/chromium/ci/chromium.mac.star=file://infra/config/groups/ios/OWNERS
per-file subprojects/chromium/try/tryserver.chromium.mac.star=file://infra/config/groups/ios/OWNERS

# For LUCI Analysis configs.
per-file chops-weetbix*=<EMAIL>
per-file chops-weetbix*=<EMAIL>
per-file luci-analysis*=<EMAIL>
per-file luci-analysis*=<EMAIL>

# For LUCI Bisection configs.
per-file luci-bisection*=<EMAIL>
per-file luci-bisection*=<EMAIL>


# For Code Coverage configs
per-file subprojects/chromium/ci/chromium.coverage.star=<EMAIL>
per-file subprojects/chromium/ci/chromium.coverage.star=<EMAIL>

# Dawn owners
per-file subprojects/chromium/ci/chromium.dawn.star=file://infra/config/groups/sheriff-rotations/DAWN_OWNERS
per-file subprojects/chromium/try/tryserver.chromium.dawn.star=file://infra/config/groups/sheriff-rotations/DAWN_OWNERS

# Fuchsia owners
per-file subprojects/chromium/ci/chromium.fuchsia.fyi.star=file://build/fuchsia/OWNERS
per-file subprojects/chromium/ci/chromium.fuchsia.star=file://build/fuchsia/OWNERS
per-file subprojects/chromium/try/tryserver.chromium.fuchsia.star=file://build/fuchsia/OWNERS

# For Android configs
per-file subprojects/chromium/ci/chromium.android.fyi.star=<EMAIL>
per-file subprojects/chromium/ci/chromium.android.star=<EMAIL>
per-file subprojects/chromium/try/tryserver.chromium.android.star=<EMAIL>

# Autosharder owners
per-file targets/autoshard_exceptions.json=<EMAIL>
per-file generated/builders/*/*/targets/*=<EMAIL>

# CBRI Branch Day Automation go/automate-branch-day
per-file generated/...=<EMAIL>
per-file milestones.json=<EMAIL>
per-file settings.json=<EMAIL>

# Chromium Bedrock
per-file subprojects/chromium/ci/chromium.bedrock.star=<EMAIL>
