{"Win x64 Builder (dbg)": {"additional_compile_targets": ["all"]}, "Win10 Tests x64 (dbg)": {"gtest_tests": [{"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "absl_hardening_tests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "absl_hardening_tests", "test_id_prefix": "ninja://third_party/abseil-cpp:absl_hardening_tests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "accessibility_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "accessibility_unittests", "test_id_prefix": "ninja://ui/accessibility:accessibility_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "angle_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "angle_unittests", "test_id_prefix": "ninja://third_party/angle/src/tests:angle_unittests/", "use_isolated_scripts_api": true}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "app_shell_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "app_shell_unittests", "test_id_prefix": "ninja://extensions/shell:app_shell_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "aura_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "aura_unittests", "test_id_prefix": "ninja://ui/aura:aura_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "base_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "base_unittests", "test_id_prefix": "ninja://base:base_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "blink_common_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "blink_common_unittests", "test_id_prefix": "ninja://third_party/blink/common:blink_common_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "blink_fuzzer_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "blink_fuzzer_unittests", "test_id_prefix": "ninja://third_party/blink/renderer/platform:blink_fuzzer_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "blink_heap_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "blink_heap_unittests", "test_id_prefix": "ninja://third_party/blink/renderer/platform/heap:blink_heap_unittests/"}, {"args": ["--git-revision=${got_revision}"], "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "blink_platform_unittests", "precommit_args": ["--gerrit-issue=${patch_issue}", "--gerrit-patchset=${patch_set}", "--buildbucket-id=${buildbucket_build_id}"], "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "blink_platform_unittests", "test_id_prefix": "ninja://third_party/blink/renderer/platform:blink_platform_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "boringssl_crypto_tests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "boringssl_crypto_tests", "test_id_prefix": "ninja://third_party/boringssl:boringssl_crypto_tests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "boringssl_ssl_tests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "boringssl_ssl_tests", "test_id_prefix": "ninja://third_party/boringssl:boringssl_ssl_tests/"}, {"args": ["--gtest_filter=-*UsingRealWebcam*"], "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "capture_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "capture_unittests", "test_id_prefix": "ninja://media/capture:capture_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "cast_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "cast_unittests", "test_id_prefix": "ninja://media/cast:cast_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "cc_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "cc_unittests", "test_id_prefix": "ninja://cc:cc_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "chrome_app_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "chrome_app_unittests", "test_id_prefix": "ninja://chrome/test:chrome_app_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "chrome_elf_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "chrome_elf_unittests", "test_id_prefix": "ninja://chrome/chrome_elf:chrome_elf_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "chromedriver_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "chromedriver_unittests", "test_id_prefix": "ninja://chrome/test/chromedriver:chromedriver_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "components_browsertests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "components_browsertests", "test_id_prefix": "ninja://components:components_browsertests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "components_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>", "shards": 2}, "test": "components_unittests", "test_id_prefix": "ninja://components:components_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "compositor_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "compositor_unittests", "test_id_prefix": "ninja://ui/compositor:compositor_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "content_browsertests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>", "shards": 8}, "test": "content_browsertests", "test_id_prefix": "ninja://content/test:content_browsertests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "content_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "content_unittests", "test_id_prefix": "ninja://content/test:content_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "crashpad_tests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "crashpad_tests", "test_id_prefix": "ninja://third_party/crashpad/crashpad:crashpad_tests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "cronet_tests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "cronet_tests", "test_id_prefix": "ninja://components/cronet:cronet_tests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "cronet_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "cronet_unittests", "test_id_prefix": "ninja://components/cronet:cronet_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "crypto_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "crypto_unittests", "test_id_prefix": "ninja://crypto:crypto_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "delayloads_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "delayloads_unittests", "test_id_prefix": "ninja://chrome/test:delayloads_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "device_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "device_unittests", "test_id_prefix": "ninja://device:device_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "display_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "display_unittests", "test_id_prefix": "ninja://ui/display:display_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "elevated_tracing_service_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "elevated_tracing_service_unittests", "test_id_prefix": "ninja://chrome/windows_services/elevated_tracing_service:elevated_tracing_service_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "elevation_service_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "elevation_service_unittests", "test_id_prefix": "ninja://chrome/elevation_service:elevation_service_unittests/"}, {"ci_only": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "env_chromium_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "env_chromium_unittests", "test_id_prefix": "ninja://third_party/leveldatabase:env_chromium_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "events_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "events_unittests", "test_id_prefix": "ninja://ui/events:events_unittests/"}, {"experiment_percentage": 100, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "extensions_browsertests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "extensions_browsertests", "test_id_prefix": "ninja://extensions:extensions_browsertests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "extensions_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "extensions_unittests", "test_id_prefix": "ninja://extensions:extensions_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "filesystem_service_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "filesystem_service_unittests", "test_id_prefix": "ninja://components/services/filesystem:filesystem_service_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "fuzzing_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "fuzzing_unittests", "test_id_prefix": "ninja://testing/libfuzzer/tests:fuzzing_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "gcm_unit_tests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "gcm_unit_tests", "test_id_prefix": "ninja://google_apis/gcm:gcm_unit_tests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "gcp_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "gcp_unittests", "test_id_prefix": "ninja://chrome/credential_provider/test:gcp_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "gfx_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "gfx_unittests", "test_id_prefix": "ninja://ui/gfx:gfx_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "gin_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "gin_unittests", "test_id_prefix": "ninja://gin:gin_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "google_apis_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "google_apis_unittests", "test_id_prefix": "ninja://google_apis:google_apis_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "gpu_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "gpu_unittests", "test_id_prefix": "ninja://gpu:gpu_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "gwp_asan_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "gwp_asan_unittests", "test_id_prefix": "ninja://components/gwp_asan:gwp_asan_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "headless_browsertests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "headless_browsertests", "test_id_prefix": "ninja://headless:headless_browsertests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "headless_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "headless_unittests", "test_id_prefix": "ninja://headless:headless_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "install_static_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "install_static_unittests", "test_id_prefix": "ninja://chrome/install_static:install_static_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "installer_util_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "integrity": "high", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "installer_util_unittests", "test_id_prefix": "ninja://chrome/installer/util:installer_util_unittests/"}, {"experiment_percentage": 100, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "interactive_ui_tests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>", "shards": 3}, "test": "interactive_ui_tests", "test_id_prefix": "ninja://chrome/test:interactive_ui_tests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "ipc_tests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "ipc_tests", "test_id_prefix": "ninja://ipc:ipc_tests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "latency_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "latency_unittests", "test_id_prefix": "ninja://ui/latency:latency_unittests/"}, {"args": ["--test-launcher-timeout=90000"], "ci_only": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "leveldb_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "leveldb_unittests", "test_id_prefix": "ninja://third_party/leveldatabase:leveldb_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "libjingle_xmpp_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "libjingle_xmpp_unittests", "test_id_prefix": "ninja://third_party/libjingle_xmpp:libjingle_xmpp_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "liburlpattern_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "liburlpattern_unittests", "test_id_prefix": "ninja://third_party/liburlpattern:liburlpattern_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "media_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "media_unittests", "test_id_prefix": "ninja://media:media_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "message_center_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "message_center_unittests", "test_id_prefix": "ninja://ui/message_center:message_center_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "midi_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "midi_unittests", "test_id_prefix": "ninja://media/midi:midi_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "mojo_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "mojo_unittests", "test_id_prefix": "ninja://mojo:mojo_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "native_theme_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "native_theme_unittests", "test_id_prefix": "ninja://ui/native_theme:native_theme_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "net_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "net_unittests", "test_id_prefix": "ninja://net:net_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "notification_helper_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "notification_helper_unittests", "test_id_prefix": "ninja://chrome/notification_helper:notification_helper_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "pdf_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "pdf_unittests", "test_id_prefix": "ninja://pdf:pdf_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "perfetto_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "perfetto_unittests", "test_id_prefix": "ninja://third_party/perfetto:perfetto_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "printing_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "printing_unittests", "test_id_prefix": "ninja://printing:printing_unittests/"}, {"ci_only": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "pthreadpool_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "pthreadpool_unittests", "test_id_prefix": "ninja://third_party/pthreadpool:pthreadpool_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "remoting_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "remoting_unittests", "test_id_prefix": "ninja://remoting:remoting_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "sbox_integration_tests", "swarming": {"dimensions": {"cpu": "x86-64", "integrity": "high", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "sbox_integration_tests", "test_id_prefix": "ninja://sandbox/win:sbox_integration_tests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "sbox_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "sbox_unittests", "test_id_prefix": "ninja://sandbox/win:sbox_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "sbox_validation_tests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "sbox_validation_tests", "test_id_prefix": "ninja://sandbox/win:sbox_validation_tests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "services_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "services_unittests", "test_id_prefix": "ninja://services:services_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "setup_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "integrity": "high", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "setup_unittests", "test_id_prefix": "ninja://chrome/installer/setup:setup_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "shell_dialogs_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "shell_dialogs_unittests", "test_id_prefix": "ninja://ui/shell_dialogs:shell_dialogs_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "skia_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "skia_unittests", "test_id_prefix": "ninja://skia:skia_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "snapshot_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "snapshot_unittests", "test_id_prefix": "ninja://ui/snapshot:snapshot_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "sql_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "sql_unittests", "test_id_prefix": "ninja://sql:sql_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "storage_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "storage_unittests", "test_id_prefix": "ninja://storage:storage_unittests/"}, {"experiment_percentage": 100, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "sync_integration_tests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>", "shards": 3}, "test": "sync_integration_tests", "test_id_prefix": "ninja://chrome/test:sync_integration_tests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "ui_base_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "ui_base_unittests", "test_id_prefix": "ninja://ui/base:ui_base_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "ui_touch_selection_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "ui_touch_selection_unittests", "test_id_prefix": "ninja://ui/touch_selection:ui_touch_selection_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "ui_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "ui_unittests", "test_id_prefix": "ninja://ui/tests:ui_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "unit_tests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "unit_tests", "test_id_prefix": "ninja://chrome/test:unit_tests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "updater_tests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "updater_tests", "test_id_prefix": "ninja://chrome/updater:updater_tests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "updater_tests_system", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "hard_timeout": 7200, "service_account": "<EMAIL>", "shards": 2}, "test": "updater_tests_system", "test_id_prefix": "ninja://chrome/updater:updater_tests_system/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "url_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "url_unittests", "test_id_prefix": "ninja://url:url_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "views_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "views_unittests", "test_id_prefix": "ninja://ui/views:views_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "viz_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "viz_unittests", "test_id_prefix": "ninja://components/viz:viz_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "vr_common_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "vr_common_unittests", "test_id_prefix": "ninja://chrome/browser/vr:vr_common_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "webkit_unit_tests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "blink_unittests", "test_id_prefix": "ninja://third_party/blink/renderer/controller:blink_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "wm_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "wm_unittests", "test_id_prefix": "ninja://ui/wm:wm_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "wtf_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "wtf_unittests", "test_id_prefix": "ninja://third_party/blink/renderer/platform/wtf:wtf_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "zlib_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "zlib_unittests", "test_id_prefix": "ninja://third_party/zlib:zlib_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "zucchini_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "zucchini_unittests", "test_id_prefix": "ninja://components/zucchini:zucchini_unittests/"}], "isolated_scripts": [{"merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "blink_python_tests", "resultdb": {"enable": true}, "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "blink_python_tests", "test_id_prefix": "ninja://:blink_python_tests/"}, {"args": ["--test-type=integration"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "chromedriver_py_tests_headless_shell", "resultdb": {"enable": true}, "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "chromedriver_py_tests_headless_shell", "test_id_prefix": "ninja://chrome/test/chromedriver:chromedriver_py_tests_headless_shell/"}, {"merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "chromedriver_replay_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "chromedriver_replay_unittests", "test_id_prefix": "ninja://chrome/test/chromedriver:chromedriver_replay_unittests/"}, {"args": ["--gtest-benchmark-name=components_perftests"], "merge": {"args": ["--smoke-test-mode"], "script": "//tools/perf/process_perf_results.py"}, "name": "components_perftests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "components_perftests", "test_id_prefix": "ninja://components:components_perftests/"}, {"experiment_percentage": 100, "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "content_shell_crash_test", "resultdb": {"enable": true, "result_format": "single"}, "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "content_shell_crash_test", "test_id_prefix": "ninja://content/shell:content_shell_crash_test/"}, {"merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "flatbuffers_unittests", "resultdb": {"enable": true, "result_format": "single"}, "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "flatbuffers_unittests", "test_id_prefix": "ninja://third_party/flatbuffers:flatbuffers_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "grit_python_unittests", "resultdb": {"enable": true}, "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "grit_python_unittests", "test_id_prefix": "ninja://tools/grit:grit_python_unittests/"}, {"args": ["--pageset-repeat=1", "--test-shard-map-filename=smoke_test_benchmark_shard_map.json", "--browser=debug_x64"], "experiment_percentage": 100, "merge": {"args": ["--smoke-test-mode"], "script": "//tools/perf/process_perf_results.py"}, "name": "performance_test_suite", "resultdb": {"enable": true}, "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "hard_timeout": 960, "service_account": "<EMAIL>", "shards": 2}, "test": "performance_test_suite", "test_id_prefix": "ninja://chrome/test:performance_test_suite/"}, {"merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "telemetry_gpu_unittests", "resultdb": {"enable": true}, "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "idempotent": false, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_unittests", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_unittests/"}, {"args": ["--jobs=1", "--extra-browser-args=--disable-gpu"], "experiment_percentage": 100, "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "telemetry_unittests", "resultdb": {"enable": true}, "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "idempotent": false, "service_account": "<EMAIL>", "shards": 8}, "test": "telemetry_unittests", "test_id_prefix": "ninja://chrome/test:telemetry_unittests/"}, {"args": ["--gtest-benchmark-name=views_perftests"], "merge": {"args": ["--smoke-test-mode"], "script": "//tools/perf/process_perf_results.py"}, "name": "views_perftests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "views_perftests", "test_id_prefix": "ninja://ui/views:views_perftests/"}]}}