{"linux-fieldtrial-rel": {"gtest_tests": [{"args": ["--disable-field-trial-config"], "ci_only": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "browser_tests_no_field_trial", "swarming": {"dimensions": {"os": "Ubuntu-22.04"}, "service_account": "<EMAIL>", "shards": 10}, "test": "browser_tests", "test_id_prefix": "ninja://chrome/test:browser_tests/"}, {"args": ["--disable-field-trial-config"], "ci_only": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "components_browsertests_no_field_trial", "swarming": {"dimensions": {"os": "Ubuntu-22.04"}, "service_account": "<EMAIL>"}, "test": "components_browsertests", "test_id_prefix": "ninja://components:components_browsertests/"}, {"args": ["--disable-field-trial-config"], "ci_only": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "interactive_ui_tests_no_field_trial", "swarming": {"dimensions": {"os": "Ubuntu-22.04"}, "service_account": "<EMAIL>"}, "test": "interactive_ui_tests", "test_id_prefix": "ninja://chrome/test:interactive_ui_tests/"}, {"args": ["--disable-field-trial-config"], "ci_only": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "sync_integration_tests_no_field_trial", "swarming": {"dimensions": {"os": "Ubuntu-22.04"}, "service_account": "<EMAIL>", "shards": 3}, "test": "sync_integration_tests", "test_id_prefix": "ninja://chrome/test:sync_integration_tests/"}]}}