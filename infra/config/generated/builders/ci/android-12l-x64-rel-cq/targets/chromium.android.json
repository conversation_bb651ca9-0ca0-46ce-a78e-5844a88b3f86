{"android-12l-x64-rel-cq": {"gtest_tests": [{"args": ["--annotation=Restriction=Tablet,Restriction=TabletOrDesktop,ImportantFormFactors=Tablet,ImportantFormFactors=TabletOrDesktop", "--git-revision=${got_revision}", "--avd-config=../../tools/android/avd/proto/android_32_google_apis_x64_foldable.textpb", "--gs-results-bucket=chromium-result-details", "--recover-devices"], "merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "chrome_public_test_apk_tablet"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "name": "chrome_public_test_apk_tablet", "precommit_args": ["--gerrit-issue=${patch_issue}", "--gerrit-patchset=${patch_set}", "--buildbucket-id=${buildbucket_build_id}"], "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"dimensions": {"cores": "8", "cpu": "x86-64", "device_os": null, "device_type": null, "os": "Ubuntu-22.04", "pool": "chromium.tests.avd"}, "named_caches": [{"name": "android_32_google_apis_x64_foldable", "path": ".android_emulator/android_32_google_apis_x64_foldable"}], "optional_dimensions": {"60": {"caches": "android_32_google_apis_x64_foldable"}}, "service_account": "<EMAIL>", "shards": 2}, "test": "chrome_public_test_apk", "test_id_prefix": "ninja://chrome/android:chrome_public_test_apk/"}]}}