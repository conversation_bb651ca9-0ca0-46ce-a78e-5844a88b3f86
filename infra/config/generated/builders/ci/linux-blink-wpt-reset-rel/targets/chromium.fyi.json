{"linux-blink-wpt-reset-rel": {"isolated_scripts": [{"args": ["--num-retries=3", "--write-run-histories-to=${ISOLATED_OUTDIR}/run_histories.json", "--additional-driver-flag=--force-browsing-instance-reset-between-tests"], "merge": {"args": ["--verbose"], "script": "//third_party/blink/tools/merge_web_test_results.py"}, "name": "blink_web_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "results_handler": "layout tests", "swarming": {"dimensions": {"os": "Ubuntu-22.04"}, "hard_timeout": 1500, "service_account": "<EMAIL>", "shards": 5}, "test": "blink_web_tests", "test_id_prefix": "ninja://:blink_web_tests/"}, {"args": ["--num-retries=3", "--write-run-histories-to=${ISOLATED_OUTDIR}/run_histories.json", "--additional-driver-flag=--force-browsing-instance-reset-between-tests"], "merge": {"args": ["--verbose"], "script": "//third_party/blink/tools/merge_web_test_results.py"}, "name": "blink_wpt_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "results_handler": "layout tests", "swarming": {"dimensions": {"os": "Ubuntu-22.04"}, "service_account": "<EMAIL>", "shards": 2}, "test": "blink_wpt_tests", "test_id_prefix": "ninja://:blink_wpt_tests/"}, {"args": ["--test-launcher-filter-file=../../third_party/blink/web_tests/TestLists/chrome.filter"], "merge": {"args": ["--verbose"], "script": "//third_party/blink/tools/merge_web_test_results.py"}, "name": "chrome_wpt_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "results_handler": "layout tests", "swarming": {"dimensions": {"os": "Ubuntu-22.04"}, "service_account": "<EMAIL>"}, "test": "chrome_wpt_tests", "test_id_prefix": "ninja://:chrome_wpt_tests/"}, {"args": ["--test-type", "testharness", "reftest", "crashtest", "print-reftest", "--inverted-test-launcher-filter-file=../../third_party/blink/web_tests/TestLists/chrome.filter", "--inverted-test-launcher-filter-file=../../third_party/blink/web_tests/TestLists/content_shell.filter"], "merge": {"args": ["--verbose"], "script": "//third_party/blink/tools/merge_web_test_results.py"}, "name": "headless_shell_wpt_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "results_handler": "layout tests", "swarming": {"dimensions": {"os": "Ubuntu-22.04"}, "service_account": "<EMAIL>", "shards": 18}, "test": "headless_shell_wpt", "test_id_prefix": "ninja://:headless_shell_wpt/"}]}}