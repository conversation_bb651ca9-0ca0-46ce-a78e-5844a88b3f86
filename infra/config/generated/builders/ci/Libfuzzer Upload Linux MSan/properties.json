{"$build/chromium_tests_builder_config": {"builder_config": {"additional_exclusions": ["infra/config/generated/builders/ci/Libfuzzer Upload Linux MSan/gn-args.json"], "builder_db": {"entries": [{"builder_id": {"bucket": "ci", "builder": "Libfuzzer Upload Linux MSan", "project": "chromium"}, "builder_spec": {"builder_group": "chromium.fuzz", "execution_mode": "COMPILE_AND_TEST", "legacy_chromium_config": {"apply_configs": ["clobber", "mb", "msan"], "build_config": "Release", "config": "chromium_clang", "target_bits": 64, "target_platform": "linux"}, "legacy_gclient_config": {"config": "chromium"}}}]}, "builder_ids": [{"bucket": "ci", "builder": "Libfuzzer Upload Linux MSan", "project": "chromium"}], "retry_failed_shards": true}}, "$build/siso": {"configs": ["builder"], "enable_cloud_monitoring": true, "enable_cloud_profiler": true, "enable_cloud_trace": true, "experiments": [], "metrics_project": "chromium-reclient-metrics", "project": "rbe-chromium-trusted", "remote_jobs": 500}, "$recipe_engine/resultdb/test_presentation": {"column_keys": [], "grouping_keys": ["status", "v.test_suite"]}, "builder_group": "chromium.fuzz", "gardener_rotations": ["chromium"], "recipe": "chromium/fuzz", "sheriff_rotations": ["chromium"], "upload_bucket": "chromium-browser-libfuzzer", "upload_directory": "msan"}