{"$build/chromium_tests_builder_config": {"builder_config": {"builder_db": {"entries": [{"builder_id": {"bucket": "ci", "builder": "mac-updater-builder-arm64-dbg", "project": "chromium"}, "builder_spec": {"builder_group": "chromium.updater", "execution_mode": "COMPILE_AND_TEST", "legacy_chromium_config": {"apply_configs": ["mb"], "build_config": "Debug", "config": "chromium", "target_bits": 64, "target_platform": "mac"}, "legacy_gclient_config": {"config": "chromium"}}}, {"builder_id": {"bucket": "ci", "builder": "mac14-arm64-updater-tester-dbg", "project": "chromium"}, "builder_spec": {"builder_group": "chromium.updater", "execution_mode": "TEST", "legacy_chromium_config": {"apply_configs": ["mb"], "build_config": "Debug", "config": "chromium", "target_bits": 64, "target_platform": "mac"}, "legacy_gclient_config": {"config": "chromium"}, "parent": {"bucket": "ci", "builder": "mac-updater-builder-arm64-dbg", "project": "chromium"}}}]}, "builder_ids": [{"bucket": "ci", "builder": "mac14-arm64-updater-tester-dbg", "project": "chromium"}], "targets_spec_directory": "src/infra/config/generated/builders/ci/mac14-arm64-updater-tester-dbg/targets"}}, "$recipe_engine/resultdb/test_presentation": {"column_keys": [], "grouping_keys": ["status", "v.test_suite"]}, "builder_group": "chromium.updater", "recipe": "chromium"}