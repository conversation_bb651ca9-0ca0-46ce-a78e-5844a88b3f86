{"$build/chromium_tests_builder_config": {"builder_config": {"builder_db": {"entries": [{"builder_id": {"bucket": "ci", "builder": "mac-arm64-rel", "project": "chromium"}, "builder_spec": {"builder_group": "chromium.mac", "execution_mode": "COMPILE_AND_TEST", "legacy_chromium_config": {"apply_configs": ["mb"], "build_config": "Release", "config": "chromium", "target_arch": "arm", "target_bits": 64, "target_platform": "mac"}, "legacy_gclient_config": {"apply_configs": ["chromium_with_telemetry_dependencies"], "config": "chromium"}}}, {"builder_id": {"bucket": "ci", "builder": "mac12-arm64-rel-tests", "project": "chromium"}, "builder_spec": {"builder_group": "chromium.mac", "execution_mode": "TEST", "legacy_chromium_config": {"apply_configs": ["mb"], "build_config": "Release", "config": "chromium", "target_arch": "arm", "target_bits": 64, "target_platform": "mac"}, "legacy_gclient_config": {"config": "chromium"}, "parent": {"bucket": "ci", "builder": "mac-arm64-rel", "project": "chromium"}}}]}, "builder_ids": [{"bucket": "ci", "builder": "mac12-arm64-rel-tests", "project": "chromium"}], "mirroring_builder_group_and_names": [{"builder": "mac12-arm64-rel", "group": "tryserver.chromium.mac"}], "retry_failed_shards": true, "targets_spec_directory": "src/infra/config/generated/builders/ci/mac12-arm64-rel-tests/targets"}}, "$recipe_engine/resultdb/test_presentation": {"column_keys": [], "grouping_keys": ["status", "v.test_suite"]}, "builder_group": "chromium.mac", "gardener_rotations": ["chromium"], "recipe": "chromium", "sheriff_rotations": ["chromium"]}