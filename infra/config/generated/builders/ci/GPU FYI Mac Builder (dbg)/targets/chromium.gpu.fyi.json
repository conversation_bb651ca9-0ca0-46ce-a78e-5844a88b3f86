{"GPU FYI Mac Builder (dbg)": {}, "Mac FYI Debug (Intel)": {"gtest_tests": [{"merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "angle_unittests", "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "display_attached": "1", "gpu": "8086:3e9b", "os": "Mac-14.5"}, "hard_timeout": 1800, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "angle_unittests", "test_id_prefix": "ninja://third_party/angle/src/tests:angle_unittests/", "use_isolated_scripts_api": true}, {"args": ["--use-cmd-decoder=passthrough", "--use-gl=angle", "--use-gpu-in-tests"], "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "gl_tests_passthrough", "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "display_attached": "1", "gpu": "8086:3e9b", "os": "Mac-14.5"}, "hard_timeout": 1800, "io_timeout": 1800, "service_account": "<EMAIL>", "shards": 2}, "test": "gl_tests", "test_id_prefix": "ninja://gpu:gl_tests/"}, {"args": ["--use-gpu-in-tests", "--git-revision=${got_revision}"], "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "gl_unittests", "precommit_args": ["--gerrit-issue=${patch_issue}", "--gerrit-patchset=${patch_set}", "--buildbucket-id=${buildbucket_build_id}"], "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "display_attached": "1", "gpu": "8086:3e9b", "os": "Mac-14.5"}, "hard_timeout": 1800, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "gl_unittests", "test_id_prefix": "ninja://ui/gl:gl_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "gpu_unittests", "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "display_attached": "1", "gpu": "8086:3e9b", "os": "Mac-14.5"}, "hard_timeout": 1800, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "gpu_unittests", "test_id_prefix": "ninja://gpu:gpu_unittests/"}, {"args": ["--gtest_filter=*Detection*", "--use-gpu-in-tests"], "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "services_unittests", "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "display_attached": "1", "gpu": "8086:3e9b", "os": "Mac-14.5"}, "hard_timeout": 1800, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "services_unittests", "test_id_prefix": "ninja://services:services_unittests/"}], "isolated_scripts": [{"args": ["context_lost", "--show-stdout", "--browser=debug", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --use-cmd-decoder=passthrough --use-gl=angle --use-angle=gl --disable-features=SkiaGraphite", "--enforce-browser-version", "--jobs=4"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "context_lost_gl_passthrough_ganesh_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "display_attached": "1", "gpu": "8086:3e9b", "os": "Mac-14.5"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["expected_color", "--show-stdout", "--browser=debug", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --use-cmd-decoder=passthrough --use-gl=angle --use-angle=gl --disable-features=SkiaGraphite", "--enforce-browser-version", "--git-revision=${got_revision}", "--dont-restore-color-profile-after-test", "--test-machine-name", "${buildername}", "--jobs=4"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "expected_color_pixel_gl_passthrough_ganesh_test", "precommit_args": ["--gerrit-issue=${patch_issue}", "--gerrit-patchset=${patch_set}", "--buildbucket-id=${buildbucket_build_id}"], "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "display_attached": "1", "gpu": "8086:3e9b", "os": "Mac-14.5"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["gpu_process", "--show-stdout", "--browser=debug", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc", "--enforce-browser-version", "--jobs=4"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "gpu_process_launch_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "display_attached": "1", "gpu": "8086:3e9b", "os": "Mac-14.5"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["hardware_accelerated_feature", "--show-stdout", "--browser=debug", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc", "--enforce-browser-version", "--jobs=4"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "hardware_accelerated_feature_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "display_attached": "1", "gpu": "8086:3e9b", "os": "Mac-14.5"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["info_collection", "--show-stdout", "--browser=debug", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --force_high_performance_gpu", "--enforce-browser-version", "--expected-vendor-id", "8086", "--expected-device-id", "3e9b", "--jobs=4"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "info_collection_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "display_attached": "1", "gpu": "8086:3e9b", "os": "Mac-14.5"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["pixel", "--show-stdout", "--browser=debug", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --use-cmd-decoder=passthrough --use-gl=angle --use-angle=gl --disable-features=SkiaGraphite", "--enforce-browser-version", "--git-revision=${got_revision}", "--dont-restore-color-profile-after-test", "--test-machine-name", "${buildername}", "--jobs=1"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "pixel_skia_gold_gl_passthrough_ganesh_test", "precommit_args": ["--gerrit-issue=${patch_issue}", "--gerrit-patchset=${patch_set}", "--buildbucket-id=${buildbucket_build_id}"], "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "display_attached": "1", "gpu": "8086:3e9b", "os": "Mac-14.5"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>", "shards": 2}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["screenshot_sync", "--show-stdout", "--browser=debug", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --use-cmd-decoder=passthrough --use-gl=angle --use-angle=gl --disable-features=SkiaGraphite", "--enforce-browser-version", "--dont-restore-color-profile-after-test", "--jobs=4"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "screenshot_sync_gl_passthrough_ganesh_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "display_attached": "1", "gpu": "8086:3e9b", "os": "Mac-14.5"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["trace_test", "--show-stdout", "--browser=debug", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc", "--enforce-browser-version", "--jobs=4"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "trace_test", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "display_attached": "1", "gpu": "8086:3e9b", "os": "Mac-14.5"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["webgl1_conformance", "--show-stdout", "--browser=debug", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --use-cmd-decoder=passthrough --use-gl=angle --use-angle=gl --disable-features=SkiaGraphite --force_high_performance_gpu", "--enforce-browser-version", "--read-abbreviated-json-results-from=../../content/test/data/gpu/webgl1_conformance_mac_runtimes.json", "--jobs=4"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "webgl_conformance_gl_passthrough_ganesh_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "display_attached": "1", "gpu": "8086:3e9b", "os": "Mac-14.5"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>", "shards": 2}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}]}, "Mac FYI Retina Debug (AMD)": {"gtest_tests": [{"merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "angle_unittests", "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "display_attached": "1", "gpu": "1002:7340", "hidpi": "1", "os": "Mac-14.4.1", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "angle_unittests", "test_id_prefix": "ninja://third_party/angle/src/tests:angle_unittests/", "use_isolated_scripts_api": true}, {"args": ["--use-cmd-decoder=passthrough", "--use-gl=angle", "--use-gpu-in-tests"], "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "gl_tests_passthrough", "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "display_attached": "1", "gpu": "1002:7340", "hidpi": "1", "os": "Mac-14.4.1", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "io_timeout": 1800, "service_account": "<EMAIL>", "shards": 2}, "test": "gl_tests", "test_id_prefix": "ninja://gpu:gl_tests/"}, {"args": ["--use-gpu-in-tests", "--git-revision=${got_revision}"], "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "gl_unittests", "precommit_args": ["--gerrit-issue=${patch_issue}", "--gerrit-patchset=${patch_set}", "--buildbucket-id=${buildbucket_build_id}"], "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "display_attached": "1", "gpu": "1002:7340", "hidpi": "1", "os": "Mac-14.4.1", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "gl_unittests", "test_id_prefix": "ninja://ui/gl:gl_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "gpu_unittests", "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "display_attached": "1", "gpu": "1002:7340", "hidpi": "1", "os": "Mac-14.4.1", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "gpu_unittests", "test_id_prefix": "ninja://gpu:gpu_unittests/"}, {"args": ["--gtest_filter=*Detection*", "--use-gpu-in-tests"], "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "services_unittests", "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "display_attached": "1", "gpu": "1002:7340", "hidpi": "1", "os": "Mac-14.4.1", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "services_unittests", "test_id_prefix": "ninja://services:services_unittests/"}], "isolated_scripts": [{"args": ["context_lost", "--show-stdout", "--browser=debug", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --use-cmd-decoder=passthrough --use-gl=angle --use-angle=gl --disable-features=SkiaGraphite", "--enforce-browser-version", "--jobs=4"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "context_lost_gl_passthrough_ganesh_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "display_attached": "1", "gpu": "1002:7340", "hidpi": "1", "os": "Mac-14.4.1", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["expected_color", "--show-stdout", "--browser=debug", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --use-cmd-decoder=passthrough --use-gl=angle --use-angle=gl --disable-features=SkiaGraphite", "--enforce-browser-version", "--git-revision=${got_revision}", "--dont-restore-color-profile-after-test", "--test-machine-name", "${buildername}", "--jobs=4"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "expected_color_pixel_gl_passthrough_ganesh_test", "precommit_args": ["--gerrit-issue=${patch_issue}", "--gerrit-patchset=${patch_set}", "--buildbucket-id=${buildbucket_build_id}"], "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "display_attached": "1", "gpu": "1002:7340", "hidpi": "1", "os": "Mac-14.4.1", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["gpu_process", "--show-stdout", "--browser=debug", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc", "--enforce-browser-version", "--jobs=4"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "gpu_process_launch_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "display_attached": "1", "gpu": "1002:7340", "hidpi": "1", "os": "Mac-14.4.1", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["hardware_accelerated_feature", "--show-stdout", "--browser=debug", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc", "--enforce-browser-version", "--jobs=4"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "hardware_accelerated_feature_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "display_attached": "1", "gpu": "1002:7340", "hidpi": "1", "os": "Mac-14.4.1", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["info_collection", "--show-stdout", "--browser=debug", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --force_high_performance_gpu", "--enforce-browser-version", "--expected-vendor-id", "1002", "--expected-device-id", "7340", "--jobs=4"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "info_collection_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "display_attached": "1", "gpu": "1002:7340", "hidpi": "1", "os": "Mac-14.4.1", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["pixel", "--show-stdout", "--browser=debug", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --use-cmd-decoder=passthrough --use-gl=angle --use-angle=gl --disable-features=SkiaGraphite", "--enforce-browser-version", "--git-revision=${got_revision}", "--dont-restore-color-profile-after-test", "--test-machine-name", "${buildername}", "--jobs=1"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "pixel_skia_gold_gl_passthrough_ganesh_test", "precommit_args": ["--gerrit-issue=${patch_issue}", "--gerrit-patchset=${patch_set}", "--buildbucket-id=${buildbucket_build_id}"], "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "display_attached": "1", "gpu": "1002:7340", "hidpi": "1", "os": "Mac-14.4.1", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>", "shards": 2}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["screenshot_sync", "--show-stdout", "--browser=debug", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --use-cmd-decoder=passthrough --use-gl=angle --use-angle=gl --disable-features=SkiaGraphite", "--enforce-browser-version", "--dont-restore-color-profile-after-test", "--jobs=4"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "screenshot_sync_gl_passthrough_ganesh_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "display_attached": "1", "gpu": "1002:7340", "hidpi": "1", "os": "Mac-14.4.1", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["trace_test", "--show-stdout", "--browser=debug", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc", "--enforce-browser-version", "--jobs=4"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "trace_test", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "display_attached": "1", "gpu": "1002:7340", "hidpi": "1", "os": "Mac-14.4.1", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["webgl1_conformance", "--show-stdout", "--browser=debug", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --use-cmd-decoder=passthrough --use-gl=angle --use-angle=gl --disable-features=SkiaGraphite --force_high_performance_gpu", "--enforce-browser-version", "--read-abbreviated-json-results-from=../../content/test/data/gpu/webgl1_conformance_mac_runtimes.json", "--jobs=4"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "webgl_conformance_gl_passthrough_ganesh_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "display_attached": "1", "gpu": "1002:7340", "hidpi": "1", "os": "Mac-14.4.1", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>", "shards": 2}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}]}}