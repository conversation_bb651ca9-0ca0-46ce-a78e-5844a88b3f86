{"$build/chromium_tests_builder_config": {"builder_config": {"additional_exclusions": ["infra/config/generated/builders/ci/GPU FYI Mac Builder (dbg)/gn-args.json"], "builder_db": {"entries": [{"builder_id": {"bucket": "ci", "builder": "GPU FYI Mac Builder (dbg)", "project": "chromium"}, "builder_spec": {"builder_group": "chromium.gpu.fyi", "execution_mode": "COMPILE_AND_TEST", "legacy_chromium_config": {"apply_configs": ["mb"], "build_config": "Debug", "config": "chromium", "target_arch": "intel", "target_bits": 64, "target_platform": "mac"}, "legacy_gclient_config": {"config": "chromium"}}}, {"builder_id": {"bucket": "ci", "builder": "Mac FYI Debug (Intel)", "project": "chromium"}, "builder_spec": {"builder_group": "chromium.gpu.fyi", "execution_mode": "TEST", "legacy_chromium_config": {"apply_configs": ["mb"], "build_config": "Debug", "config": "chromium", "target_bits": 64, "target_platform": "mac"}, "legacy_gclient_config": {"config": "chromium"}, "parent": {"bucket": "ci", "builder": "GPU FYI Mac Builder (dbg)", "project": "chromium"}, "run_tests_serially": true}}, {"builder_id": {"bucket": "ci", "builder": "Mac FYI Retina De<PERSON>g (AMD)", "project": "chromium"}, "builder_spec": {"builder_group": "chromium.gpu.fyi", "execution_mode": "TEST", "legacy_chromium_config": {"apply_configs": ["mb"], "build_config": "Debug", "config": "chromium", "target_bits": 64, "target_platform": "mac"}, "legacy_gclient_config": {"config": "chromium"}, "parent": {"bucket": "ci", "builder": "GPU FYI Mac Builder (dbg)", "project": "chromium"}, "run_tests_serially": true}}]}, "builder_ids": [{"bucket": "ci", "builder": "GPU FYI Mac Builder (dbg)", "project": "chromium"}], "builder_ids_in_scope_for_testing": [{"bucket": "ci", "builder": "Mac FYI Debug (Intel)", "project": "chromium"}, {"bucket": "ci", "builder": "Mac FYI Retina De<PERSON>g (AMD)", "project": "chromium"}], "mirroring_builder_group_and_names": [{"builder": "gpu-fyi-try-mac-amd-retina-dbg", "group": "tryserver.chromium.mac"}, {"builder": "gpu-fyi-try-mac-intel-dbg", "group": "tryserver.chromium.mac"}], "targets_spec_directory": "src/infra/config/generated/builders/ci/GPU FYI Mac Builder (dbg)/targets"}}, "$build/siso": {"configs": ["builder"], "enable_cloud_monitoring": true, "enable_cloud_profiler": true, "enable_cloud_trace": true, "experiments": [], "metrics_project": "chromium-reclient-metrics", "project": "rbe-chromium-trusted", "remote_jobs": 250}, "$recipe_engine/resultdb/test_presentation": {"column_keys": [], "grouping_keys": ["status", "v.test_suite"]}, "builder_group": "chromium.gpu.fyi", "gardener_rotations": ["chromium.gpu"], "perf_dashboard_machine_group": "ChromiumGPUFYI", "recipe": "chromium", "sheriff_rotations": ["chromium.gpu"]}