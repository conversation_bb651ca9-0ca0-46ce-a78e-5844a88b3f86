{"$build/chromium_tests_builder_config": {"builder_config": {"additional_exclusions": ["infra/config/generated/builders/ci/Linux TSan Builder/gn-args.json"], "builder_db": {"entries": [{"builder_id": {"bucket": "ci", "builder": "Linux TSan Builder", "project": "chromium"}, "builder_spec": {"build_gs_bucket": "chromium-memory-archive", "builder_group": "chromium.memory", "execution_mode": "COMPILE_AND_TEST", "legacy_chromium_config": {"apply_configs": ["mb"], "build_config": "Release", "config": "chromium_tsan2", "target_bits": 64, "target_platform": "linux"}, "legacy_gclient_config": {"config": "chromium"}}}, {"builder_id": {"bucket": "ci", "builder": "Linux TSan Tests", "project": "chromium"}, "builder_spec": {"build_gs_bucket": "chromium-memory-archive", "builder_group": "chromium.memory", "execution_mode": "TEST", "legacy_chromium_config": {"apply_configs": ["mb"], "build_config": "Release", "config": "chromium_tsan2", "target_bits": 64, "target_platform": "linux"}, "legacy_gclient_config": {"config": "chromium"}, "parent": {"bucket": "ci", "builder": "Linux TSan Builder", "project": "chromium"}}}]}, "builder_ids": [{"bucket": "ci", "builder": "Linux TSan Builder", "project": "chromium"}], "builder_ids_in_scope_for_testing": [{"bucket": "ci", "builder": "Linux TSan Tests", "project": "chromium"}], "mirroring_builder_group_and_names": [{"builder": "linux_chromium_tsan_rel_ng", "group": "tryserver.chromium.linux"}], "retry_failed_shards": true, "targets_spec_directory": "src/infra/config/generated/builders/ci/Linux TSan Builder/targets"}}, "$build/siso": {"configs": ["builder"], "enable_cloud_monitoring": true, "enable_cloud_profiler": true, "enable_cloud_trace": true, "experiments": [], "metrics_project": "chromium-reclient-metrics", "project": "rbe-chromium-trusted", "remote_jobs": 500}, "$recipe_engine/resultdb/test_presentation": {"column_keys": [], "grouping_keys": ["status", "v.test_suite"]}, "builder_group": "chromium.memory", "gardener_rotations": ["chromium"], "recipe": "chromium", "sheriff_rotations": ["chromium"]}