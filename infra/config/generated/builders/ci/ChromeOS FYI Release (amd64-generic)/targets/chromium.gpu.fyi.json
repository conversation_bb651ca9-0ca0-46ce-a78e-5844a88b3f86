{"ChromeOS FYI Release (amd64-generic)": {"additional_compile_targets": ["chromiumos_preflight"], "gtest_tests": [{"args": ["--use-cmd-decoder=passthrough", "--use-gl=angle", "--use-gpu-in-tests", "--stop-ui", "--test-launcher-filter-file=../../testing/buildbot/filters/chromeos.amd64-generic.gl_tests_passthrough.filter", "--magic-vm-cache=magic_cros_vm_cache"], "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "gl_tests_passthrough", "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "kvm": "1", "os": "Ubuntu-22.04", "pool": "chromium.tests"}, "hard_timeout": 1800, "io_timeout": 1800, "named_caches": [{"name": "cros_vm", "path": "magic_cros_vm_cache"}], "optional_dimensions": {"60": {"caches": "cros_vm"}}, "service_account": "<EMAIL>", "shards": 2}, "test": "gl_tests", "test_id_prefix": "ninja://gpu:gl_tests/"}, {"args": ["--use-gpu-in-tests", "--stop-ui", "--test-launcher-filter-file=../../testing/buildbot/filters/chromeos.gl_unittests.filter", "--git-revision=${got_revision}", "--magic-vm-cache=magic_cros_vm_cache"], "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "gl_unittests", "precommit_args": ["--gerrit-issue=${patch_issue}", "--gerrit-patchset=${patch_set}", "--buildbucket-id=${buildbucket_build_id}"], "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "kvm": "1", "os": "Ubuntu-22.04", "pool": "chromium.tests"}, "hard_timeout": 1800, "io_timeout": 1800, "named_caches": [{"name": "cros_vm", "path": "magic_cros_vm_cache"}], "optional_dimensions": {"60": {"caches": "cros_vm"}}, "service_account": "<EMAIL>"}, "test": "gl_unittests", "test_id_prefix": "ninja://ui/gl:gl_unittests/"}], "isolated_scripts": [{"args": ["context_lost", "--show-stdout", "--browser=cros-chrome", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--log-level=0 --js-flags=--expose-gc --use-cmd-decoder=passthrough --use-gl=angle", "--enforce-browser-version", "--jobs=1", "--remote=127.0.0.1", "--remote-ssh-port=9222", "--magic-vm-cache=magic_cros_vm_cache"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "context_lost_passthrough_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "kvm": "1", "os": "Ubuntu-22.04", "pool": "chromium.tests"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "named_caches": [{"name": "cros_vm", "path": "magic_cros_vm_cache"}], "optional_dimensions": {"60": {"caches": "cros_vm"}}, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["expected_color", "--show-stdout", "--browser=cros-chrome", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--log-level=0 --js-flags=--expose-gc --use-cmd-decoder=passthrough --use-gl=angle", "--enforce-browser-version", "--git-revision=${got_revision}", "--dont-restore-color-profile-after-test", "--test-machine-name", "${buildername}", "--jobs=1", "--remote=127.0.0.1", "--remote-ssh-port=9222", "--magic-vm-cache=magic_cros_vm_cache"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "expected_color_pixel_passthrough_test", "precommit_args": ["--gerrit-issue=${patch_issue}", "--gerrit-patchset=${patch_set}", "--buildbucket-id=${buildbucket_build_id}"], "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "kvm": "1", "os": "Ubuntu-22.04", "pool": "chromium.tests"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "named_caches": [{"name": "cros_vm", "path": "magic_cros_vm_cache"}], "optional_dimensions": {"60": {"caches": "cros_vm"}}, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["gpu_process", "--show-stdout", "--browser=cros-chrome", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--log-level=0 --js-flags=--expose-gc", "--enforce-browser-version", "--jobs=1", "--remote=127.0.0.1", "--remote-ssh-port=9222", "--magic-vm-cache=magic_cros_vm_cache"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "gpu_process_launch_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "kvm": "1", "os": "Ubuntu-22.04", "pool": "chromium.tests"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "named_caches": [{"name": "cros_vm", "path": "magic_cros_vm_cache"}], "optional_dimensions": {"60": {"caches": "cros_vm"}}, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["hardware_accelerated_feature", "--show-stdout", "--browser=cros-chrome", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--log-level=0 --js-flags=--expose-gc", "--enforce-browser-version", "--jobs=1", "--remote=127.0.0.1", "--remote-ssh-port=9222", "--magic-vm-cache=magic_cros_vm_cache"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "hardware_accelerated_feature_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "kvm": "1", "os": "Ubuntu-22.04", "pool": "chromium.tests"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "named_caches": [{"name": "cros_vm", "path": "magic_cros_vm_cache"}], "optional_dimensions": {"60": {"caches": "cros_vm"}}, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["info_collection", "--show-stdout", "--browser=cros-chrome", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--log-level=0 --js-flags=--expose-gc --force_high_performance_gpu", "--enforce-browser-version", "--jobs=1", "--remote=127.0.0.1", "--remote-ssh-port=9222", "--magic-vm-cache=magic_cros_vm_cache", "--expected-vendor-id", "1af4", "--expected-device-id", "1050"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "info_collection_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "kvm": "1", "os": "Ubuntu-22.04", "pool": "chromium.tests"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "named_caches": [{"name": "cros_vm", "path": "magic_cros_vm_cache"}], "optional_dimensions": {"60": {"caches": "cros_vm"}}, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["pixel", "--show-stdout", "--browser=cros-chrome", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--log-level=0 --js-flags=--expose-gc --use-cmd-decoder=passthrough --use-gl=angle", "--enforce-browser-version", "--git-revision=${got_revision}", "--dont-restore-color-profile-after-test", "--test-machine-name", "${buildername}", "--jobs=1", "--remote=127.0.0.1", "--remote-ssh-port=9222", "--magic-vm-cache=magic_cros_vm_cache"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "pixel_skia_gold_passthrough_test", "precommit_args": ["--gerrit-issue=${patch_issue}", "--gerrit-patchset=${patch_set}", "--buildbucket-id=${buildbucket_build_id}"], "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "kvm": "1", "os": "Ubuntu-22.04", "pool": "chromium.tests"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "named_caches": [{"name": "cros_vm", "path": "magic_cros_vm_cache"}], "optional_dimensions": {"60": {"caches": "cros_vm"}}, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["screenshot_sync", "--show-stdout", "--browser=cros-chrome", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--log-level=0 --js-flags=--expose-gc --use-cmd-decoder=passthrough --use-gl=angle", "--enforce-browser-version", "--dont-restore-color-profile-after-test", "--jobs=1", "--remote=127.0.0.1", "--remote-ssh-port=9222", "--magic-vm-cache=magic_cros_vm_cache"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "screenshot_sync_passthrough_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "kvm": "1", "os": "Ubuntu-22.04", "pool": "chromium.tests"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "named_caches": [{"name": "cros_vm", "path": "magic_cros_vm_cache"}], "optional_dimensions": {"60": {"caches": "cros_vm"}}, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["trace_test", "--show-stdout", "--browser=cros-chrome", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--log-level=0 --js-flags=--expose-gc", "--enforce-browser-version", "--jobs=1", "--remote=127.0.0.1", "--remote-ssh-port=9222", "--magic-vm-cache=magic_cros_vm_cache"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "trace_test", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "kvm": "1", "os": "Ubuntu-22.04", "pool": "chromium.tests"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "named_caches": [{"name": "cros_vm", "path": "magic_cros_vm_cache"}], "optional_dimensions": {"60": {"caches": "cros_vm"}}, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["webcodecs", "--show-stdout", "--browser=cros-chrome", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--log-level=0 --js-flags=--expose-gc", "--enforce-browser-version", "--jobs=1", "--remote=127.0.0.1", "--remote-ssh-port=9222", "--magic-vm-cache=magic_cros_vm_cache"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "webcodecs_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "kvm": "1", "os": "Ubuntu-22.04", "pool": "chromium.tests"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "named_caches": [{"name": "cros_vm", "path": "magic_cros_vm_cache"}], "optional_dimensions": {"60": {"caches": "cros_vm"}}, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["webgl2_conformance", "--show-stdout", "--browser=cros-chrome", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--log-level=0 --js-flags=--expose-gc --use-cmd-decoder=passthrough --use-gl=angle --use-angle=gles --force_high_performance_gpu", "--enforce-browser-version", "--webgl-conformance-version=2.0.1", "--read-abbreviated-json-results-from=../../content/test/data/gpu/webgl2_conformance_linux_runtimes.json", "--jobs=1", "--remote=127.0.0.1", "--remote-ssh-port=9222", "--magic-vm-cache=magic_cros_vm_cache"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "webgl2_conformance_gles_passthrough_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "kvm": "1", "os": "Ubuntu-22.04", "pool": "chromium.tests"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "named_caches": [{"name": "cros_vm", "path": "magic_cros_vm_cache"}], "optional_dimensions": {"60": {"caches": "cros_vm"}}, "service_account": "<EMAIL>", "shards": 30}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["webgl1_conformance", "--show-stdout", "--browser=cros-chrome", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--log-level=0 --js-flags=--expose-gc --use-angle=gles --use-cmd-decoder=passthrough --use-gl=angle --force_high_performance_gpu", "--enforce-browser-version", "--read-abbreviated-json-results-from=../../content/test/data/gpu/webgl1_conformance_linux_runtimes.json", "--jobs=1", "--remote=127.0.0.1", "--remote-ssh-port=9222", "--magic-vm-cache=magic_cros_vm_cache"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "webgl_conformance_gles_passthrough_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "kvm": "1", "os": "Ubuntu-22.04", "pool": "chromium.tests"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "named_caches": [{"name": "cros_vm", "path": "magic_cros_vm_cache"}], "optional_dimensions": {"60": {"caches": "cros_vm"}}, "service_account": "<EMAIL>", "shards": 6}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["webrtc", "--show-stdout", "--browser=cros-chrome", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--log-level=0 --js-flags=--expose-gc", "--enforce-browser-version", "--jobs=1", "--remote=127.0.0.1", "--remote-ssh-port=9222", "--magic-vm-cache=magic_cros_vm_cache"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "webrtc_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "kvm": "1", "os": "Ubuntu-22.04", "pool": "chromium.tests"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "named_caches": [{"name": "cros_vm", "path": "magic_cros_vm_cache"}], "optional_dimensions": {"60": {"caches": "cros_vm"}}, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}]}}