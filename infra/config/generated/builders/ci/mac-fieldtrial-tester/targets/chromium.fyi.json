{"mac-fieldtrial-tester": {"gtest_tests": [{"args": ["--disable-field-trial-config"], "ci_only": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "accessibility_unittests_no_field_trial", "swarming": {"dimensions": {"cpu": "arm64", "os": "Mac-15", "pool": "chromium.tests.finch"}, "service_account": "<EMAIL>"}, "test": "accessibility_unittests", "test_id_prefix": "ninja://ui/accessibility:accessibility_unittests/"}, {"args": ["--disable-field-trial-config"], "ci_only": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "components_browsertests_no_field_trial", "swarming": {"dimensions": {"cpu": "arm64", "os": "Mac-15", "pool": "chromium.tests.finch"}, "service_account": "<EMAIL>"}, "test": "components_browsertests", "test_id_prefix": "ninja://components:components_browsertests/"}, {"args": ["--disable-field-trial-config"], "ci_only": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "content_browsertests_no_field_trial", "swarming": {"dimensions": {"cpu": "arm64", "os": "Mac-15", "pool": "chromium.tests.finch"}, "service_account": "<EMAIL>", "shards": 8}, "test": "content_browsertests", "test_id_prefix": "ninja://content/test:content_browsertests/"}, {"args": ["--disable-field-trial-config"], "ci_only": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "interactive_ui_tests_no_field_trial", "swarming": {"dimensions": {"cpu": "arm64", "os": "Mac-15", "pool": "chromium.tests.finch"}, "service_account": "<EMAIL>"}, "test": "interactive_ui_tests", "test_id_prefix": "ninja://chrome/test:interactive_ui_tests/"}, {"args": ["--disable-field-trial-config"], "ci_only": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "sync_integration_tests_no_field_trial", "swarming": {"dimensions": {"cpu": "arm64", "os": "Mac-15", "pool": "chromium.tests.finch"}, "service_account": "<EMAIL>", "shards": 3}, "test": "sync_integration_tests", "test_id_prefix": "ninja://chrome/test:sync_integration_tests/"}]}}