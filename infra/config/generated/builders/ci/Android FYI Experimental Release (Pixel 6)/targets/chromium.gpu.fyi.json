{"Android FYI Experimental Release (Pixel 6)": {"gtest_tests": [{"args": ["-v"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "angle_unittests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"device_os": "AP1A.240405.002", "device_os_type": "userdebug", "device_type": "oriole", "os": "Android", "pool": "chromium.tests.gpu"}, "expiration": 21600, "hard_timeout": 1800, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "angle_unittests", "test_id_prefix": "ninja://third_party/angle/src/tests:angle_unittests/", "use_isolated_scripts_api": true}, {"args": ["--use-cmd-decoder=passthrough", "--use-gl=angle", "--gs-results-bucket=chromium-result-details", "--recover-devices"], "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "gl_tests_passthrough", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"device_os": "AP1A.240405.002", "device_os_type": "userdebug", "device_type": "oriole", "os": "Android", "pool": "chromium.tests.gpu"}, "expiration": 21600, "hard_timeout": 1800, "io_timeout": 1800, "service_account": "<EMAIL>", "shards": 2}, "test": "gl_tests", "test_id_prefix": "ninja://gpu:gl_tests/"}, {"args": ["--use-cmd-decoder=validating", "--gs-results-bucket=chromium-result-details", "--recover-devices"], "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "gl_tests_validating", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"device_os": "AP1A.240405.002", "device_os_type": "userdebug", "device_type": "oriole", "os": "Android", "pool": "chromium.tests.gpu"}, "expiration": 21600, "hard_timeout": 1800, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "gl_tests", "test_id_prefix": "ninja://gpu:gl_tests/"}, {"args": ["--git-revision=${got_revision}", "--gs-results-bucket=chromium-result-details", "--recover-devices"], "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "gl_unittests", "precommit_args": ["--gerrit-issue=${patch_issue}", "--gerrit-patchset=${patch_set}", "--buildbucket-id=${buildbucket_build_id}"], "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"device_os": "AP1A.240405.002", "device_os_type": "userdebug", "device_type": "oriole", "os": "Android", "pool": "chromium.tests.gpu"}, "expiration": 21600, "hard_timeout": 1800, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "gl_unittests", "test_id_prefix": "ninja://ui/gl:gl_unittests/"}, {"args": ["--gs-results-bucket=chromium-result-details", "--recover-devices"], "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "gpu_unittests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"device_os": "AP1A.240405.002", "device_os_type": "userdebug", "device_type": "oriole", "os": "Android", "pool": "chromium.tests.gpu"}, "expiration": 21600, "hard_timeout": 1800, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "gpu_unittests", "test_id_prefix": "ninja://gpu:gpu_unittests/"}], "isolated_scripts": [{"args": ["context_lost", "--show-stdout", "--browser=android-chromium", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --use-cmd-decoder=passthrough --use-gl=angle --disable-features=SkiaGraphite", "--enforce-browser-version", "--jobs=1", "--initial-find-device-attempts=3"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "context_lost_passthrough_ganesh_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"device_os": "AP1A.240405.002", "device_os_type": "userdebug", "device_type": "oriole", "os": "Android", "pool": "chromium.tests.gpu"}, "expiration": 21600, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test_android_chrome", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test_android_chrome/"}, {"args": ["context_lost", "--show-stdout", "--browser=android-chromium", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --use-cmd-decoder=passthrough --use-gl=angle --enable-features=SkiaGraphite", "--enforce-browser-version", "--jobs=1", "--initial-find-device-attempts=3"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "context_lost_passthrough_graphite_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"device_os": "AP1A.240405.002", "device_os_type": "userdebug", "device_type": "oriole", "os": "Android", "pool": "chromium.tests.gpu"}, "expiration": 21600, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test_android_chrome", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test_android_chrome/"}, {"args": ["context_lost", "--show-stdout", "--browser=android-chromium", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --use-cmd-decoder=validating", "--enforce-browser-version", "--jobs=1", "--initial-find-device-attempts=3"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "context_lost_validating_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"device_os": "AP1A.240405.002", "device_os_type": "userdebug", "device_type": "oriole", "os": "Android", "pool": "chromium.tests.gpu"}, "expiration": 21600, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test_android_chrome", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test_android_chrome/"}, {"args": ["expected_color", "--show-stdout", "--browser=android-chromium", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --use-cmd-decoder=passthrough --use-gl=angle --disable-features=SkiaGraphite --force-online-connection-state-for-indicator", "--enforce-browser-version", "--git-revision=${got_revision}", "--dont-restore-color-profile-after-test", "--test-machine-name", "${buildername}", "--jobs=1", "--initial-find-device-attempts=3"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "expected_color_pixel_passthrough_ganesh_test", "precommit_args": ["--gerrit-issue=${patch_issue}", "--gerrit-patchset=${patch_set}", "--buildbucket-id=${buildbucket_build_id}"], "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"device_os": "AP1A.240405.002", "device_os_type": "userdebug", "device_type": "oriole", "os": "Android", "pool": "chromium.tests.gpu"}, "expiration": 21600, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test_android_chrome", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test_android_chrome/"}, {"args": ["expected_color", "--show-stdout", "--browser=android-chromium", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --use-cmd-decoder=passthrough --use-gl=angle --enable-features=SkiaGraphite --force-online-connection-state-for-indicator", "--enforce-browser-version", "--git-revision=${got_revision}", "--dont-restore-color-profile-after-test", "--test-machine-name", "${buildername}", "--jobs=1", "--initial-find-device-attempts=3"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "expected_color_pixel_passthrough_graphite_test", "precommit_args": ["--gerrit-issue=${patch_issue}", "--gerrit-patchset=${patch_set}", "--buildbucket-id=${buildbucket_build_id}"], "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"device_os": "AP1A.240405.002", "device_os_type": "userdebug", "device_type": "oriole", "os": "Android", "pool": "chromium.tests.gpu"}, "expiration": 21600, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test_android_chrome", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test_android_chrome/"}, {"args": ["expected_color", "--show-stdout", "--browser=android-chromium", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --use-cmd-decoder=validating --force-online-connection-state-for-indicator", "--enforce-browser-version", "--git-revision=${got_revision}", "--dont-restore-color-profile-after-test", "--test-machine-name", "${buildername}", "--jobs=1", "--initial-find-device-attempts=3"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "expected_color_pixel_validating_test", "precommit_args": ["--gerrit-issue=${patch_issue}", "--gerrit-patchset=${patch_set}", "--buildbucket-id=${buildbucket_build_id}"], "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"device_os": "AP1A.240405.002", "device_os_type": "userdebug", "device_type": "oriole", "os": "Android", "pool": "chromium.tests.gpu"}, "expiration": 21600, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test_android_chrome", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test_android_chrome/"}, {"args": ["gpu_process", "--show-stdout", "--browser=android-chromium", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc", "--enforce-browser-version", "--jobs=1", "--initial-find-device-attempts=3"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "gpu_process_launch_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"device_os": "AP1A.240405.002", "device_os_type": "userdebug", "device_type": "oriole", "os": "Android", "pool": "chromium.tests.gpu"}, "expiration": 21600, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test_android_chrome", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test_android_chrome/"}, {"args": ["hardware_accelerated_feature", "--show-stdout", "--browser=android-chromium", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc", "--enforce-browser-version", "--jobs=1", "--initial-find-device-attempts=3"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "hardware_accelerated_feature_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"device_os": "AP1A.240405.002", "device_os_type": "userdebug", "device_type": "oriole", "os": "Android", "pool": "chromium.tests.gpu"}, "expiration": 21600, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test_android_chrome", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test_android_chrome/"}, {"args": ["info_collection", "--show-stdout", "--browser=android-chromium", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --force_high_performance_gpu", "--enforce-browser-version", "--expected-vendor-id", "13b5", "--expected-device-id", "********", "--expected-device-id", "********", "--jobs=1", "--initial-find-device-attempts=3"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "info_collection_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"device_os": "AP1A.240405.002", "device_os_type": "userdebug", "device_type": "oriole", "os": "Android", "pool": "chromium.tests.gpu"}, "expiration": 21600, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test_android_chrome", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test_android_chrome/"}, {"args": ["pixel", "--show-stdout", "--browser=android-chromium", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --use-cmd-decoder=passthrough --use-gl=angle --disable-features=SkiaGraphite --force-online-connection-state-for-indicator", "--enforce-browser-version", "--git-revision=${got_revision}", "--dont-restore-color-profile-after-test", "--test-machine-name", "${buildername}", "--jobs=1", "--initial-find-device-attempts=3"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "pixel_skia_gold_passthrough_ganesh_test", "precommit_args": ["--gerrit-issue=${patch_issue}", "--gerrit-patchset=${patch_set}", "--buildbucket-id=${buildbucket_build_id}"], "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"device_os": "AP1A.240405.002", "device_os_type": "userdebug", "device_type": "oriole", "os": "Android", "pool": "chromium.tests.gpu"}, "expiration": 21600, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test_android_chrome", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test_android_chrome/"}, {"args": ["pixel", "--show-stdout", "--browser=android-chromium", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --use-cmd-decoder=passthrough --use-gl=angle --enable-features=SkiaGraphite --force-online-connection-state-for-indicator", "--enforce-browser-version", "--git-revision=${got_revision}", "--dont-restore-color-profile-after-test", "--test-machine-name", "${buildername}", "--jobs=1", "--initial-find-device-attempts=3"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "pixel_skia_gold_passthrough_graphite_test", "precommit_args": ["--gerrit-issue=${patch_issue}", "--gerrit-patchset=${patch_set}", "--buildbucket-id=${buildbucket_build_id}"], "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"device_os": "AP1A.240405.002", "device_os_type": "userdebug", "device_type": "oriole", "os": "Android", "pool": "chromium.tests.gpu"}, "expiration": 21600, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test_android_chrome", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test_android_chrome/"}, {"args": ["pixel", "--show-stdout", "--browser=android-chromium", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --use-cmd-decoder=validating --force-online-connection-state-for-indicator", "--enforce-browser-version", "--git-revision=${got_revision}", "--dont-restore-color-profile-after-test", "--test-machine-name", "${buildername}", "--jobs=1", "--initial-find-device-attempts=3"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "pixel_skia_gold_validating_test", "precommit_args": ["--gerrit-issue=${patch_issue}", "--gerrit-patchset=${patch_set}", "--buildbucket-id=${buildbucket_build_id}"], "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"device_os": "AP1A.240405.002", "device_os_type": "userdebug", "device_type": "oriole", "os": "Android", "pool": "chromium.tests.gpu"}, "expiration": 21600, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test_android_chrome", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test_android_chrome/"}, {"args": ["screenshot_sync", "--show-stdout", "--browser=android-chromium", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --use-cmd-decoder=passthrough --use-gl=angle --disable-features=SkiaGraphite --force-online-connection-state-for-indicator", "--enforce-browser-version", "--dont-restore-color-profile-after-test", "--jobs=1", "--initial-find-device-attempts=3"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "screenshot_sync_passthrough_ganesh_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"device_os": "AP1A.240405.002", "device_os_type": "userdebug", "device_type": "oriole", "os": "Android", "pool": "chromium.tests.gpu"}, "expiration": 21600, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test_android_chrome", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test_android_chrome/"}, {"args": ["screenshot_sync", "--show-stdout", "--browser=android-chromium", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --use-cmd-decoder=passthrough --use-gl=angle --enable-features=SkiaGraphite --force-online-connection-state-for-indicator", "--enforce-browser-version", "--dont-restore-color-profile-after-test", "--jobs=1", "--initial-find-device-attempts=3"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "screenshot_sync_passthrough_graphite_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"device_os": "AP1A.240405.002", "device_os_type": "userdebug", "device_type": "oriole", "os": "Android", "pool": "chromium.tests.gpu"}, "expiration": 21600, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test_android_chrome", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test_android_chrome/"}, {"args": ["screenshot_sync", "--show-stdout", "--browser=android-chromium", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --use-cmd-decoder=validating --force-online-connection-state-for-indicator", "--enforce-browser-version", "--dont-restore-color-profile-after-test", "--jobs=1", "--initial-find-device-attempts=3"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "screenshot_sync_validating_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"device_os": "AP1A.240405.002", "device_os_type": "userdebug", "device_type": "oriole", "os": "Android", "pool": "chromium.tests.gpu"}, "expiration": 21600, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test_android_chrome", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test_android_chrome/"}, {"args": ["trace_test", "--show-stdout", "--browser=android-chromium", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc", "--enforce-browser-version", "--jobs=1", "--initial-find-device-attempts=3"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "trace_test", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"device_os": "AP1A.240405.002", "device_os_type": "userdebug", "device_type": "oriole", "os": "Android", "pool": "chromium.tests.gpu"}, "expiration": 21600, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test_android_chrome", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test_android_chrome/"}, {"args": ["webcodecs", "--show-stdout", "--browser=android-chromium", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --use-cmd-decoder=validating --enable-features=SkiaGraphite", "--enforce-browser-version", "--jobs=1", "--initial-find-device-attempts=3"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "webcodecs_graphite_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"device_os": "AP1A.240405.002", "device_os_type": "userdebug", "device_type": "oriole", "os": "Android", "pool": "chromium.tests.gpu"}, "expiration": 21600, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test_android_chrome", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test_android_chrome/"}, {"args": ["webcodecs", "--show-stdout", "--browser=android-chromium", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --use-cmd-decoder=validating --disable-features=SkiaGraphite", "--enforce-browser-version", "--jobs=1", "--initial-find-device-attempts=3"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "webcodecs_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"device_os": "AP1A.240405.002", "device_os_type": "userdebug", "device_type": "oriole", "os": "Android", "pool": "chromium.tests.gpu"}, "expiration": 21600, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test_android_chrome", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test_android_chrome/"}, {"args": ["webgl1_conformance", "--show-stdout", "--browser=android-chromium", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --use-angle=gles --use-cmd-decoder=passthrough --use-gl=angle --disable-features=SkiaGraphite --force_high_performance_gpu", "--enforce-browser-version", "--read-abbreviated-json-results-from=../../content/test/data/gpu/webgl1_conformance_android_runtimes.json", "--jobs=1", "--initial-find-device-attempts=3"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "webgl_conformance_gles_passthrough_ganesh_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"device_os": "AP1A.240405.002", "device_os_type": "userdebug", "device_type": "oriole", "os": "Android", "pool": "chromium.tests.gpu"}, "expiration": 21600, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>", "shards": 6}, "test": "telemetry_gpu_integration_test_android_chrome", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test_android_chrome/"}, {"args": ["webgl1_conformance", "--show-stdout", "--browser=android-chromium", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --use-angle=gles --use-cmd-decoder=passthrough --use-gl=angle --enable-features=SkiaGraphite --force_high_performance_gpu", "--enforce-browser-version", "--read-abbreviated-json-results-from=../../content/test/data/gpu/webgl1_conformance_android_runtimes.json", "--jobs=1", "--initial-find-device-attempts=3"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "webgl_conformance_gles_passthrough_graphite_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"device_os": "AP1A.240405.002", "device_os_type": "userdebug", "device_type": "oriole", "os": "Android", "pool": "chromium.tests.gpu"}, "expiration": 21600, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>", "shards": 3}, "test": "telemetry_gpu_integration_test_android_chrome", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test_android_chrome/"}, {"args": ["webgl1_conformance", "--show-stdout", "--browser=android-chromium", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --use-cmd-decoder=validating --disable-features=SkiaGraphite --force_high_performance_gpu", "--enforce-browser-version", "--read-abbreviated-json-results-from=../../content/test/data/gpu/webgl1_conformance_android_runtimes.json", "--jobs=1", "--initial-find-device-attempts=3"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "webgl_conformance_validating_ganesh_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"device_os": "AP1A.240405.002", "device_os_type": "userdebug", "device_type": "oriole", "os": "Android", "pool": "chromium.tests.gpu"}, "expiration": 21600, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>", "shards": 6}, "test": "telemetry_gpu_integration_test_android_chrome", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test_android_chrome/"}, {"args": ["webgl1_conformance", "--show-stdout", "--browser=android-chromium", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --use-cmd-decoder=validating --enable-features=SkiaGraphite --force_high_performance_gpu", "--enforce-browser-version", "--read-abbreviated-json-results-from=../../content/test/data/gpu/webgl1_conformance_android_runtimes.json", "--jobs=1", "--initial-find-device-attempts=3"], "ci_only": true, "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "webgl_conformance_validating_graphite_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"device_os": "AP1A.240405.002", "device_os_type": "userdebug", "device_type": "oriole", "os": "Android", "pool": "chromium.tests.gpu"}, "expiration": 21600, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>", "shards": 6}, "test": "telemetry_gpu_integration_test_android_chrome", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test_android_chrome/"}, {"args": ["webrtc", "--show-stdout", "--browser=android-chromium", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --use-cmd-decoder=validating --enable-features=SkiaGraphite", "--enforce-browser-version", "--jobs=1", "--initial-find-device-attempts=3"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "webrtc_graphite_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"device_os": "AP1A.240405.002", "device_os_type": "userdebug", "device_type": "oriole", "os": "Android", "pool": "chromium.tests.gpu"}, "expiration": 21600, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test_android_chrome", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test_android_chrome/"}, {"args": ["webrtc", "--show-stdout", "--browser=android-chromium", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --use-cmd-decoder=validating --disable-features=SkiaGraphite", "--enforce-browser-version", "--jobs=1", "--initial-find-device-attempts=3"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "webrtc_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"device_os": "AP1A.240405.002", "device_os_type": "userdebug", "device_type": "oriole", "os": "Android", "pool": "chromium.tests.gpu"}, "expiration": 21600, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test_android_chrome", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test_android_chrome/"}]}}