{"$build/chromium_tests_builder_config": {"builder_config": {"builder_db": {"entries": [{"builder_id": {"bucket": "ci", "builder": "Android FYI Release (Pixel 2)", "project": "chromium"}, "builder_spec": {"builder_group": "chromium.gpu.fyi", "execution_mode": "TEST", "legacy_android_config": {"config": "base_config"}, "legacy_chromium_config": {"apply_configs": ["mb"], "build_config": "Release", "config": "main_builder", "target_arch": "arm", "target_bits": 32, "target_platform": "android"}, "legacy_gclient_config": {"apply_configs": ["android"], "config": "chromium"}, "parent": {"bucket": "ci", "builder": "GPU FYI Android arm Builder", "project": "chromium"}}}, {"builder_id": {"bucket": "ci", "builder": "GPU FYI Android arm Builder", "project": "chromium"}, "builder_spec": {"builder_group": "chromium.gpu.fyi", "execution_mode": "COMPILE_AND_TEST", "legacy_android_config": {"config": "base_config"}, "legacy_chromium_config": {"apply_configs": ["mb"], "build_config": "Release", "config": "main_builder", "target_arch": "arm", "target_bits": 32, "target_platform": "android"}, "legacy_gclient_config": {"apply_configs": ["android"], "config": "chromium"}}}]}, "builder_ids": [{"bucket": "ci", "builder": "Android FYI Release (Pixel 2)", "project": "chromium"}], "mirroring_builder_group_and_names": [{"builder": "gpu-fyi-try-android-p-pixel-2-32", "group": "tryserver.chromium.android"}], "targets_spec_directory": "src/infra/config/generated/builders/ci/Android FYI Release (Pixel 2)/targets"}}, "$recipe_engine/resultdb/test_presentation": {"column_keys": [], "grouping_keys": ["status", "v.test_suite"]}, "builder_group": "chromium.gpu.fyi", "gardener_rotations": ["chromium.gpu"], "perf_dashboard_machine_group": "ChromiumGPUFYI", "recipe": "chromium", "sheriff_rotations": ["chromium.gpu"]}