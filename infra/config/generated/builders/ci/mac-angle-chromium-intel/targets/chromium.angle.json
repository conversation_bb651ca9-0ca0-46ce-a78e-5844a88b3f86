{"mac-angle-chromium-intel": {"gtest_tests": [{"args": ["--use-cmd-decoder=passthrough", "--use-gl=angle", "--use-gpu-in-tests"], "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "gl_tests_passthrough", "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "display_attached": "1", "gpu": "8086:3e9b", "os": "Mac-14.5"}, "service_account": "<EMAIL>", "shards": 2}, "test": "gl_tests", "test_id_prefix": "ninja://gpu:gl_tests/"}, {"args": ["--use-gpu-in-tests", "--git-revision=${got_revision}"], "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "gl_unittests", "precommit_args": ["--gerrit-issue=${patch_issue}", "--gerrit-patchset=${patch_set}", "--buildbucket-id=${buildbucket_build_id}"], "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "display_attached": "1", "gpu": "8086:3e9b", "os": "Mac-14.5"}, "service_account": "<EMAIL>"}, "test": "gl_unittests", "test_id_prefix": "ninja://ui/gl:gl_unittests/"}], "isolated_scripts": [{"args": ["info_collection", "--show-stdout", "--browser=release", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --force_high_performance_gpu", "--enforce-browser-version", "--expected-vendor-id", "8086", "--expected-device-id", "3e9b", "--jobs=4"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "info_collection_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "display_attached": "1", "gpu": "8086:3e9b", "os": "Mac-14.5"}, "idempotent": false, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["webgl2_conformance", "--show-stdout", "--browser=release", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --use-cmd-decoder=passthrough --use-gl=angle --use-angle=gl --disable-features=SkiaGraphite --force_high_performance_gpu", "--enforce-browser-version", "--webgl-conformance-version=2.0.1", "--read-abbreviated-json-results-from=../../content/test/data/gpu/webgl2_conformance_mac_runtimes.json", "--jobs=2"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "webgl2_conformance_gl_passthrough_ganesh_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "display_attached": "1", "gpu": "8086:3e9b", "os": "Mac-14.5"}, "idempotent": false, "service_account": "<EMAIL>", "shards": 20}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["webgl2_conformance", "--show-stdout", "--browser=release", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --use-cmd-decoder=passthrough --use-gl=angle --use-angle=metal --enable-features=SkiaGraphite --enable-features=EGLDualGPURendering,ForceHighPerformanceGPUForWebGL", "--enforce-browser-version", "--enable-metal-debug-layers", "--webgl-conformance-version=2.0.1", "--read-abbreviated-json-results-from=../../content/test/data/gpu/webgl2_conformance_mac_runtimes.json", "--jobs=2"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "webgl2_conformance_metal_passthrough_graphite_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "display_attached": "1", "gpu": "8086:3e9b", "os": "Mac-14.5"}, "idempotent": false, "service_account": "<EMAIL>", "shards": 20}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["webgl1_conformance", "--show-stdout", "--browser=release", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --use-cmd-decoder=passthrough --use-gl=angle --use-angle=gl --disable-features=SkiaGraphite --force_high_performance_gpu", "--enforce-browser-version", "--read-abbreviated-json-results-from=../../content/test/data/gpu/webgl1_conformance_mac_runtimes.json", "--jobs=4"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "webgl_conformance_gl_passthrough_ganesh_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "display_attached": "1", "gpu": "8086:3e9b", "os": "Mac-14.5"}, "idempotent": false, "service_account": "<EMAIL>", "shards": 2}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["webgl1_conformance", "--show-stdout", "--browser=release", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --use-cmd-decoder=passthrough --use-gl=angle --use-angle=metal --disable-features=SkiaGraphite --enable-features=EGLDualGPURendering,ForceHighPerformanceGPUForWebGL", "--enforce-browser-version", "--enable-metal-debug-layers", "--read-abbreviated-json-results-from=../../content/test/data/gpu/webgl1_conformance_mac_runtimes.json", "--jobs=4"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "webgl_conformance_metal_passthrough_ganesh_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "display_attached": "1", "gpu": "8086:3e9b", "os": "Mac-14.5"}, "idempotent": false, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["webgl1_conformance", "--show-stdout", "--browser=release", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --use-cmd-decoder=passthrough --use-gl=angle --use-angle=metal --enable-features=SkiaGraphite --enable-features=EGLDualGPURendering,ForceHighPerformanceGPUForWebGL", "--enforce-browser-version", "--enable-metal-debug-layers", "--read-abbreviated-json-results-from=../../content/test/data/gpu/webgl1_conformance_mac_runtimes.json", "--jobs=4"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "webgl_conformance_metal_passthrough_graphite_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "display_attached": "1", "gpu": "8086:3e9b", "os": "Mac-14.5"}, "idempotent": false, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["webgl1_conformance", "--show-stdout", "--browser=release", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --use-cmd-decoder=passthrough --use-gl=angle --use-angle=swiftshader --force_high_performance_gpu", "--enforce-browser-version", "--jobs=4", "--test-filter=conformance/rendering/gl-drawelements.html"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "webgl_conformance_swangle_passthrough_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"cpu": "x86-64", "display_attached": "1", "gpu": "8086:3e9b", "os": "Mac-14.5"}, "idempotent": false, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}]}}