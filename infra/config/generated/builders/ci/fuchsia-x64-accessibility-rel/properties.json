{"$build/chromium_tests_builder_config": {"builder_config": {"additional_exclusions": ["infra/config/generated/builders/ci/fuchsia-x64-accessibility-rel/gn-args.json"], "builder_db": {"entries": [{"builder_id": {"bucket": "ci", "builder": "fuchsia-x64-accessibility-rel", "project": "chromium"}, "builder_spec": {"builder_group": "chromium.accessibility", "execution_mode": "COMPILE_AND_TEST", "legacy_chromium_config": {"apply_configs": ["mb"], "build_config": "Release", "config": "chromium", "target_bits": 64, "target_platform": "fuchsia"}, "legacy_gclient_config": {"apply_configs": ["fuchsia_x64"], "config": "chromium"}}}]}, "builder_ids": [{"bucket": "ci", "builder": "fuchsia-x64-accessibility-rel", "project": "chromium"}], "mirroring_builder_group_and_names": [{"builder": "fuchsia-x64-accessibility-rel", "group": "tryserver.chromium.accessibility"}], "targets_spec_directory": "src/infra/config/generated/builders/ci/fuchsia-x64-accessibility-rel/targets"}}, "$build/siso": {"configs": ["builder"], "enable_cloud_monitoring": true, "enable_cloud_profiler": true, "enable_cloud_trace": true, "experiments": [], "metrics_project": "chromium-reclient-metrics", "project": "rbe-chromium-trusted", "remote_jobs": 500}, "$recipe_engine/resultdb/test_presentation": {"column_keys": [], "grouping_keys": ["status", "v.test_suite"]}, "builder_group": "chromium.accessibility", "recipe": "chromium"}