{"$build/chromium_tests_builder_config": {"builder_config": {"additional_exclusions": ["infra/config/generated/builders/ci/ASAN Release/gn-args.json"], "builder_db": {"entries": [{"builder_id": {"bucket": "ci", "builder": "ASAN Release", "project": "chromium"}, "builder_spec": {"builder_group": "chromium.fuzz", "clusterfuzz_archive": {"archive_name_prefix": "asan", "gs_acl": "public-read", "gs_bucket": "chromium-browser-asan"}, "execution_mode": "COMPILE_AND_TEST", "legacy_chromium_config": {"apply_configs": ["mb", "clobber"], "build_config": "Release", "config": "chromium_asan", "target_bits": 64, "target_platform": "linux"}, "legacy_gclient_config": {"config": "chromium"}}}]}, "builder_ids": [{"bucket": "ci", "builder": "ASAN Release", "project": "chromium"}], "mirroring_builder_group_and_names": [{"builder": "linux-asan-rel", "group": "tryserver.chromium.fuzz"}], "retry_failed_shards": true, "targets_spec_directory": "src/infra/config/generated/builders/ci/ASAN Release/targets"}}, "$build/siso": {"configs": ["builder"], "enable_cloud_monitoring": true, "enable_cloud_profiler": true, "enable_cloud_trace": true, "experiments": [], "metrics_project": "chromium-reclient-metrics", "project": "rbe-chromium-trusted", "remote_jobs": 250}, "$recipe_engine/resultdb/test_presentation": {"column_keys": [], "grouping_keys": ["status", "v.test_suite"]}, "builder_group": "chromium.fuzz", "gardener_rotations": ["chromium"], "recipe": "chromium", "sheriff_rotations": ["chromium"]}