{"win-arm64-updater-builder-dbg": {"additional_compile_targets": ["chrome/updater:all"]}, "win11-arm64-updater-tester-dbg": {"gtest_tests": [{"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "updater_tests", "swarming": {"dimensions": {"cpu": "arm64", "integrity": "high", "os": "Windows-11", "pool": "chromium.tests"}, "service_account": "<EMAIL>"}, "test": "updater_tests", "test_id_prefix": "ninja://chrome/updater:updater_tests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "updater_tests_system", "swarming": {"dimensions": {"cpu": "arm64", "integrity": "high", "os": "Windows-11", "pool": "chromium.tests"}, "hard_timeout": 7200, "service_account": "<EMAIL>", "shards": 2}, "test": "updater_tests_system", "test_id_prefix": "ninja://chrome/updater:updater_tests_system/"}]}}