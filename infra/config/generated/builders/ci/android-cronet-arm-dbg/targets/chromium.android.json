{"android-cronet-arm-dbg": {"additional_compile_targets": ["cronet_package", "cronet_perf_test_apk", "cronet_sample_test_apk", "cronet_smoketests_missing_native_library_instrumentation_apk", "cronet_smoketests_platform_only_instrumentation_apk", "cronet_test_instrumentation_apk", "cronet_tests_android", "cronet_unittests_android", "net_unittests"], "isolated_scripts": [{"merge": {"args": ["--upload-skia-json"], "script": "//tools/perf/process_perf_results.py"}, "name": "cronet_sizes", "resultdb": {"enable": true, "result_file": "${ISOLATED_OUTDIR}/sizes/test_results.json", "result_format": "single"}, "swarming": {"dimensions": {"cpu": "x86-64", "os": "Ubuntu-22.04"}, "service_account": "<EMAIL>"}, "test": "cronet_sizes", "test_id_prefix": "ninja://components/cronet/android:cronet_sizes/"}]}}