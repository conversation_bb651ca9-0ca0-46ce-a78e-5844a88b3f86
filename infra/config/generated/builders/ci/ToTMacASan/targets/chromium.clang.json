{"ToTMacASan": {"additional_compile_targets": ["chromium_builder_asan"], "gtest_tests": [{"args": ["--test-launcher-print-test-stdio=always"], "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "base_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "base_unittests", "test_id_prefix": "ninja://base:base_unittests/"}, {"args": ["--test-launcher-print-test-stdio=always"], "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "highway_tests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "highway_tests", "test_id_prefix": "ninja://third_party/highway:highway_tests/"}]}}