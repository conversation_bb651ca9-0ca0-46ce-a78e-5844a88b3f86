{"android-cronet-x86-dbg-pie-tests": {"gtest_tests": [{"args": ["--avd-config=../../tools/android/avd/proto/android_28_google_apis_x86.textpb", "--gs-results-bucket=chromium-result-details", "--recover-devices"], "merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "cronet_sample_test_apk"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "name": "cronet_sample_test_apk", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"dimensions": {"cores": "4", "cpu": "x86-64", "device_os": null, "device_type": null, "os": "Ubuntu-22.04", "pool": "chromium.tests.avd"}, "named_caches": [{"name": "android_28_google_apis_x86", "path": ".android_emulator/android_28_google_apis_x86"}], "optional_dimensions": {"60": {"caches": "android_28_google_apis_x86"}}, "service_account": "<EMAIL>"}, "test": "cronet_sample_test_apk", "test_id_prefix": "ninja://components/cronet/android:cronet_sample_test_apk/"}, {"args": ["--avd-config=../../tools/android/avd/proto/android_28_google_apis_x86.textpb", "--gs-results-bucket=chromium-result-details", "--recover-devices"], "merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "cronet_smoketests_apk"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "name": "cronet_smoketests_apk", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"dimensions": {"cores": "4", "cpu": "x86-64", "device_os": null, "device_type": null, "os": "Ubuntu-22.04", "pool": "chromium.tests.avd"}, "named_caches": [{"name": "android_28_google_apis_x86", "path": ".android_emulator/android_28_google_apis_x86"}], "optional_dimensions": {"60": {"caches": "android_28_google_apis_x86"}}, "service_account": "<EMAIL>"}, "test": "cronet_smoketests_apk", "test_id_prefix": "ninja://components/cronet/android:cronet_smoketests_apk/"}, {"args": ["--avd-config=../../tools/android/avd/proto/android_28_google_apis_x86.textpb", "--gs-results-bucket=chromium-result-details", "--recover-devices"], "merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "cronet_smoketests_missing_native_library_instrumentation_apk"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "name": "cronet_smoketests_missing_native_library_instrumentation_apk", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"dimensions": {"cores": "4", "cpu": "x86-64", "device_os": null, "device_type": null, "os": "Ubuntu-22.04", "pool": "chromium.tests.avd"}, "named_caches": [{"name": "android_28_google_apis_x86", "path": ".android_emulator/android_28_google_apis_x86"}], "optional_dimensions": {"60": {"caches": "android_28_google_apis_x86"}}, "service_account": "<EMAIL>"}, "test": "cronet_smoketests_missing_native_library_instrumentation_apk", "test_id_prefix": "ninja://components/cronet/android:cronet_smoketests_missing_native_library_instrumentation_apk/"}, {"args": ["--avd-config=../../tools/android/avd/proto/android_28_google_apis_x86.textpb", "--gs-results-bucket=chromium-result-details", "--recover-devices"], "merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "cronet_smoketests_platform_only_instrumentation_apk"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "name": "cronet_smoketests_platform_only_instrumentation_apk", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"dimensions": {"cores": "4", "cpu": "x86-64", "device_os": null, "device_type": null, "os": "Ubuntu-22.04", "pool": "chromium.tests.avd"}, "named_caches": [{"name": "android_28_google_apis_x86", "path": ".android_emulator/android_28_google_apis_x86"}], "optional_dimensions": {"60": {"caches": "android_28_google_apis_x86"}}, "service_account": "<EMAIL>"}, "test": "cronet_smoketests_platform_only_instrumentation_apk", "test_id_prefix": "ninja://components/cronet/android:cronet_smoketests_platform_only_instrumentation_apk/"}, {"args": ["--emulator-enable-network", "--avd-config=../../tools/android/avd/proto/android_28_google_apis_x86.textpb", "--gs-results-bucket=chromium-result-details", "--recover-devices"], "merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "cronet_test_instrumentation_apk"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "name": "cronet_test_instrumentation_apk", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"dimensions": {"cores": "4", "cpu": "x86-64", "device_os": null, "device_type": null, "os": "Ubuntu-22.04", "pool": "chromium.tests.avd"}, "idempotent": false, "named_caches": [{"name": "android_28_google_apis_x86", "path": ".android_emulator/android_28_google_apis_x86"}], "optional_dimensions": {"60": {"caches": "android_28_google_apis_x86"}}, "service_account": "<EMAIL>", "shards": 3}, "test": "cronet_test_instrumentation_apk", "test_id_prefix": "ninja://components/cronet/android:cronet_test_instrumentation_apk/"}, {"args": ["--avd-config=../../tools/android/avd/proto/android_28_google_apis_x86.textpb", "--gs-results-bucket=chromium-result-details", "--recover-devices"], "merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "cronet_tests_android"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "name": "cronet_tests_android", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"dimensions": {"cores": "4", "cpu": "x86-64", "device_os": null, "device_type": null, "os": "Ubuntu-22.04", "pool": "chromium.tests.avd"}, "named_caches": [{"name": "android_28_google_apis_x86", "path": ".android_emulator/android_28_google_apis_x86"}], "optional_dimensions": {"60": {"caches": "android_28_google_apis_x86"}}, "service_account": "<EMAIL>"}, "test": "cronet_tests_android", "test_id_prefix": "ninja://components/cronet/android:cronet_tests_android/"}, {"args": ["--avd-config=../../tools/android/avd/proto/android_28_google_apis_x86.textpb", "--gs-results-bucket=chromium-result-details", "--recover-devices"], "merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "cronet_unittests_android"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "name": "cronet_unittests_android", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"dimensions": {"cores": "4", "cpu": "x86-64", "device_os": null, "device_type": null, "os": "Ubuntu-22.04", "pool": "chromium.tests.avd"}, "named_caches": [{"name": "android_28_google_apis_x86", "path": ".android_emulator/android_28_google_apis_x86"}], "optional_dimensions": {"60": {"caches": "android_28_google_apis_x86"}}, "service_account": "<EMAIL>"}, "test": "cronet_unittests_android", "test_id_prefix": "ninja://components/cronet/android:cronet_unittests_android/"}, {"args": ["--avd-config=../../tools/android/avd/proto/android_28_google_apis_x86.textpb", "--test-launcher-filter-file=../../testing/buildbot/filters/android.emulator.net_unittests.filter", "--gs-results-bucket=chromium-result-details", "--recover-devices"], "merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "net_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "name": "net_unittests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"dimensions": {"cores": "4", "cpu": "x86-64", "device_os": null, "device_type": null, "os": "Ubuntu-22.04", "pool": "chromium.tests.avd"}, "named_caches": [{"name": "android_28_google_apis_x86", "path": ".android_emulator/android_28_google_apis_x86"}], "optional_dimensions": {"60": {"caches": "android_28_google_apis_x86"}}, "service_account": "<EMAIL>", "shards": 4}, "test": "net_unittests", "test_id_prefix": "ninja://net:net_unittests/"}]}}