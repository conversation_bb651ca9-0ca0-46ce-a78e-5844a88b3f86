{"linux-blink-web-tests-force-accessibility-rel": {"additional_compile_targets": ["blink_tests"], "gtest_tests": [{"args": ["--force-renderer-accessibility", "--test-launcher-filter-file=../../testing/buildbot/filters/accessibility-linux.browser_tests.filter"], "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "browser_tests", "swarming": {"dimensions": {"os": "Ubuntu-22.04"}, "service_account": "<EMAIL>", "shards": 20}, "test": "browser_tests", "test_id_prefix": "ninja://chrome/test:browser_tests/"}, {"args": ["--force-renderer-accessibility", "--test-launcher-filter-file=../../testing/buildbot/filters/accessibility-linux.content_browsertests.filter"], "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "content_browsertests", "swarming": {"dimensions": {"os": "Ubuntu-22.04"}, "service_account": "<EMAIL>", "shards": 8}, "test": "content_browsertests", "test_id_prefix": "ninja://content/test:content_browsertests/"}, {"args": ["--force-renderer-accessibility", "--test-launcher-filter-file=../../testing/buildbot/filters/accessibility-linux.interactive_ui_tests.filter"], "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "interactive_ui_tests", "swarming": {"dimensions": {"os": "Ubuntu-22.04"}, "service_account": "<EMAIL>", "shards": 6}, "test": "interactive_ui_tests", "test_id_prefix": "ninja://chrome/test:interactive_ui_tests/"}], "isolated_scripts": [{"args": ["--num-retries=3", "--write-run-histories-to=${ISOLATED_OUTDIR}/run_histories.json", "--flag-specific=force-renderer-accessibility"], "merge": {"args": ["--verbose"], "script": "//third_party/blink/tools/merge_web_test_results.py"}, "name": "blink_web_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "results_handler": "layout tests", "swarming": {"dimensions": {"os": "Ubuntu-22.04"}, "service_account": "<EMAIL>", "shards": 20}, "test": "blink_web_tests", "test_id_prefix": "ninja://:blink_web_tests/"}, {"args": ["--num-retries=3", "--write-run-histories-to=${ISOLATED_OUTDIR}/run_histories.json", "--flag-specific=force-renderer-accessibility"], "merge": {"args": ["--verbose"], "script": "//third_party/blink/tools/merge_web_test_results.py"}, "name": "blink_wpt_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "results_handler": "layout tests", "swarming": {"dimensions": {"os": "Ubuntu-22.04"}, "service_account": "<EMAIL>", "shards": 2}, "test": "blink_wpt_tests", "test_id_prefix": "ninja://:blink_wpt_tests/"}, {"args": ["--test-launcher-filter-file=../../third_party/blink/web_tests/TestLists/chrome.filter", "--flag-specific=force-renderer-accessibility"], "merge": {"args": ["--verbose"], "script": "//third_party/blink/tools/merge_web_test_results.py"}, "name": "chrome_wpt_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "results_handler": "layout tests", "swarming": {"dimensions": {"os": "Ubuntu-22.04"}, "service_account": "<EMAIL>"}, "test": "chrome_wpt_tests", "test_id_prefix": "ninja://:chrome_wpt_tests/"}, {"args": ["--test-type", "testharness", "reftest", "crashtest", "print-reftest", "--inverted-test-launcher-filter-file=../../third_party/blink/web_tests/TestLists/chrome.filter", "--inverted-test-launcher-filter-file=../../third_party/blink/web_tests/TestLists/content_shell.filter", "--flag-specific=force-renderer-accessibility"], "merge": {"args": ["--verbose"], "script": "//third_party/blink/tools/merge_web_test_results.py"}, "name": "headless_shell_wpt_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "results_handler": "layout tests", "swarming": {"dimensions": {"os": "Ubuntu-22.04"}, "service_account": "<EMAIL>", "shards": 18}, "test": "headless_shell_wpt", "test_id_prefix": "ninja://:headless_shell_wpt/"}]}}