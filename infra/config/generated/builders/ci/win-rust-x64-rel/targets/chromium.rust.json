{"win-rust-x64-rel": {"additional_compile_targets": ["mojo_rust", "mojo_rust_integration_unittests", "mojo_rust_unittests", "rust_build_tests"], "gtest_tests": [{"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "base_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10"}, "service_account": "<EMAIL>"}, "test": "base_unittests", "test_id_prefix": "ninja://base:base_unittests/"}, {"args": ["--git-revision=${got_revision}", "--test-launcher-bot-mode", "--gtest_filter=*PNG*"], "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "blink_platform_unittests", "precommit_args": ["--gerrit-issue=${patch_issue}", "--gerrit-patchset=${patch_set}", "--buildbucket-id=${buildbucket_build_id}"], "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10"}, "service_account": "<EMAIL>"}, "test": "blink_platform_unittests", "test_id_prefix": "ninja://third_party/blink/renderer/platform:blink_platform_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "gfx_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10"}, "service_account": "<EMAIL>"}, "test": "gfx_unittests", "test_id_prefix": "ninja://ui/gfx:gfx_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "mojo_rust_integration_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10"}, "service_account": "<EMAIL>"}, "test": "mojo_rust_integration_unittests", "test_id_prefix": "ninja://mojo/public/rust:mojo_rust_integration_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "mojo_rust_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10"}, "service_account": "<EMAIL>"}, "test": "mojo_rust_unittests", "test_id_prefix": "ninja://mojo/public/rust:mojo_rust_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "rust_gtest_interop_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10"}, "service_account": "<EMAIL>"}, "test": "rust_gtest_interop_unittests", "test_id_prefix": "ninja://testing/rust_gtest_interop:rust_gtest_interop_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "test_cpp_including_rust_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10"}, "service_account": "<EMAIL>"}, "test": "test_cpp_including_rust_unittests", "test_id_prefix": "ninja://build/rust/tests/test_cpp_including_rust:test_cpp_including_rust_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "test_serde_json_lenient", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10"}, "service_account": "<EMAIL>"}, "test": "test_serde_json_lenient", "test_id_prefix": "ninja://build/rust/tests/test_serde_json_lenient:test_serde_json_lenient/"}], "isolated_scripts": [{"merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "build_rust_tests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10"}, "service_account": "<EMAIL>"}, "test": "build_rust_tests", "test_id_prefix": "ninja://build/rust/tests:build_rust_tests/"}]}}