{"$build/chromium_tests_builder_config": {"builder_config": {"builder_db": {"entries": [{"builder_id": {"bucket": "ci", "builder": "win-updater-builder-dbg", "project": "chromium"}, "builder_spec": {"builder_group": "chromium.updater", "execution_mode": "COMPILE_AND_TEST", "legacy_chromium_config": {"apply_configs": ["mb"], "build_config": "Debug", "config": "chromium", "target_bits": 64, "target_platform": "win"}, "legacy_gclient_config": {"config": "chromium"}}}, {"builder_id": {"bucket": "ci", "builder": "win10-updater-tester-dbg", "project": "chromium"}, "builder_spec": {"builder_group": "chromium.updater", "execution_mode": "TEST", "legacy_chromium_config": {"apply_configs": ["mb"], "build_config": "Debug", "config": "chromium", "target_bits": 64, "target_platform": "win"}, "legacy_gclient_config": {"config": "chromium"}, "parent": {"bucket": "ci", "builder": "win-updater-builder-dbg", "project": "chromium"}}}]}, "builder_ids": [{"bucket": "ci", "builder": "win10-updater-tester-dbg", "project": "chromium"}], "mirroring_builder_group_and_names": [{"builder": "win-updater-try-builder-dbg", "group": "tryserver.chromium.updater"}], "targets_spec_directory": "src/infra/config/generated/builders/ci/win10-updater-tester-dbg/targets"}}, "$recipe_engine/resultdb/test_presentation": {"column_keys": [], "grouping_keys": ["status", "v.test_suite"]}, "builder_group": "chromium.updater", "recipe": "chromium"}