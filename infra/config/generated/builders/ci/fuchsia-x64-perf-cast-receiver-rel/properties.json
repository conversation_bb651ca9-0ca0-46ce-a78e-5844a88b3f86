{"$build/chromium_tests_builder_config": {"builder_config": {"additional_exclusions": ["infra/config/generated/builders/ci/fuchsia-x64-perf-cast-receiver-rel/gn-args.json"], "builder_db": {"entries": [{"builder_id": {"bucket": "ci", "builder": "fuchsia-x64-perf-cast-receiver-rel", "project": "chromium"}, "builder_spec": {"build_gs_bucket": "chromium-linux-archive", "builder_group": "chromium.fuchsia.fyi", "execution_mode": "COMPILE_AND_TEST", "legacy_chromium_config": {"apply_configs": ["mb"], "build_config": "Release", "config": "chromium", "target_bits": 64, "target_platform": "fuchsia"}, "legacy_gclient_config": {"apply_configs": ["checkout_pgo_profiles", "fuchsia_x64"], "config": "chromium"}}}]}, "builder_ids": [{"bucket": "ci", "builder": "fuchsia-x64-perf-cast-receiver-rel", "project": "chromium"}], "mirroring_builder_group_and_names": [{"builder": "fuchsia-x64-perf-cast-receiver-rel", "group": "tryserver.chromium.fuchsia"}], "targets_spec_directory": "src/infra/config/generated/builders/ci/fuchsia-x64-perf-cast-receiver-rel/targets"}}, "$build/siso": {"configs": ["builder"], "enable_cloud_monitoring": true, "enable_cloud_profiler": true, "enable_cloud_trace": true, "experiments": [], "metrics_project": "chromium-reclient-metrics", "project": "rbe-chromium-trusted", "remote_jobs": 250}, "$recipe_engine/resultdb/test_presentation": {"column_keys": [], "grouping_keys": ["status", "v.test_suite"]}, "builder_group": "chromium.fuchsia.fyi", "gardener_rotations": ["fuchsia"], "recipe": "chromium", "sheriff_rotations": ["fuchsia"]}