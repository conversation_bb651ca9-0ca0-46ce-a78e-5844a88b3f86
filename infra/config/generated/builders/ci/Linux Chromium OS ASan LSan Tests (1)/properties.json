{"$build/chromium_tests_builder_config": {"builder_config": {"builder_db": {"entries": [{"builder_id": {"bucket": "ci", "builder": "Linux Chromium OS ASan LSan Builder", "project": "chromium"}, "builder_spec": {"build_gs_bucket": "chromium-memory-archive", "builder_group": "chromium.memory", "execution_mode": "COMPILE_AND_TEST", "legacy_chromium_config": {"apply_configs": ["lsan", "mb"], "build_config": "Release", "config": "chromium_asan", "target_bits": 64, "target_platform": "chromeos"}, "legacy_gclient_config": {"apply_configs": ["chromeos"], "config": "chromium"}}}, {"builder_id": {"bucket": "ci", "builder": "Linux Chromium OS ASan LSan Tests (1)", "project": "chromium"}, "builder_spec": {"build_gs_bucket": "chromium-memory-archive", "builder_group": "chromium.memory", "execution_mode": "TEST", "legacy_chromium_config": {"apply_configs": ["lsan", "mb"], "build_config": "Release", "config": "chromium_asan", "target_bits": 64, "target_platform": "chromeos"}, "legacy_gclient_config": {"apply_configs": ["chromeos"], "config": "chromium"}, "parent": {"bucket": "ci", "builder": "Linux Chromium OS ASan LSan Builder", "project": "chromium"}}}]}, "builder_ids": [{"bucket": "ci", "builder": "Linux Chromium OS ASan LSan Tests (1)", "project": "chromium"}], "mirroring_builder_group_and_names": [{"builder": "linux_chromium_chromeos_asan_rel_ng", "group": "tryserver.chromium.linux"}], "retry_failed_shards": true, "targets_spec_directory": "src/infra/config/generated/builders/ci/Linux Chromium OS ASan LSan Tests (1)/targets"}}, "$recipe_engine/resultdb/test_presentation": {"column_keys": [], "grouping_keys": ["status", "v.test_suite"]}, "builder_group": "chromium.memory", "gardener_rotations": ["chromium"], "recipe": "chromium", "sheriff_rotations": ["chromium"]}