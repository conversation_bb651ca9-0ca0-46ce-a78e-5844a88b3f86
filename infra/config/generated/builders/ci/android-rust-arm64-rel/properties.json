{"$build/chromium_tests_builder_config": {"builder_config": {"additional_exclusions": ["infra/config/generated/builders/ci/android-rust-arm64-rel/gn-args.json"], "builder_db": {"entries": [{"builder_id": {"bucket": "ci", "builder": "android-rust-arm64-rel", "project": "chromium"}, "builder_spec": {"builder_group": "chromium.rust", "execution_mode": "COMPILE_AND_TEST", "legacy_android_config": {"config": "base_config"}, "legacy_chromium_config": {"apply_configs": ["android"], "build_config": "Release", "config": "base_config", "target_arch": "arm", "target_bits": 64, "target_platform": "android"}, "legacy_gclient_config": {"apply_configs": ["android"], "config": "chromium"}}}]}, "builder_ids": [{"bucket": "ci", "builder": "android-rust-arm64-rel", "project": "chromium"}], "mirroring_builder_group_and_names": [{"builder": "android-rust-arm64-rel", "group": "tryserver.chromium.rust"}], "targets_spec_directory": "src/infra/config/generated/builders/ci/android-rust-arm64-rel/targets"}}, "$build/siso": {"configs": ["builder"], "enable_cloud_monitoring": true, "enable_cloud_profiler": true, "enable_cloud_trace": true, "experiments": [], "metrics_project": "chromium-reclient-metrics", "project": "rbe-chromium-trusted", "remote_jobs": 250}, "$recipe_engine/resultdb/test_presentation": {"column_keys": [], "grouping_keys": ["status", "v.test_suite"]}, "builder_group": "chromium.rust", "recipe": "chromium"}