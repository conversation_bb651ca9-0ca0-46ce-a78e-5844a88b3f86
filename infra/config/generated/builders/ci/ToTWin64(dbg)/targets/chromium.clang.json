{"ToTWin64(dbg)": {"additional_compile_targets": ["all"], "gtest_tests": [{"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "base_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "base_unittests", "test_id_prefix": "ninja://base:base_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "highway_tests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "highway_tests", "test_id_prefix": "ninja://third_party/highway:highway_tests/"}], "isolated_scripts": [{"merge": {"args": ["--upload-skia-json"], "script": "//tools/perf/process_perf_results.py"}, "name": "chrome_sizes", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "chrome_sizes", "test_id_prefix": "ninja://chrome/test:chrome_sizes/"}, {"merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "mini_installer_tests", "swarming": {"dimensions": {"cpu": "x86-64", "integrity": "high", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "mini_installer_tests", "test_id_prefix": "ninja://chrome/test/mini_installer:mini_installer_tests/"}, {"experiment_percentage": 0, "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "polymer_tools_python_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Windows-10-19045"}, "service_account": "<EMAIL>"}, "test": "polymer_tools_python_unittests", "test_id_prefix": "ninja://tools/polymer:polymer_tools_python_unittests/"}]}}