{"$build/chromium_tests_builder_config": {"builder_config": {"additional_exclusions": ["infra/config/generated/builders/ci/Network Service Linux/gn-args.json"], "builder_db": {"entries": [{"builder_id": {"bucket": "ci", "builder": "Network Service Linux", "project": "chromium"}, "builder_spec": {"build_gs_bucket": "chromium-linux-archive", "builder_group": "chromium.linux", "execution_mode": "COMPILE_AND_TEST", "legacy_chromium_config": {"apply_configs": ["mb"], "build_config": "Release", "config": "chromium", "target_bits": 64, "target_platform": "linux"}, "legacy_gclient_config": {"config": "chromium"}}}]}, "builder_ids": [{"bucket": "ci", "builder": "Network Service Linux", "project": "chromium"}], "mirroring_builder_group_and_names": [{"builder": "network_service_linux", "group": "tryserver.chromium.linux"}], "retry_failed_shards": true, "targets_spec_directory": "src/infra/config/generated/builders/ci/Network Service Linux/targets"}}, "$build/siso": {"configs": ["builder"], "enable_cloud_monitoring": true, "enable_cloud_profiler": true, "enable_cloud_trace": true, "experiments": [], "metrics_project": "chromium-reclient-metrics", "project": "rbe-chromium-trusted", "remote_jobs": 250}, "$recipe_engine/resultdb/test_presentation": {"column_keys": [], "grouping_keys": ["status", "v.test_suite"]}, "builder_group": "chromium.linux", "gardener_rotations": ["chromium"], "recipe": "chromium", "sheriff_rotations": ["chromium"]}