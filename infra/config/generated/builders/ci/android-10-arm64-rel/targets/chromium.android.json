{"android-10-arm64-rel": {"additional_compile_targets": ["check_chrome_static_initializers"], "gtest_tests": [{"args": ["--gs-results-bucket=chromium-result-details", "--recover-devices"], "merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "chrome_public_test_vr_apk"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "name": "chrome_public_test_vr_apk", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"dimensions": {"device_os": "QQ1A.191205.008", "device_os_flavor": "google", "device_os_type": "userdebug", "device_type": "walleye", "os": "Android"}, "service_account": "<EMAIL>", "shards": 2}, "test": "chrome_public_test_vr_apk", "test_id_prefix": "ninja://chrome/android:chrome_public_test_vr_apk/"}, {"args": ["--gs-results-bucket=chromium-result-details", "--recover-devices"], "merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "monochrome_public_test_ar_apk"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "name": "monochrome_public_test_ar_apk", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"dimensions": {"device_os": "QQ1A.191205.008", "device_os_flavor": "google", "device_os_type": "userdebug", "device_type": "walleye", "os": "Android"}, "service_account": "<EMAIL>"}, "test": "monochrome_public_test_ar_apk", "test_id_prefix": "ninja://chrome/android:monochrome_public_test_ar_apk/"}, {"args": ["--gs-results-bucket=chromium-result-details", "--recover-devices"], "merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "trichrome_chrome_bundle_smoke_test"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "name": "trichrome_chrome_bundle_smoke_test", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"dimensions": {"device_os": "QQ1A.191205.008", "device_os_flavor": "google", "device_os_type": "userdebug", "device_type": "walleye", "os": "Android"}, "service_account": "<EMAIL>"}, "test": "trichrome_chrome_bundle_smoke_test", "test_id_prefix": "ninja://chrome/android:trichrome_chrome_bundle_smoke_test/"}, {"args": ["--gs-results-bucket=chromium-result-details", "--recover-devices"], "merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "vr_android_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "name": "vr_android_unittests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"dimensions": {"device_os": "QQ1A.191205.008", "device_os_flavor": "google", "device_os_type": "userdebug", "device_type": "walleye", "os": "Android"}, "service_account": "<EMAIL>"}, "test": "vr_android_unittests", "test_id_prefix": "ninja://chrome/browser/android/vr:vr_android_unittests/"}]}}