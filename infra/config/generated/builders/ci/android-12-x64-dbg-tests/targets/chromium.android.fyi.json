{"android-12-x64-dbg-tests": {"gtest_tests": [{"args": ["--avd-config=../../tools/android/avd/proto/android_31_google_apis_x64.textpb", "--gs-results-bucket=chromium-result-details", "--recover-devices"], "description": "Run with android_31_google_apis_x64", "merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "trichrome_chrome_bundle_smoke_test"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "name": "trichrome_chrome_bundle_smoke_test", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"dimensions": {"cores": "8", "cpu": "x86-64", "device_os": null, "device_type": null, "os": "Ubuntu-22.04", "pool": "chromium.tests.avd"}, "named_caches": [{"name": "android_31_google_apis_x64", "path": ".android_emulator/android_31_google_apis_x64"}], "optional_dimensions": {"60": {"caches": "android_31_google_apis_x64"}}, "service_account": "<EMAIL>"}, "test": "trichrome_chrome_bundle_smoke_test", "test_id_prefix": "ninja://chrome/android:trichrome_chrome_bundle_smoke_test/"}]}}