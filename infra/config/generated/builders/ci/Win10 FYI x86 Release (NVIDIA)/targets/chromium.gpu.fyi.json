{"Win10 FYI x86 Release (NVIDIA)": {"gtest_tests": [{"merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "angle_unittests", "swarming": {"containment_type": "AUTO", "dimensions": {"display_attached": "1", "gpu": "10de:2184-31.0.15.4601", "os": "Windows-10-19045", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "angle_unittests", "test_id_prefix": "ninja://third_party/angle/src/tests:angle_unittests/", "use_isolated_scripts_api": true}, {"args": ["--use-cmd-decoder=passthrough", "--use-gl=angle", "--use-gpu-in-tests"], "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "gl_tests_passthrough", "swarming": {"containment_type": "AUTO", "dimensions": {"display_attached": "1", "gpu": "10de:2184-31.0.15.4601", "os": "Windows-10-19045", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "io_timeout": 1800, "service_account": "<EMAIL>", "shards": 2}, "test": "gl_tests", "test_id_prefix": "ninja://gpu:gl_tests/"}, {"args": ["--use-gpu-in-tests", "--git-revision=${got_revision}"], "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "gl_unittests", "precommit_args": ["--gerrit-issue=${patch_issue}", "--gerrit-patchset=${patch_set}", "--buildbucket-id=${buildbucket_build_id}"], "swarming": {"containment_type": "AUTO", "dimensions": {"display_attached": "1", "gpu": "10de:2184-31.0.15.4601", "os": "Windows-10-19045", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "gl_unittests", "test_id_prefix": "ninja://ui/gl:gl_unittests/"}, {"merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "gpu_unittests", "swarming": {"containment_type": "AUTO", "dimensions": {"display_attached": "1", "gpu": "10de:2184-31.0.15.4601", "os": "Windows-10-19045", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "gpu_unittests", "test_id_prefix": "ninja://gpu:gpu_unittests/"}, {"args": ["--gtest_filter=WebNN*", "--use-gpu-in-tests"], "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "services_webnn_unittests", "swarming": {"containment_type": "AUTO", "dimensions": {"display_attached": "1", "gpu": "10de:2184-31.0.15.4601", "os": "Windows-10-19045", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "services_unittests", "test_id_prefix": "ninja://services:services_unittests/"}, {"args": ["--enable-gpu", "--test-launcher-bot-mode", "--test-launcher-jobs=1", "--gtest_filter=TabCaptureApiPixelTest.EndToEnd*"], "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "tab_capture_end2end_tests", "swarming": {"containment_type": "AUTO", "dimensions": {"display_attached": "1", "gpu": "10de:2184-31.0.15.4601", "os": "Windows-10-19045", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "browser_tests", "test_id_prefix": "ninja://chrome/test:browser_tests/"}, {"args": ["--ignore-runtime-requirements=*"], "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "xr_browser_tests", "swarming": {"containment_type": "AUTO", "dimensions": {"display_attached": "1", "gpu": "10de:2184-31.0.15.4601", "os": "Windows-10-19045", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "xr_browser_tests", "test_id_prefix": "ninja://chrome/test:xr_browser_tests/"}], "isolated_scripts": [{"args": ["context_lost", "--show-stdout", "--browser=release", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --use-cmd-decoder=passthrough --use-gl=angle --enable-features=SkiaGraphite", "--enforce-browser-version", "--jobs=4"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "context_lost_passthrough_graphite_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"display_attached": "1", "gpu": "10de:2184-31.0.15.4601", "os": "Windows-10-19045", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["context_lost", "--show-stdout", "--browser=release", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --use-cmd-decoder=passthrough --use-gl=angle", "--enforce-browser-version", "--jobs=4"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "context_lost_passthrough_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"display_attached": "1", "gpu": "10de:2184-31.0.15.4601", "os": "Windows-10-19045", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["expected_color", "--show-stdout", "--browser=release", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --use-cmd-decoder=passthrough --use-gl=angle --enable-features=SkiaGraphite", "--enforce-browser-version", "--git-revision=${got_revision}", "--dont-restore-color-profile-after-test", "--test-machine-name", "${buildername}", "--jobs=4"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "expected_color_pixel_passthrough_graphite_test", "precommit_args": ["--gerrit-issue=${patch_issue}", "--gerrit-patchset=${patch_set}", "--buildbucket-id=${buildbucket_build_id}"], "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"display_attached": "1", "gpu": "10de:2184-31.0.15.4601", "os": "Windows-10-19045", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["expected_color", "--show-stdout", "--browser=release", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --use-cmd-decoder=passthrough --use-gl=angle", "--enforce-browser-version", "--git-revision=${got_revision}", "--dont-restore-color-profile-after-test", "--test-machine-name", "${buildername}", "--jobs=4"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "expected_color_pixel_passthrough_test", "precommit_args": ["--gerrit-issue=${patch_issue}", "--gerrit-patchset=${patch_set}", "--buildbucket-id=${buildbucket_build_id}"], "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"display_attached": "1", "gpu": "10de:2184-31.0.15.4601", "os": "Windows-10-19045", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["gpu_process", "--show-stdout", "--browser=release", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc", "--enforce-browser-version", "--jobs=4"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "gpu_process_launch_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"display_attached": "1", "gpu": "10de:2184-31.0.15.4601", "os": "Windows-10-19045", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["hardware_accelerated_feature", "--show-stdout", "--browser=release", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc", "--enforce-browser-version", "--jobs=4"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "hardware_accelerated_feature_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"display_attached": "1", "gpu": "10de:2184-31.0.15.4601", "os": "Windows-10-19045", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["info_collection", "--show-stdout", "--browser=release", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --force_high_performance_gpu", "--enforce-browser-version", "--expected-vendor-id", "10de", "--expected-device-id", "2184", "--jobs=4"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "info_collection_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"display_attached": "1", "gpu": "10de:2184-31.0.15.4601", "os": "Windows-10-19045", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["--gtest-benchmark-name=passthrough_command_buffer_perftests", "-v", "--use-cmd-decoder=passthrough", "--use-angle=gl-null", "--fast-run"], "merge": {"args": ["--smoke-test-mode"], "script": "//tools/perf/process_perf_results.py"}, "name": "passthrough_command_buffer_perftests", "swarming": {"containment_type": "AUTO", "dimensions": {"display_attached": "1", "gpu": "10de:2184-31.0.15.4601", "os": "Windows-10-19045", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "command_buffer_perftests", "test_id_prefix": "ninja://gpu:command_buffer_perftests/"}, {"args": ["pixel", "--show-stdout", "--browser=release", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --use-cmd-decoder=passthrough --use-gl=angle --enable-features=SkiaGraphite", "--enforce-browser-version", "--git-revision=${got_revision}", "--dont-restore-color-profile-after-test", "--test-machine-name", "${buildername}", "--jobs=1"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "pixel_skia_gold_passthrough_graphite_test", "precommit_args": ["--gerrit-issue=${patch_issue}", "--gerrit-patchset=${patch_set}", "--buildbucket-id=${buildbucket_build_id}"], "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"display_attached": "1", "gpu": "10de:2184-31.0.15.4601", "os": "Windows-10-19045", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["pixel", "--show-stdout", "--browser=release", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --use-cmd-decoder=passthrough --use-gl=angle", "--enforce-browser-version", "--git-revision=${got_revision}", "--dont-restore-color-profile-after-test", "--test-machine-name", "${buildername}", "--jobs=1"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "pixel_skia_gold_passthrough_test", "precommit_args": ["--gerrit-issue=${patch_issue}", "--gerrit-patchset=${patch_set}", "--buildbucket-id=${buildbucket_build_id}"], "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"display_attached": "1", "gpu": "10de:2184-31.0.15.4601", "os": "Windows-10-19045", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["screenshot_sync", "--show-stdout", "--browser=release", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --use-cmd-decoder=passthrough --use-gl=angle --enable-features=SkiaGraphite", "--enforce-browser-version", "--dont-restore-color-profile-after-test", "--jobs=4"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "screenshot_sync_passthrough_graphite_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"display_attached": "1", "gpu": "10de:2184-31.0.15.4601", "os": "Windows-10-19045", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["screenshot_sync", "--show-stdout", "--browser=release", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --use-cmd-decoder=passthrough --use-gl=angle", "--enforce-browser-version", "--dont-restore-color-profile-after-test", "--jobs=4"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "screenshot_sync_passthrough_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"display_attached": "1", "gpu": "10de:2184-31.0.15.4601", "os": "Windows-10-19045", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["trace_test", "--show-stdout", "--browser=release", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc", "--enforce-browser-version", "--jobs=1"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "trace_test", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"display_attached": "1", "gpu": "10de:2184-31.0.15.4601", "os": "Windows-10-19045", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["webcodecs", "--show-stdout", "--browser=release", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc", "--enforce-browser-version", "--jobs=4"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "webcodecs_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"display_attached": "1", "gpu": "10de:2184-31.0.15.4601", "os": "Windows-10-19045", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["webgl2_conformance", "--show-stdout", "--browser=release", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --use-cmd-decoder=passthrough --use-gl=angle --use-angle=d3d11 --force_high_performance_gpu", "--enforce-browser-version", "--webgl-conformance-version=2.0.1", "--read-abbreviated-json-results-from=../../content/test/data/gpu/webgl2_conformance_win_runtimes.json", "--jobs=4"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "webgl2_conformance_d3d11_passthrough_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"display_attached": "1", "gpu": "10de:2184-31.0.15.4601", "os": "Windows-10-19045", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>", "shards": 20}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["webgl1_conformance", "--show-stdout", "--browser=release", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --use-cmd-decoder=passthrough --use-gl=angle --use-angle=d3d11 --force_high_performance_gpu", "--enforce-browser-version", "--read-abbreviated-json-results-from=../../content/test/data/gpu/webgl1_conformance_win_runtimes.json", "--jobs=4"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "webgl_conformance_d3d11_passthrough_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"display_attached": "1", "gpu": "10de:2184-31.0.15.4601", "os": "Windows-10-19045", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>", "shards": 2}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["webgl1_conformance", "--show-stdout", "--browser=release", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --use-cmd-decoder=passthrough --use-gl=angle --use-angle=d3d9 --force_high_performance_gpu", "--enforce-browser-version", "--read-abbreviated-json-results-from=../../content/test/data/gpu/webgl1_conformance_win_runtimes.json", "--jobs=4"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "webgl_conformance_d3d9_passthrough_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"display_attached": "1", "gpu": "10de:2184-31.0.15.4601", "os": "Windows-10-19045", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>", "shards": 2}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["webrtc", "--show-stdout", "--browser=release", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc", "--enforce-browser-version", "--jobs=4"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "webrtc_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"containment_type": "AUTO", "dimensions": {"display_attached": "1", "gpu": "10de:2184-31.0.15.4601", "os": "Windows-10-19045", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}]}}