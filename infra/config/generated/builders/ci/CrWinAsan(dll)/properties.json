{"$build/chromium": {"fail_build_on_clang_warnings": true}, "$build/chromium_tests_builder_config": {"builder_config": {"additional_exclusions": ["infra/config/generated/builders/ci/Cr<PERSON>in<PERSON><PERSON>(dll)/gn-args.json"], "builder_db": {"entries": [{"builder_id": {"bucket": "ci", "builder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(dll)", "project": "chromium"}, "builder_spec": {"build_gs_bucket": "chromium-clang-archive", "builder_group": "chromium.clang", "execution_mode": "COMPILE_AND_TEST", "legacy_chromium_config": {"apply_configs": ["mb"], "build_config": "Release", "config": "chromium_win_clang_asan_tot", "target_bits": 32, "target_platform": "win"}, "legacy_gclient_config": {"apply_configs": ["clang_tot"], "config": "chromium"}}}]}, "builder_ids": [{"bucket": "ci", "builder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(dll)", "project": "chromium"}], "targets_spec_directory": "src/infra/config/generated/builders/ci/Cr<PERSON>in<PERSON>an(dll)/targets"}}, "$build/siso": {"configs": ["builder"], "enable_cloud_monitoring": true, "enable_cloud_profiler": true, "enable_cloud_trace": true, "experiments": [], "metrics_project": "chromium-reclient-metrics", "project": "rbe-chromium-trusted"}, "$recipe_engine/resultdb/test_presentation": {"column_keys": [], "grouping_keys": ["status", "v.test_suite"]}, "builder_group": "chromium.clang", "gardener_rotations": ["chromium.clang"], "perf_dashboard_machine_group": "ChromiumClang", "recipe": "chromium", "sheriff_rotations": ["chromium.clang"]}