{"Dawn Mac arm64 DEPS Release (Apple M2)": {"gtest_tests": [{"args": ["--enable-implicit-device-sync", "--use-gpu-in-tests", "--exclusive-device-type-preference=discrete,integrated", "--test-launcher-retry-limit=0", "--test-launcher-batch-limit=512"], "ci_only": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "dawn_end2end_implicit_device_sync_tests", "swarming": {"dimensions": {"cpu": "arm64", "display_attached": "1", "gpu": "apple:m2", "hidpi": "1", "mac_model": "Mac14,7", "os": "Mac-14.4.1", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "io_timeout": 1800, "service_account": "<EMAIL>", "shards": 2}, "test": "dawn_end2end_tests", "test_id_prefix": "ninja://third_party/dawn/src/dawn/tests:dawn_end2end_tests/"}, {"args": ["--enable-toggles=skip_validation", "--use-gpu-in-tests", "--exclusive-device-type-preference=discrete,integrated", "--test-launcher-retry-limit=0", "--test-launcher-batch-limit=512"], "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "dawn_end2end_skip_validation_tests", "swarming": {"dimensions": {"cpu": "arm64", "display_attached": "1", "gpu": "apple:m2", "hidpi": "1", "mac_model": "Mac14,7", "os": "Mac-14.4.1", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "io_timeout": 1800, "service_account": "<EMAIL>", "shards": 2}, "test": "dawn_end2end_tests", "test_id_prefix": "ninja://third_party/dawn/src/dawn/tests:dawn_end2end_tests/"}, {"args": ["--use-gpu-in-tests", "--exclusive-device-type-preference=discrete,integrated", "--test-launcher-retry-limit=0", "--test-launcher-batch-limit=512"], "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "dawn_end2end_tests", "swarming": {"dimensions": {"cpu": "arm64", "display_attached": "1", "gpu": "apple:m2", "hidpi": "1", "mac_model": "Mac14,7", "os": "Mac-14.4.1", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "io_timeout": 1800, "service_account": "<EMAIL>", "shards": 2}, "test": "dawn_end2end_tests", "test_id_prefix": "ninja://third_party/dawn/src/dawn/tests:dawn_end2end_tests/"}, {"args": ["--enable-backend-validation", "--use-gpu-in-tests", "--exclusive-device-type-preference=discrete,integrated", "--test-launcher-retry-limit=0", "--test-launcher-batch-limit=512"], "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "dawn_end2end_validation_layers_tests", "swarming": {"dimensions": {"cpu": "arm64", "display_attached": "1", "gpu": "apple:m2", "hidpi": "1", "mac_model": "Mac14,7", "os": "Mac-14.4.1", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "io_timeout": 1800, "service_account": "<EMAIL>", "shards": 2}, "test": "dawn_end2end_tests", "test_id_prefix": "ninja://third_party/dawn/src/dawn/tests:dawn_end2end_tests/"}, {"args": ["--use-wire", "--use-gpu-in-tests", "--exclusive-device-type-preference=discrete,integrated", "--test-launcher-retry-limit=0", "--test-launcher-batch-limit=512"], "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "dawn_end2end_wire_tests", "swarming": {"dimensions": {"cpu": "arm64", "display_attached": "1", "gpu": "apple:m2", "hidpi": "1", "mac_model": "Mac14,7", "os": "Mac-14.4.1", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "io_timeout": 1800, "service_account": "<EMAIL>", "shards": 2}, "test": "dawn_end2end_tests", "test_id_prefix": "ninja://third_party/dawn/src/dawn/tests:dawn_end2end_tests/"}, {"args": ["--use-cmd-decoder=passthrough", "--use-gl=angle", "--use-gpu-in-tests"], "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "gl_tests_passthrough", "swarming": {"dimensions": {"cpu": "arm64", "display_attached": "1", "gpu": "apple:m2", "hidpi": "1", "mac_model": "Mac14,7", "os": "Mac-14.4.1", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "io_timeout": 1800, "service_account": "<EMAIL>", "shards": 2}, "test": "gl_tests", "test_id_prefix": "ninja://gpu:gl_tests/"}, {"args": ["--use-gpu-in-tests", "--git-revision=${got_revision}"], "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "gl_unittests", "precommit_args": ["--gerrit-issue=${patch_issue}", "--gerrit-patchset=${patch_set}", "--buildbucket-id=${buildbucket_build_id}"], "swarming": {"dimensions": {"cpu": "arm64", "display_attached": "1", "gpu": "apple:m2", "hidpi": "1", "mac_model": "Mac14,7", "os": "Mac-14.4.1", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "gl_unittests", "test_id_prefix": "ninja://ui/gl:gl_unittests/"}], "isolated_scripts": [{"args": ["--override-steps=1", "--gtest-benchmark-name=dawn_perf_tests", "-v"], "merge": {"args": ["--smoke-test-mode"], "script": "//tools/perf/process_perf_results.py"}, "name": "dawn_perf_tests", "swarming": {"dimensions": {"cpu": "arm64", "display_attached": "1", "gpu": "apple:m2", "hidpi": "1", "mac_model": "Mac14,7", "os": "Mac-14.4.1", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "dawn_perf_tests", "test_id_prefix": "ninja://third_party/dawn/src/dawn/tests:dawn_perf_tests/"}, {"merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "telemetry_gpu_unittests", "resultdb": {"enable": true}, "swarming": {"dimensions": {"cpu": "arm64", "display_attached": "1", "gpu": "apple:m2", "hidpi": "1", "mac_model": "Mac14,7", "os": "Mac-14.4.1", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_unittests", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_unittests/"}, {"args": ["--flag-specific=webgpu", "--initialize-webgpu-adapter-at-startup-timeout-ms=60000"], "merge": {"args": ["--verbose"], "script": "//third_party/blink/tools/merge_web_test_results.py"}, "name": "webgpu_blink_web_tests", "resultdb": {"enable": true}, "swarming": {"dimensions": {"cpu": "arm64", "display_attached": "1", "gpu": "apple:m2", "hidpi": "1", "mac_model": "Mac14,7", "os": "Mac-14.4.1", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "webgpu_blink_web_tests", "test_id_prefix": "ninja://:webgpu_blink_web_tests/"}, {"args": ["--flag-specific=webgpu-with-backend-validation", "--timeout-ms=30000", "--initialize-webgpu-adapter-at-startup-timeout-ms=60000"], "merge": {"args": ["--verbose"], "script": "//third_party/blink/tools/merge_web_test_results.py"}, "name": "webgpu_blink_web_tests_with_backend_validation", "resultdb": {"enable": true}, "swarming": {"dimensions": {"cpu": "arm64", "display_attached": "1", "gpu": "apple:m2", "hidpi": "1", "mac_model": "Mac14,7", "os": "Mac-14.4.1", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "webgpu_blink_web_tests", "test_id_prefix": "ninja://:webgpu_blink_web_tests/"}, {"args": ["webgpu_cts", "--show-stdout", "--browser=release", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --force_high_performance_gpu", "--enforce-browser-version", "--use-worker=dedicated", "--jobs=4", "--use-webgpu-power-preference=default-high-performance"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "webgpu_cts_dedicated_worker_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"dimensions": {"cpu": "arm64", "display_attached": "1", "gpu": "apple:m2", "hidpi": "1", "mac_model": "Mac14,7", "os": "Mac-14.4.1", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["webgpu_cts", "--show-stdout", "--browser=release", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --force_high_performance_gpu", "--enforce-browser-version", "--use-worker=service", "--jobs=4", "--use-webgpu-power-preference=default-high-performance"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "webgpu_cts_service_worker_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"dimensions": {"cpu": "arm64", "display_attached": "1", "gpu": "apple:m2", "hidpi": "1", "mac_model": "Mac14,7", "os": "Mac-14.4.1", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["webgpu_cts", "--show-stdout", "--browser=release", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --force_high_performance_gpu", "--enforce-browser-version", "--use-worker=shared", "--jobs=4", "--use-webgpu-power-preference=default-high-performance"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "webgpu_cts_shared_worker_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"dimensions": {"cpu": "arm64", "display_attached": "1", "gpu": "apple:m2", "hidpi": "1", "mac_model": "Mac14,7", "os": "Mac-14.4.1", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["webgpu_cts", "--show-stdout", "--browser=release", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --force_high_performance_gpu", "--enforce-browser-version", "--jobs=4", "--use-webgpu-power-preference=default-high-performance"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "webgpu_cts_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"dimensions": {"cpu": "arm64", "display_attached": "1", "gpu": "apple:m2", "hidpi": "1", "mac_model": "Mac14,7", "os": "Mac-14.4.1", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>", "shards": 14}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["webgpu_cts", "--show-stdout", "--browser=release", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --force_high_performance_gpu", "--enforce-browser-version", "--enable-dawn-backend-validation", "--jobs=4", "--use-webgpu-power-preference=default-high-performance"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "webgpu_cts_with_validation_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"dimensions": {"cpu": "arm64", "display_attached": "1", "gpu": "apple:m2", "hidpi": "1", "mac_model": "Mac14,7", "os": "Mac-14.4.1", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>", "shards": 14}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["--flag-specific=webgpu-swiftshader", "--initialize-webgpu-adapter-at-startup-timeout-ms=60000"], "merge": {"args": ["--verbose"], "script": "//third_party/blink/tools/merge_web_test_results.py"}, "name": "webgpu_swiftshader_blink_web_tests", "resultdb": {"enable": true}, "swarming": {"dimensions": {"cpu": "arm64", "display_attached": "1", "gpu": "apple:m2", "hidpi": "1", "mac_model": "Mac14,7", "os": "Mac-14.4.1", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "webgpu_blink_web_tests", "test_id_prefix": "ninja://:webgpu_blink_web_tests/"}, {"args": ["--flag-specific=webgpu-swiftshader-with-backend-validation", "--timeout-ms=30000", "--initialize-webgpu-adapter-at-startup-timeout-ms=60000"], "merge": {"args": ["--verbose"], "script": "//third_party/blink/tools/merge_web_test_results.py"}, "name": "webgpu_swiftshader_blink_web_tests_with_backend_validation", "resultdb": {"enable": true}, "swarming": {"dimensions": {"cpu": "arm64", "display_attached": "1", "gpu": "apple:m2", "hidpi": "1", "mac_model": "Mac14,7", "os": "Mac-14.4.1", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "io_timeout": 1800, "service_account": "<EMAIL>"}, "test": "webgpu_blink_web_tests", "test_id_prefix": "ninja://:webgpu_blink_web_tests/"}, {"args": ["webgpu_cts", "--show-stdout", "--browser=release", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --force_high_performance_gpu", "--enforce-browser-version", "--use-webgpu-adapter=swiftshader", "--test-filter=*web_platform*", "--jobs=4", "--use-webgpu-power-preference=default-high-performance"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "webgpu_swiftshader_web_platform_cts_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"dimensions": {"cpu": "arm64", "display_attached": "1", "gpu": "apple:m2", "hidpi": "1", "mac_model": "Mac14,7", "os": "Mac-14.4.1", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>", "shards": 2}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}, {"args": ["webgpu_cts", "--show-stdout", "--browser=release", "--passthrough", "-v", "--stable-jobs", "--extra-browser-args=--enable-logging=stderr --js-flags=--expose-gc --force_high_performance_gpu", "--enforce-browser-version", "--use-webgpu-adapter=swiftshader", "--test-filter=*web_platform*", "--enable-dawn-backend-validation", "--jobs=4", "--use-webgpu-power-preference=default-high-performance"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "webgpu_swiftshader_web_platform_cts_with_validation_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"dimensions": {"cpu": "arm64", "display_attached": "1", "gpu": "apple:m2", "hidpi": "1", "mac_model": "Mac14,7", "os": "Mac-14.4.1", "pool": "chromium.tests.gpu"}, "hard_timeout": 1800, "idempotent": false, "io_timeout": 1800, "service_account": "<EMAIL>", "shards": 2}, "test": "telemetry_gpu_integration_test", "test_id_prefix": "ninja://chrome/test:telemetry_gpu_integration_test/"}]}}