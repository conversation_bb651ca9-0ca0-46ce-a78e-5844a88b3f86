{"$build/chromium_tests_builder_config": {"builder_config": {"additional_exclusions": ["infra/config/generated/builders/try/android-15-chrome-wpt-fyi-rel/gn-args.json"], "builder_db": {"entries": [{"builder_id": {"bucket": "ci", "builder": "android-15-chrome-wpt-fyi-rel", "project": "chromium"}, "builder_spec": {"build_gs_bucket": "chromium-android-archive", "builder_group": "chromium.android.fyi", "execution_mode": "COMPILE_AND_TEST", "legacy_android_config": {"config": "base_config"}, "legacy_chromium_config": {"apply_configs": ["mb"], "build_config": "Release", "config": "main_builder", "target_arch": "intel", "target_bits": 64, "target_platform": "android"}, "legacy_gclient_config": {"apply_configs": ["android"], "config": "chromium"}}}]}, "builder_ids": [{"bucket": "ci", "builder": "android-15-chrome-wpt-fyi-rel", "project": "chromium"}], "targets_spec_directory": "src/infra/config/generated/builders/try/android-15-chrome-wpt-fyi-rel/targets"}}, "$build/siso": {"configs": ["builder", "remote-link"], "enable_cloud_monitoring": true, "enable_cloud_profiler": true, "enable_cloud_trace": true, "experiments": [], "metrics_project": "chromium-reclient-metrics", "output_local_strategy": "greedy", "project": "rbe-chromium-untrusted", "remote_jobs": 150}, "$recipe_engine/resultdb/test_presentation": {"column_keys": [], "grouping_keys": ["status", "v.test_suite"]}, "builder_group": "tryserver.chromium.android", "recipe": "chromium_trybot"}