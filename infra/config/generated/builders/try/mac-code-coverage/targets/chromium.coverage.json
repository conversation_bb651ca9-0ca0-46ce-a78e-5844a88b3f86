{"mac-code-coverage": {"gtest_tests": [{"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "absl_hardening_tests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "absl_hardening_tests", "test_id_prefix": "ninja://third_party/abseil-cpp:absl_hardening_tests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "accessibility_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "accessibility_unittests", "test_id_prefix": "ninja://ui/accessibility:accessibility_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "angle_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "angle_unittests", "test_id_prefix": "ninja://third_party/angle/src/tests:angle_unittests/", "use_isolated_scripts_api": true}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "app_shell_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "app_shell_unittests", "test_id_prefix": "ninja://extensions/shell:app_shell_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "base_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "base_unittests", "test_id_prefix": "ninja://base:base_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "blink_common_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "blink_common_unittests", "test_id_prefix": "ninja://third_party/blink/common:blink_common_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "blink_fuzzer_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "blink_fuzzer_unittests", "test_id_prefix": "ninja://third_party/blink/renderer/platform:blink_fuzzer_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "blink_heap_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "blink_heap_unittests", "test_id_prefix": "ninja://third_party/blink/renderer/platform/heap:blink_heap_unittests/"}, {"args": ["--git-revision=${got_revision}"], "isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "blink_platform_unittests", "precommit_args": ["--gerrit-issue=${patch_issue}", "--gerrit-patchset=${patch_set}", "--buildbucket-id=${buildbucket_build_id}"], "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "blink_platform_unittests", "test_id_prefix": "ninja://third_party/blink/renderer/platform:blink_platform_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "boringssl_crypto_tests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "boringssl_crypto_tests", "test_id_prefix": "ninja://third_party/boringssl:boringssl_crypto_tests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "boringssl_ssl_tests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "boringssl_ssl_tests", "test_id_prefix": "ninja://third_party/boringssl:boringssl_ssl_tests/"}, {"args": ["--gtest_filter=-*UsingRealWebcam*"], "isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "capture_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "capture_unittests", "test_id_prefix": "ninja://media/capture:capture_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "cast_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "cast_unittests", "test_id_prefix": "ninja://media/cast:cast_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "cc_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "cc_unittests", "test_id_prefix": "ninja://cc:cc_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "chrome_app_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "chrome_app_unittests", "test_id_prefix": "ninja://chrome/test:chrome_app_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "chromedriver_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "chromedriver_unittests", "test_id_prefix": "ninja://chrome/test/chromedriver:chromedriver_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "components_browsertests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "components_browsertests", "test_id_prefix": "ninja://components:components_browsertests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "components_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>", "shards": 3}, "test": "components_unittests", "test_id_prefix": "ninja://components:components_unittests/"}, {"args": ["--coverage-continuous-mode=1"], "isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "content_browsertests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>", "shards": 8}, "test": "content_browsertests", "test_id_prefix": "ninja://content/test:content_browsertests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "content_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "content_unittests", "test_id_prefix": "ninja://content/test:content_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "crashpad_tests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "crashpad_tests", "test_id_prefix": "ninja://third_party/crashpad/crashpad:crashpad_tests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "cronet_tests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "cronet_tests", "test_id_prefix": "ninja://components/cronet:cronet_tests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "cronet_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "cronet_unittests", "test_id_prefix": "ninja://components/cronet:cronet_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "crypto_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "crypto_unittests", "test_id_prefix": "ninja://crypto:crypto_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "device_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "device_unittests", "test_id_prefix": "ninja://device:device_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "display_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "display_unittests", "test_id_prefix": "ninja://ui/display:display_unittests/"}, {"ci_only": true, "isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "env_chromium_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "env_chromium_unittests", "test_id_prefix": "ninja://third_party/leveldatabase:env_chromium_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "events_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "events_unittests", "test_id_prefix": "ninja://ui/events:events_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "extensions_browsertests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "extensions_browsertests", "test_id_prefix": "ninja://extensions:extensions_browsertests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "extensions_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "extensions_unittests", "test_id_prefix": "ninja://extensions:extensions_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "filesystem_service_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "filesystem_service_unittests", "test_id_prefix": "ninja://components/services/filesystem:filesystem_service_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "fuzzing_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "fuzzing_unittests", "test_id_prefix": "ninja://testing/libfuzzer/tests:fuzzing_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "gcm_unit_tests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "gcm_unit_tests", "test_id_prefix": "ninja://google_apis/gcm:gcm_unit_tests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "gfx_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "gfx_unittests", "test_id_prefix": "ninja://ui/gfx:gfx_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "gin_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "gin_unittests", "test_id_prefix": "ninja://gin:gin_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "google_apis_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "google_apis_unittests", "test_id_prefix": "ninja://google_apis:google_apis_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "gpu_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "gpu_unittests", "test_id_prefix": "ninja://gpu:gpu_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "gwp_asan_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "gwp_asan_unittests", "test_id_prefix": "ninja://components/gwp_asan:gwp_asan_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "headless_browsertests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "headless_browsertests", "test_id_prefix": "ninja://headless:headless_browsertests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "headless_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "headless_unittests", "test_id_prefix": "ninja://headless:headless_unittests/"}, {"args": ["--coverage-continuous-mode=1"], "isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "interactive_ui_tests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>", "shards": 3}, "test": "interactive_ui_tests", "test_id_prefix": "ninja://chrome/test:interactive_ui_tests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "ipc_tests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "ipc_tests", "test_id_prefix": "ninja://ipc:ipc_tests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "latency_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "latency_unittests", "test_id_prefix": "ninja://ui/latency:latency_unittests/"}, {"args": ["--test-launcher-filter-file=../../testing/buildbot/filters/layer_list_mode.cc_unittests.filter", "--enable-features=UseLayerListsByDefault"], "isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "layer_list_mode_cc_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "cc_unittests", "test_id_prefix": "ninja://cc:cc_unittests/"}, {"ci_only": true, "isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "leveldb_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "leveldb_unittests", "test_id_prefix": "ninja://third_party/leveldatabase:leveldb_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "libjingle_xmpp_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "libjingle_xmpp_unittests", "test_id_prefix": "ninja://third_party/libjingle_xmpp:libjingle_xmpp_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "liburlpattern_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "liburlpattern_unittests", "test_id_prefix": "ninja://third_party/liburlpattern:liburlpattern_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "media_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "media_unittests", "test_id_prefix": "ninja://media:media_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "message_center_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "message_center_unittests", "test_id_prefix": "ninja://ui/message_center:message_center_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "midi_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "midi_unittests", "test_id_prefix": "ninja://media/midi:midi_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "mojo_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "mojo_unittests", "test_id_prefix": "ninja://mojo:mojo_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "native_theme_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "native_theme_unittests", "test_id_prefix": "ninja://ui/native_theme:native_theme_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "net_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "net_unittests", "test_id_prefix": "ninja://net:net_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "openscreen_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "openscreen_unittests", "test_id_prefix": "ninja://chrome/browser/media/router:openscreen_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "pdf_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "pdf_unittests", "test_id_prefix": "ninja://pdf:pdf_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "perfetto_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "perfetto_unittests", "test_id_prefix": "ninja://third_party/perfetto:perfetto_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "power_sampler_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "power_sampler_unittests", "test_id_prefix": "ninja://tools/mac/power:power_sampler_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "printing_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "printing_unittests", "test_id_prefix": "ninja://printing:printing_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "remoting_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "remoting_unittests", "test_id_prefix": "ninja://remoting:remoting_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "sandbox_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "sandbox_unittests", "test_id_prefix": "ninja://sandbox:sandbox_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "services_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "services_unittests", "test_id_prefix": "ninja://services:services_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "shell_dialogs_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "shell_dialogs_unittests", "test_id_prefix": "ninja://ui/shell_dialogs:shell_dialogs_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "skia_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "skia_unittests", "test_id_prefix": "ninja://skia:skia_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "snapshot_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "snapshot_unittests", "test_id_prefix": "ninja://ui/snapshot:snapshot_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "sql_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "sql_unittests", "test_id_prefix": "ninja://sql:sql_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "storage_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "storage_unittests", "test_id_prefix": "ninja://storage:storage_unittests/"}, {"args": ["--coverage-continuous-mode=1"], "isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "sync_integration_tests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>", "shards": 3}, "test": "sync_integration_tests", "test_id_prefix": "ninja://chrome/test:sync_integration_tests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "ui_base_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "ui_base_unittests", "test_id_prefix": "ninja://ui/base:ui_base_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "ui_touch_selection_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "ui_touch_selection_unittests", "test_id_prefix": "ninja://ui/touch_selection:ui_touch_selection_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "ui_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "ui_unittests", "test_id_prefix": "ninja://ui/tests:ui_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "unit_tests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "unit_tests", "test_id_prefix": "ninja://chrome/test:unit_tests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "updater_tests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "updater_tests", "test_id_prefix": "ninja://chrome/updater:updater_tests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "url_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "url_unittests", "test_id_prefix": "ninja://url:url_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "views_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "views_unittests", "test_id_prefix": "ninja://ui/views:views_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "viz_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "viz_unittests", "test_id_prefix": "ninja://components/viz:viz_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "webkit_unit_tests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "blink_unittests", "test_id_prefix": "ninja://third_party/blink/renderer/controller:blink_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "wtf_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "wtf_unittests", "test_id_prefix": "ninja://third_party/blink/renderer/platform/wtf:wtf_unittests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "xr_browser_tests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "xr_browser_tests", "test_id_prefix": "ninja://chrome/test:xr_browser_tests/"}, {"isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "zlib_unittests", "swarming": {"dimensions": {"cpu": "x86-64", "os": "Mac-15"}, "service_account": "<EMAIL>"}, "test": "zlib_unittests", "test_id_prefix": "ninja://third_party/zlib:zlib_unittests/"}]}}