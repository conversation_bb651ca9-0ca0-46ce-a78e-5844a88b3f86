{"Network Service Linux": {"gtest_tests": [{"args": ["--enable-features=ForceWebRequestProxyForTest"], "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "network_service_web_request_proxy_browser_tests", "swarming": {"dimensions": {"os": "Ubuntu-22.04"}, "service_account": "<EMAIL>", "shards": 15}, "test": "browser_tests", "test_id_prefix": "ninja://chrome/test:browser_tests/"}]}}