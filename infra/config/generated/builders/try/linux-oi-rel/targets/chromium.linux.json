{"Linux Builder": {"additional_compile_targets": ["all"], "scripts": [{"name": "check_network_annotations", "script": "check_network_annotations.py"}, {"name": "check_static_initializers", "script": "check_static_initializers.py"}, {"name": "checkdeps", "script": "checkdeps.py"}, {"name": "checkperms", "script": "checkperms.py"}, {"name": "metrics_python_tests", "script": "metrics_python_tests.py"}, {"name": "webkit_lint", "script": "blink_lint_expectations.py"}]}, "linux-oi-rel": {"gtest_tests": [{"args": ["--enable-features=OriginKeyedProcessesByDefault"], "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "browser_tests", "swarming": {"dimensions": {"os": "Ubuntu-22.04"}, "service_account": "<EMAIL>", "shards": 33}, "test": "browser_tests", "test_id_prefix": "ninja://chrome/test:browser_tests/"}, {"args": ["--enable-features=OriginKeyedProcessesByDefault"], "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "content_browsertests", "swarming": {"dimensions": {"os": "Ubuntu-22.04"}, "service_account": "<EMAIL>", "shards": 8}, "test": "content_browsertests", "test_id_prefix": "ninja://content/test:content_browsertests/"}, {"args": ["--enable-features=OriginKeyedProcessesByDefault"], "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "content_unittests", "swarming": {"dimensions": {"os": "Ubuntu-22.04"}, "service_account": "<EMAIL>"}, "test": "content_unittests", "test_id_prefix": "ninja://content/test:content_unittests/"}, {"args": ["--enable-features=OriginKeyedProcessesByDefault"], "merge": {"script": "//testing/merge_scripts/standard_gtest_merge.py"}, "name": "unit_tests", "swarming": {"dimensions": {"os": "Ubuntu-22.04"}, "service_account": "<EMAIL>"}, "test": "unit_tests", "test_id_prefix": "ninja://chrome/test:unit_tests/"}], "isolated_scripts": [{"args": ["--num-retries=3", "--write-run-histories-to=${ISOLATED_OUTDIR}/run_histories.json", "--additional-driver-flag=--enable-features=OriginKeyedProcessesByDefault"], "merge": {"args": ["--verbose"], "script": "//third_party/blink/tools/merge_web_test_results.py"}, "name": "blink_web_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "results_handler": "layout tests", "swarming": {"dimensions": {"os": "Ubuntu-22.04"}, "service_account": "<EMAIL>", "shards": 9}, "test": "blink_web_tests", "test_id_prefix": "ninja://:blink_web_tests/"}, {"args": ["--num-retries=3", "--write-run-histories-to=${ISOLATED_OUTDIR}/run_histories.json", "--additional-driver-flag=--enable-features=OriginKeyedProcessesByDefault"], "merge": {"args": ["--verbose"], "script": "//third_party/blink/tools/merge_web_test_results.py"}, "name": "blink_wpt_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "results_handler": "layout tests", "swarming": {"dimensions": {"os": "Ubuntu-22.04"}, "service_account": "<EMAIL>", "shards": 2}, "test": "blink_wpt_tests", "test_id_prefix": "ninja://:blink_wpt_tests/"}, {"args": ["--test-launcher-filter-file=../../third_party/blink/web_tests/TestLists/chrome.filter", "--additional-driver-flag=--enable-features=OriginKeyedProcessesByDefault"], "merge": {"args": ["--verbose"], "script": "//third_party/blink/tools/merge_web_test_results.py"}, "name": "chrome_wpt_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "results_handler": "layout tests", "swarming": {"dimensions": {"os": "Ubuntu-22.04"}, "service_account": "<EMAIL>"}, "test": "chrome_wpt_tests", "test_id_prefix": "ninja://:chrome_wpt_tests/"}]}}