{"android-desktop-x64-compile-rel": {"additional_compile_targets": ["android_lint"]}, "android-desktop-x64-rel-15-tests": {"gtest_tests": [{"args": ["--avd-config=../../tools/android/avd/proto/android_35_google_apis_tablet_x64_tablet_landscape.textpb", "--force-android-desktop", "--test-launcher-filter-file=../../testing/buildbot/filters/android.desktop.emulator_15.android_browsertests.filter", "--emulator-debug-tags=all", "--gs-results-bucket=chromium-result-details", "--recover-devices"], "description": "Run with android_35_google_apis_tablet_x64", "merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "android_browsertests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "name": "android_browsertests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"dimensions": {"cores": "8", "cpu": "x86-64", "device_os": null, "device_type": null, "os": "Ubuntu-22.04", "pool": "chromium.tests.avd"}, "named_caches": [{"name": "android_35_google_apis_tablet_x64_tablet_landscape", "path": ".android_emulator/android_35_google_apis_tablet_x64_tablet_landscape"}], "optional_dimensions": {"60": {"caches": "android_35_google_apis_tablet_x64_tablet_landscape"}}, "service_account": "<EMAIL>", "shards": 22}, "test": "android_browsertests", "test_id_prefix": "ninja://chrome/test:android_browsertests/"}, {"args": ["--git-revision=${got_revision}", "--avd-config=../../tools/android/avd/proto/android_35_google_apis_tablet_x64_tablet_landscape.textpb", "--force-android-desktop", "--gtest_filter=-org.chromium.chrome.browser.ui.appmenu.AppMenuTest.testShowAppMenu_AnchorTop", "--gs-results-bucket=chromium-result-details", "--recover-devices"], "ci_only": true, "description": "Run with android_35_google_apis_tablet_x64", "merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "chrome_public_unit_test_apk"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "name": "chrome_public_unit_test_apk", "precommit_args": ["--gerrit-issue=${patch_issue}", "--gerrit-patchset=${patch_set}", "--buildbucket-id=${buildbucket_build_id}"], "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"dimensions": {"cores": "8", "cpu": "x86-64", "device_os": null, "device_type": null, "os": "Ubuntu-22.04", "pool": "chromium.tests.avd"}, "named_caches": [{"name": "android_35_google_apis_tablet_x64_tablet_landscape", "path": ".android_emulator/android_35_google_apis_tablet_x64_tablet_landscape"}], "optional_dimensions": {"60": {"caches": "android_35_google_apis_tablet_x64_tablet_landscape"}}, "service_account": "<EMAIL>", "shards": 2}, "test": "chrome_public_unit_test_apk", "test_id_prefix": "ninja://chrome/android:chrome_public_unit_test_apk/"}, {"args": ["--avd-config=../../tools/android/avd/proto/android_35_google_apis_tablet_x64_tablet_landscape.textpb", "--force-android-desktop", "--gs-results-bucket=chromium-result-details", "--recover-devices"], "description": "Run with android_35_google_apis_tablet_x64", "merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "extensions_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "name": "extensions_unittests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"dimensions": {"cores": "8", "cpu": "x86-64", "device_os": null, "device_type": null, "os": "Ubuntu-22.04", "pool": "chromium.tests.avd"}, "named_caches": [{"name": "android_35_google_apis_tablet_x64_tablet_landscape", "path": ".android_emulator/android_35_google_apis_tablet_x64_tablet_landscape"}], "optional_dimensions": {"60": {"caches": "android_35_google_apis_tablet_x64_tablet_landscape"}}, "service_account": "<EMAIL>"}, "test": "extensions_unittests", "test_id_prefix": "ninja://extensions:extensions_unittests/"}, {"args": ["--avd-config=../../tools/android/avd/proto/android_35_google_apis_tablet_x64_tablet_landscape.textpb", "--force-android-desktop", "--test-launcher-filter-file=../../testing/buildbot/filters/android.desktop.emulator_15.unit_tests.filter", "--gs-results-bucket=chromium-result-details", "--recover-devices"], "description": "Run with android_35_google_apis_tablet_x64", "merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "unit_tests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "name": "unit_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"dimensions": {"cores": "8", "cpu": "x86-64", "device_os": null, "device_type": null, "os": "Ubuntu-22.04", "pool": "chromium.tests.avd"}, "named_caches": [{"name": "android_35_google_apis_tablet_x64_tablet_landscape", "path": ".android_emulator/android_35_google_apis_tablet_x64_tablet_landscape"}], "optional_dimensions": {"60": {"caches": "android_35_google_apis_tablet_x64_tablet_landscape"}}, "service_account": "<EMAIL>", "shards": 2}, "test": "unit_tests", "test_id_prefix": "ninja://chrome/test:unit_tests/"}, {"args": ["--avd-config=../../tools/android/avd/proto/android_35_google_apis_tablet_x64_tablet_landscape.textpb", "--force-android-desktop", "--gs-results-bucket=chromium-result-details", "--recover-devices"], "description": "Run with android_35_google_apis_tablet_x64", "merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "video_encode_accelerator_tests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "name": "video_encode_accelerator_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"dimensions": {"cores": "8", "cpu": "x86-64", "device_os": null, "device_type": null, "os": "Ubuntu-22.04", "pool": "chromium.tests.avd"}, "named_caches": [{"name": "android_35_google_apis_tablet_x64_tablet_landscape", "path": ".android_emulator/android_35_google_apis_tablet_x64_tablet_landscape"}], "optional_dimensions": {"60": {"caches": "android_35_google_apis_tablet_x64_tablet_landscape"}}, "service_account": "<EMAIL>"}, "test": "video_encode_accelerator_tests", "test_id_prefix": "ninja://media/gpu/test:video_encode_accelerator_tests/"}], "isolated_scripts": [{"merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "chrome_junit_tests", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"dimensions": {"cores": "8", "cpu": "x86-64", "os": "Ubuntu-22.04", "pool": "chromium.tests"}, "service_account": "<EMAIL>"}, "test": "chrome_junit_tests", "test_id_prefix": "ninja://chrome/android:chrome_junit_tests/"}]}}