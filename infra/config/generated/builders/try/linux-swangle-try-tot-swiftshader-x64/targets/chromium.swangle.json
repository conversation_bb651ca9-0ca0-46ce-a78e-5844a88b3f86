{"linux-swangle-tot-swiftshader-x64": {"gtest_tests": [{"args": ["--use-angle=swiftshader"], "isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "angle_deqp_egl_tests", "swarming": {"dimensions": {"cpu": "x86-64", "gpu": "none", "os": "Ubuntu-22.04", "pool": "chromium.tests.gpu"}, "hard_timeout": 900, "io_timeout": 900, "service_account": "<EMAIL>"}, "test": "angle_deqp_egl_tests", "test_id_prefix": "ninja://third_party/angle/src/tests:angle_deqp_egl_tests/", "use_isolated_scripts_api": true}, {"args": ["--use-angle=swiftshader"], "isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "angle_deqp_gles2_tests", "swarming": {"dimensions": {"cpu": "x86-64", "gpu": "none", "os": "Ubuntu-22.04", "pool": "chromium.tests.gpu"}, "hard_timeout": 900, "io_timeout": 900, "service_account": "<EMAIL>"}, "test": "angle_deqp_gles2_tests", "test_id_prefix": "ninja://third_party/angle/src/tests:angle_deqp_gles2_tests/", "use_isolated_scripts_api": true}, {"args": ["--use-angle=swiftshader"], "isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "angle_deqp_gles31_rotate180_tests", "swarming": {"dimensions": {"cpu": "x86-64", "gpu": "none", "os": "Ubuntu-22.04", "pool": "chromium.tests.gpu"}, "hard_timeout": 900, "io_timeout": 900, "service_account": "<EMAIL>"}, "test": "angle_deqp_gles31_rotate180_tests", "test_id_prefix": "ninja://third_party/angle/src/tests:angle_deqp_gles31_rotate180_tests/", "use_isolated_scripts_api": true}, {"args": ["--use-angle=swiftshader"], "isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "angle_deqp_gles31_rotate270_tests", "swarming": {"dimensions": {"cpu": "x86-64", "gpu": "none", "os": "Ubuntu-22.04", "pool": "chromium.tests.gpu"}, "hard_timeout": 900, "io_timeout": 900, "service_account": "<EMAIL>"}, "test": "angle_deqp_gles31_rotate270_tests", "test_id_prefix": "ninja://third_party/angle/src/tests:angle_deqp_gles31_rotate270_tests/", "use_isolated_scripts_api": true}, {"args": ["--use-angle=swiftshader"], "isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "angle_deqp_gles31_rotate90_tests", "swarming": {"dimensions": {"cpu": "x86-64", "gpu": "none", "os": "Ubuntu-22.04", "pool": "chromium.tests.gpu"}, "hard_timeout": 900, "io_timeout": 900, "service_account": "<EMAIL>"}, "test": "angle_deqp_gles31_rotate90_tests", "test_id_prefix": "ninja://third_party/angle/src/tests:angle_deqp_gles31_rotate90_tests/", "use_isolated_scripts_api": true}, {"args": ["--use-angle=swiftshader"], "isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "angle_deqp_gles31_tests", "swarming": {"dimensions": {"cpu": "x86-64", "gpu": "none", "os": "Ubuntu-22.04", "pool": "chromium.tests.gpu"}, "hard_timeout": 900, "io_timeout": 900, "service_account": "<EMAIL>", "shards": 10}, "test": "angle_deqp_gles31_tests", "test_id_prefix": "ninja://third_party/angle/src/tests:angle_deqp_gles31_tests/", "use_isolated_scripts_api": true}, {"args": ["--use-angle=swiftshader"], "isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "angle_deqp_gles3_rotate180_tests", "swarming": {"dimensions": {"cpu": "x86-64", "gpu": "none", "os": "Ubuntu-22.04", "pool": "chromium.tests.gpu"}, "hard_timeout": 900, "io_timeout": 900, "service_account": "<EMAIL>"}, "test": "angle_deqp_gles3_rotate180_tests", "test_id_prefix": "ninja://third_party/angle/src/tests:angle_deqp_gles3_rotate180_tests/", "use_isolated_scripts_api": true}, {"args": ["--use-angle=swiftshader"], "isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "angle_deqp_gles3_rotate270_tests", "swarming": {"dimensions": {"cpu": "x86-64", "gpu": "none", "os": "Ubuntu-22.04", "pool": "chromium.tests.gpu"}, "hard_timeout": 900, "io_timeout": 900, "service_account": "<EMAIL>"}, "test": "angle_deqp_gles3_rotate270_tests", "test_id_prefix": "ninja://third_party/angle/src/tests:angle_deqp_gles3_rotate270_tests/", "use_isolated_scripts_api": true}, {"args": ["--use-angle=swiftshader"], "isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "angle_deqp_gles3_rotate90_tests", "swarming": {"dimensions": {"cpu": "x86-64", "gpu": "none", "os": "Ubuntu-22.04", "pool": "chromium.tests.gpu"}, "hard_timeout": 900, "io_timeout": 900, "service_account": "<EMAIL>"}, "test": "angle_deqp_gles3_rotate90_tests", "test_id_prefix": "ninja://third_party/angle/src/tests:angle_deqp_gles3_rotate90_tests/", "use_isolated_scripts_api": true}, {"args": ["--use-angle=swiftshader"], "isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "angle_deqp_gles3_tests", "swarming": {"dimensions": {"cpu": "x86-64", "gpu": "none", "os": "Ubuntu-22.04", "pool": "chromium.tests.gpu"}, "hard_timeout": 900, "io_timeout": 900, "service_account": "<EMAIL>", "shards": 4}, "test": "angle_deqp_gles3_tests", "test_id_prefix": "ninja://third_party/angle/src/tests:angle_deqp_gles3_tests/", "use_isolated_scripts_api": true}, {"args": ["--use-angle=swiftshader"], "isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "angle_deqp_khr_gles2_tests", "swarming": {"dimensions": {"cpu": "x86-64", "gpu": "none", "os": "Ubuntu-22.04", "pool": "chromium.tests.gpu"}, "hard_timeout": 900, "io_timeout": 900, "service_account": "<EMAIL>"}, "test": "angle_deqp_khr_gles2_tests", "test_id_prefix": "ninja://third_party/angle/src/tests:angle_deqp_khr_gles2_tests/", "use_isolated_scripts_api": true}, {"args": ["--use-angle=swiftshader"], "isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "angle_deqp_khr_gles31_tests", "swarming": {"dimensions": {"cpu": "x86-64", "gpu": "none", "os": "Ubuntu-22.04", "pool": "chromium.tests.gpu"}, "hard_timeout": 900, "io_timeout": 900, "service_account": "<EMAIL>"}, "test": "angle_deqp_khr_gles31_tests", "test_id_prefix": "ninja://third_party/angle/src/tests:angle_deqp_khr_gles31_tests/", "use_isolated_scripts_api": true}, {"args": ["--use-angle=swiftshader"], "isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "angle_deqp_khr_gles3_tests", "swarming": {"dimensions": {"cpu": "x86-64", "gpu": "none", "os": "Ubuntu-22.04", "pool": "chromium.tests.gpu"}, "hard_timeout": 900, "io_timeout": 900, "service_account": "<EMAIL>"}, "test": "angle_deqp_khr_gles3_tests", "test_id_prefix": "ninja://third_party/angle/src/tests:angle_deqp_khr_gles3_tests/", "use_isolated_scripts_api": true}, {"args": ["--gtest_filter=*Vulkan_SwiftShader*"], "isolate_profile_data": true, "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "angle_end2end_tests", "swarming": {"dimensions": {"cpu": "x86-64", "gpu": "none", "os": "Ubuntu-22.04", "pool": "chromium.tests.gpu"}, "hard_timeout": 900, "io_timeout": 900, "service_account": "<EMAIL>", "shards": 2}, "test": "angle_end2end_tests", "test_id_prefix": "ninja://third_party/angle/src/tests:angle_end2end_tests/", "use_isolated_scripts_api": true}]}}