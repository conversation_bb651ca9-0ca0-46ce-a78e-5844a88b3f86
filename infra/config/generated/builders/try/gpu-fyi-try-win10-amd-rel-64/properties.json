{"$build/chromium_tests_builder_config": {"builder_config": {"additional_exclusions": ["infra/config/generated/builders/try/gpu-fyi-try-win10-amd-rel-64/gn-args.json"], "builder_db": {"entries": [{"builder_id": {"bucket": "ci", "builder": "GPU FYI Win x64 Builder", "project": "chromium"}, "builder_spec": {"builder_group": "chromium.gpu.fyi", "execution_mode": "COMPILE_AND_TEST", "legacy_chromium_config": {"apply_configs": ["mb"], "build_config": "Release", "config": "chromium", "target_bits": 64, "target_platform": "win"}, "legacy_gclient_config": {"config": "chromium"}}}, {"builder_id": {"bucket": "ci", "builder": "Win10 FYI x64 Release (AMD RX 5500 XT)", "project": "chromium"}, "builder_spec": {"builder_group": "chromium.gpu.fyi", "execution_mode": "TEST", "legacy_chromium_config": {"apply_configs": ["mb"], "build_config": "Release", "config": "chromium", "target_bits": 64, "target_platform": "win"}, "legacy_gclient_config": {"config": "chromium"}, "parent": {"bucket": "ci", "builder": "GPU FYI Win x64 Builder", "project": "chromium"}, "run_tests_serially": true}}]}, "builder_ids": [{"bucket": "ci", "builder": "GPU FYI Win x64 Builder", "project": "chromium"}], "builder_ids_in_scope_for_testing": [{"bucket": "ci", "builder": "Win10 FYI x64 Release (AMD RX 5500 XT)", "project": "chromium"}], "targets_spec_directory": "src/infra/config/generated/builders/try/gpu-fyi-try-win10-amd-rel-64/targets"}}, "$build/siso": {"configs": ["builder"], "enable_cloud_monitoring": true, "enable_cloud_profiler": true, "enable_cloud_trace": true, "experiments": [], "metrics_project": "chromium-reclient-metrics", "project": "rbe-chromium-untrusted", "remote_jobs": 150}, "$recipe_engine/resultdb/test_presentation": {"column_keys": [], "grouping_keys": ["status", "v.test_suite"]}, "builder_group": "tryserver.chromium.win", "recipe": "chromium_trybot"}