{"$build/chromium_tests_builder_config": {"builder_config": {"additional_exclusions": ["infra/config/generated/builders/try/android-cast-arm-rel/gn-args.json"], "builder_db": {"entries": [{"builder_id": {"bucket": "ci", "builder": "android-cast-arm-rel", "project": "chromium"}, "builder_spec": {"build_gs_bucket": "chromium-android-archive", "builder_group": "chromium.android", "execution_mode": "COMPILE_AND_TEST", "legacy_android_config": {"config": "base_config"}, "legacy_chromium_config": {"apply_configs": ["mb"], "build_config": "Release", "config": "main_builder", "target_arch": "arm", "target_bits": 32, "target_platform": "android"}, "legacy_gclient_config": {"apply_configs": ["android"], "config": "chromium"}}}]}, "builder_ids": [{"bucket": "ci", "builder": "android-cast-arm-rel", "project": "chromium"}], "targets_spec_directory": "src/infra/config/generated/builders/try/android-cast-arm-rel/targets"}}, "$build/flakiness": {"check_for_flakiness": true, "check_for_flakiness_with_resultdb": true}, "$build/siso": {"configs": ["builder", "remote-link"], "enable_cloud_monitoring": true, "enable_cloud_profiler": true, "enable_cloud_trace": true, "experiments": [], "metrics_project": "chromium-reclient-metrics", "output_local_strategy": "greedy", "project": "rbe-chromium-untrusted", "remote_jobs": -1}, "$recipe_engine/resultdb/test_presentation": {"column_keys": [], "grouping_keys": ["status", "v.test_suite"]}, "builder_group": "tryserver.chromium.android", "cq": "path-based", "recipe": "chromium_trybot"}