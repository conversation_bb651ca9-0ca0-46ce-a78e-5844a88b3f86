{"ios18-sdk-simulator": {"additional_compile_targets": ["all"], "isolated_scripts": [{"args": ["--platform", "iPhone 14", "--version", "17.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "absl_hardening_tests iPhone 14 17.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_17_5", "path": "Runtime-ios-17.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "absl_hardening_tests", "test_id_prefix": "ninja://third_party/abseil-cpp:absl_hardening_tests/", "variant_id": "iPhone 14 17.5"}, {"args": ["--platform", "iPhone 15", "--version", "18.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "absl_hardening_tests iPhone 15 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "absl_hardening_tests", "test_id_prefix": "ninja://third_party/abseil-cpp:absl_hardening_tests/", "variant_id": "iPhone 15 18.5"}, {"args": ["--platform", "iPad Air 11-inch (M2)", "--version", "18.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "base_unittests iPad Air (6th generation) 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "base_unittests", "test_id_prefix": "ninja://base:base_unittests/", "variant_id": "iPad Air (6th generation) 18.5"}, {"args": ["--platform", "iPhone 15", "--version", "18.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "base_unittests iPhone 15 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "base_unittests", "test_id_prefix": "ninja://base:base_unittests/", "variant_id": "iPhone 15 18.5"}, {"args": ["--platform", "iPhone SE (3rd generation)", "--version", "18.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "base_unittests iPhone SE (3rd generation) 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "base_unittests", "test_id_prefix": "ninja://base:base_unittests/", "variant_id": "iPhone SE (3rd generation) 18.5"}, {"args": ["--platform", "iPhone 14", "--version", "17.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "boringssl_crypto_tests iPhone 14 17.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_17_5", "path": "Runtime-ios-17.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "boringssl_crypto_tests", "test_id_prefix": "ninja://third_party/boringssl:boringssl_crypto_tests/", "variant_id": "iPhone 14 17.5"}, {"args": ["--platform", "iPhone 15", "--version", "18.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "boringssl_crypto_tests iPhone 15 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "boringssl_crypto_tests", "test_id_prefix": "ninja://third_party/boringssl:boringssl_crypto_tests/", "variant_id": "iPhone 15 18.5"}, {"args": ["--platform", "iPhone 14", "--version", "17.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "boringssl_ssl_tests iPhone 14 17.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_17_5", "path": "Runtime-ios-17.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "boringssl_ssl_tests", "test_id_prefix": "ninja://third_party/boringssl:boringssl_ssl_tests/", "variant_id": "iPhone 14 17.5"}, {"args": ["--platform", "iPhone 15", "--version", "18.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "boringssl_ssl_tests iPhone 15 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "boringssl_ssl_tests", "test_id_prefix": "ninja://third_party/boringssl:boringssl_ssl_tests/", "variant_id": "iPhone 15 18.5"}, {"args": ["--platform", "iPad Air 11-inch (M2)", "--version", "18.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "components_unittests iPad Air (6th generation) 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "components_unittests", "test_id_prefix": "ninja://components:components_unittests/", "variant_id": "iPad Air (6th generation) 18.5"}, {"args": ["--platform", "iPhone 15", "--version", "18.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "components_unittests iPhone 15 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "components_unittests", "test_id_prefix": "ninja://components:components_unittests/", "variant_id": "iPhone 15 18.5"}, {"args": ["--platform", "iPhone SE (3rd generation)", "--version", "18.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "components_unittests iPhone SE (3rd generation) 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "components_unittests", "test_id_prefix": "ninja://components:components_unittests/", "variant_id": "iPhone SE (3rd generation) 18.5"}, {"args": ["--platform", "iPhone 14", "--version", "17.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "crypto_unittests iPhone 14 17.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_17_5", "path": "Runtime-ios-17.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "crypto_unittests", "test_id_prefix": "ninja://crypto:crypto_unittests/", "variant_id": "iPhone 14 17.5"}, {"args": ["--platform", "iPhone 15", "--version", "18.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "crypto_unittests iPhone 15 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "crypto_unittests", "test_id_prefix": "ninja://crypto:crypto_unittests/", "variant_id": "iPhone 15 18.5"}, {"args": ["--platform", "iPad Air 11-inch (M2)", "--version", "18.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "gfx_unittests iPad Air (6th generation) 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "gfx_unittests", "test_id_prefix": "ninja://ui/gfx:gfx_unittests/", "variant_id": "iPad Air (6th generation) 18.5"}, {"args": ["--platform", "iPhone 15", "--version", "18.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "gfx_unittests iPhone 15 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "gfx_unittests", "test_id_prefix": "ninja://ui/gfx:gfx_unittests/", "variant_id": "iPhone 15 18.5"}, {"args": ["--platform", "iPhone SE (3rd generation)", "--version", "18.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "gfx_unittests iPhone SE (3rd generation) 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "gfx_unittests", "test_id_prefix": "ninja://ui/gfx:gfx_unittests/", "variant_id": "iPhone SE (3rd generation) 18.5"}, {"args": ["--platform", "iPhone 14", "--version", "17.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "google_apis_unittests iPhone 14 17.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_17_5", "path": "Runtime-ios-17.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "google_apis_unittests", "test_id_prefix": "ninja://google_apis:google_apis_unittests/", "variant_id": "iPhone 14 17.5"}, {"args": ["--platform", "iPhone 15", "--version", "18.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "google_apis_unittests iPhone 15 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "google_apis_unittests", "test_id_prefix": "ninja://google_apis:google_apis_unittests/", "variant_id": "iPhone 15 18.5"}, {"args": ["--platform", "iPhone 14", "--version", "17.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "gwp_asan_unittests iPhone 14 17.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_17_5", "path": "Runtime-ios-17.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "gwp_asan_unittests", "test_id_prefix": "ninja://components/gwp_asan:gwp_asan_unittests/", "variant_id": "iPhone 14 17.5"}, {"args": ["--platform", "iPhone 15", "--version", "18.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "gwp_asan_unittests iPhone 15 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "gwp_asan_unittests", "test_id_prefix": "ninja://components/gwp_asan:gwp_asan_unittests/", "variant_id": "iPhone 15 18.5"}, {"args": ["--platform", "iPad Air 11-inch (M2)", "--version", "18.5", "--xcodebuild-sim-runner", "--record-video", "failed_only", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ios_chrome_bookmarks_eg2tests_module iPad Air (6th generation) 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>", "shards": 2}, "test": "ios_chrome_bookmarks_eg2tests_module", "test_id_prefix": "ninja://ios/chrome/test/earl_grey2:ios_chrome_bookmarks_eg2tests_module/", "variant_id": "iPad Air (6th generation) 18.5"}, {"args": ["--platform", "iPhone 14", "--version", "17.5", "--xcodebuild-sim-runner", "--record-video", "failed_only", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ios_chrome_bookmarks_eg2tests_module iPhone 14 17.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_17_5", "path": "Runtime-ios-17.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>", "shards": 2}, "test": "ios_chrome_bookmarks_eg2tests_module", "test_id_prefix": "ninja://ios/chrome/test/earl_grey2:ios_chrome_bookmarks_eg2tests_module/", "variant_id": "iPhone 14 17.5"}, {"args": ["--platform", "iPhone 15", "--version", "18.5", "--xcodebuild-sim-runner", "--record-video", "failed_only", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ios_chrome_bookmarks_eg2tests_module iPhone 15 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>", "shards": 2}, "test": "ios_chrome_bookmarks_eg2tests_module", "test_id_prefix": "ninja://ios/chrome/test/earl_grey2:ios_chrome_bookmarks_eg2tests_module/", "variant_id": "iPhone 15 18.5"}, {"args": ["--clones", "2", "--platform", "iPad Air 11-inch (M2)", "--version", "18.5", "--xcodebuild-sim-runner", "--record-video", "failed_only", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ios_chrome_integration_eg2tests_module iPad Air (6th generation) 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>", "shards": 8}, "test": "ios_chrome_integration_eg2tests_module", "test_id_prefix": "ninja://ios/chrome/test/earl_grey2:ios_chrome_integration_eg2tests_module/", "variant_id": "iPad Air (6th generation) 18.5"}, {"args": ["--clones", "2", "--platform", "iPhone 14", "--version", "17.5", "--xcodebuild-sim-runner", "--record-video", "failed_only", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ios_chrome_integration_eg2tests_module iPhone 14 17.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_17_5", "path": "Runtime-ios-17.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>", "shards": 8}, "test": "ios_chrome_integration_eg2tests_module", "test_id_prefix": "ninja://ios/chrome/test/earl_grey2:ios_chrome_integration_eg2tests_module/", "variant_id": "iPhone 14 17.5"}, {"args": ["--clones", "2", "--platform", "iPhone 15", "--version", "18.5", "--xcodebuild-sim-runner", "--record-video", "failed_only", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ios_chrome_integration_eg2tests_module iPhone 15 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>", "shards": 8}, "test": "ios_chrome_integration_eg2tests_module", "test_id_prefix": "ninja://ios/chrome/test/earl_grey2:ios_chrome_integration_eg2tests_module/", "variant_id": "iPhone 15 18.5"}, {"args": ["--clones", "2", "--platform", "iPad Air 11-inch (M2)", "--version", "18.5", "--xcodebuild-sim-runner", "--record-video", "failed_only", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ios_chrome_settings_eg2tests_module iPad Air (6th generation) 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>", "shards": 4}, "test": "ios_chrome_settings_eg2tests_module", "test_id_prefix": "ninja://ios/chrome/test/earl_grey2:ios_chrome_settings_eg2tests_module/", "variant_id": "iPad Air (6th generation) 18.5"}, {"args": ["--clones", "2", "--platform", "iPhone 14", "--version", "17.5", "--xcodebuild-sim-runner", "--record-video", "failed_only", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ios_chrome_settings_eg2tests_module iPhone 14 17.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_17_5", "path": "Runtime-ios-17.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>", "shards": 4}, "test": "ios_chrome_settings_eg2tests_module", "test_id_prefix": "ninja://ios/chrome/test/earl_grey2:ios_chrome_settings_eg2tests_module/", "variant_id": "iPhone 14 17.5"}, {"args": ["--clones", "2", "--platform", "iPhone 15", "--version", "18.5", "--xcodebuild-sim-runner", "--record-video", "failed_only", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ios_chrome_settings_eg2tests_module iPhone 15 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>", "shards": 4}, "test": "ios_chrome_settings_eg2tests_module", "test_id_prefix": "ninja://ios/chrome/test/earl_grey2:ios_chrome_settings_eg2tests_module/", "variant_id": "iPhone 15 18.5"}, {"args": ["--platform", "iPad Air 11-inch (M2)", "--version", "18.5", "--xcodebuild-sim-runner", "--record-video", "failed_only", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ios_chrome_signin_eg2tests_module iPad Air (6th generation) 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>", "shards": 6}, "test": "ios_chrome_signin_eg2tests_module", "test_id_prefix": "ninja://ios/chrome/test/earl_grey2:ios_chrome_signin_eg2tests_module/", "variant_id": "iPad Air (6th generation) 18.5"}, {"args": ["--platform", "iPhone 14", "--version", "17.5", "--xcodebuild-sim-runner", "--record-video", "failed_only", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ios_chrome_signin_eg2tests_module iPhone 14 17.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_17_5", "path": "Runtime-ios-17.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>", "shards": 6}, "test": "ios_chrome_signin_eg2tests_module", "test_id_prefix": "ninja://ios/chrome/test/earl_grey2:ios_chrome_signin_eg2tests_module/", "variant_id": "iPhone 14 17.5"}, {"args": ["--platform", "iPhone 15", "--version", "18.5", "--xcodebuild-sim-runner", "--record-video", "failed_only", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ios_chrome_signin_eg2tests_module iPhone 15 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>", "shards": 6}, "test": "ios_chrome_signin_eg2tests_module", "test_id_prefix": "ninja://ios/chrome/test/earl_grey2:ios_chrome_signin_eg2tests_module/", "variant_id": "iPhone 15 18.5"}, {"args": ["--platform", "iPad Air 11-inch (M2)", "--version", "18.5", "--xcodebuild-sim-runner", "--record-video", "failed_only", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ios_chrome_smoke_eg2tests_module iPad Air (6th generation) 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "ios_chrome_smoke_eg2tests_module", "test_id_prefix": "ninja://ios/chrome/test/earl_grey2:ios_chrome_smoke_eg2tests_module/", "variant_id": "iPad Air (6th generation) 18.5"}, {"args": ["--platform", "iPhone 14", "--version", "17.5", "--xcodebuild-sim-runner", "--record-video", "failed_only", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ios_chrome_smoke_eg2tests_module iPhone 14 17.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_17_5", "path": "Runtime-ios-17.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "ios_chrome_smoke_eg2tests_module", "test_id_prefix": "ninja://ios/chrome/test/earl_grey2:ios_chrome_smoke_eg2tests_module/", "variant_id": "iPhone 14 17.5"}, {"args": ["--platform", "iPhone 15", "--version", "18.5", "--xcodebuild-sim-runner", "--record-video", "failed_only", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ios_chrome_smoke_eg2tests_module iPhone 15 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "ios_chrome_smoke_eg2tests_module", "test_id_prefix": "ninja://ios/chrome/test/earl_grey2:ios_chrome_smoke_eg2tests_module/", "variant_id": "iPhone 15 18.5"}, {"args": ["--clones", "2", "--platform", "iPad Air 11-inch (M2)", "--version", "18.5", "--xcodebuild-sim-runner", "--record-video", "failed_only", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ios_chrome_ui_eg2tests_module iPad Air (6th generation) 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>", "shards": 12}, "test": "ios_chrome_ui_eg2tests_module", "test_id_prefix": "ninja://ios/chrome/test/earl_grey2:ios_chrome_ui_eg2tests_module/", "variant_id": "iPad Air (6th generation) 18.5"}, {"args": ["--clones", "2", "--platform", "iPhone 14", "--version", "17.5", "--xcodebuild-sim-runner", "--record-video", "failed_only", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ios_chrome_ui_eg2tests_module iPhone 14 17.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_17_5", "path": "Runtime-ios-17.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>", "shards": 12}, "test": "ios_chrome_ui_eg2tests_module", "test_id_prefix": "ninja://ios/chrome/test/earl_grey2:ios_chrome_ui_eg2tests_module/", "variant_id": "iPhone 14 17.5"}, {"args": ["--clones", "2", "--platform", "iPhone 15", "--version", "18.5", "--xcodebuild-sim-runner", "--record-video", "failed_only", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ios_chrome_ui_eg2tests_module iPhone 15 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>", "shards": 12}, "test": "ios_chrome_ui_eg2tests_module", "test_id_prefix": "ninja://ios/chrome/test/earl_grey2:ios_chrome_ui_eg2tests_module/", "variant_id": "iPhone 15 18.5"}, {"args": ["--platform", "iPad Air 11-inch (M2)", "--version", "18.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ios_chrome_unittests iPad Air (6th generation) 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "ios_chrome_unittests", "test_id_prefix": "ninja://ios/chrome/test:ios_chrome_unittests/", "variant_id": "iPad Air (6th generation) 18.5"}, {"args": ["--platform", "iPhone 15", "--version", "18.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ios_chrome_unittests iPhone 15 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "ios_chrome_unittests", "test_id_prefix": "ninja://ios/chrome/test:ios_chrome_unittests/", "variant_id": "iPhone 15 18.5"}, {"args": ["--platform", "iPhone SE (3rd generation)", "--version", "18.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ios_chrome_unittests iPhone SE (3rd generation) 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "ios_chrome_unittests", "test_id_prefix": "ninja://ios/chrome/test:ios_chrome_unittests/", "variant_id": "iPhone SE (3rd generation) 18.5"}, {"args": ["--platform", "iPad Air 11-inch (M2)", "--version", "18.5", "--xcodebuild-sim-runner", "--record-video", "failed_only", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ios_chrome_web_eg2tests_module iPad Air (6th generation) 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>", "shards": 2}, "test": "ios_chrome_web_eg2tests_module", "test_id_prefix": "ninja://ios/chrome/test/earl_grey2:ios_chrome_web_eg2tests_module/", "variant_id": "iPad Air (6th generation) 18.5"}, {"args": ["--platform", "iPhone 14", "--version", "17.5", "--xcodebuild-sim-runner", "--record-video", "failed_only", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ios_chrome_web_eg2tests_module iPhone 14 17.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_17_5", "path": "Runtime-ios-17.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>", "shards": 2}, "test": "ios_chrome_web_eg2tests_module", "test_id_prefix": "ninja://ios/chrome/test/earl_grey2:ios_chrome_web_eg2tests_module/", "variant_id": "iPhone 14 17.5"}, {"args": ["--platform", "iPhone 15", "--version", "18.5", "--xcodebuild-sim-runner", "--record-video", "failed_only", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ios_chrome_web_eg2tests_module iPhone 15 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>", "shards": 2}, "test": "ios_chrome_web_eg2tests_module", "test_id_prefix": "ninja://ios/chrome/test/earl_grey2:ios_chrome_web_eg2tests_module/", "variant_id": "iPhone 15 18.5"}, {"args": ["--platform", "iPhone 14", "--version", "17.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ios_components_unittests iPhone 14 17.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_17_5", "path": "Runtime-ios-17.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "ios_components_unittests", "test_id_prefix": "ninja://ios/components:ios_components_unittests/", "variant_id": "iPhone 14 17.5"}, {"args": ["--platform", "iPhone 15", "--version", "18.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ios_components_unittests iPhone 15 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "ios_components_unittests", "test_id_prefix": "ninja://ios/components:ios_components_unittests/", "variant_id": "iPhone 15 18.5"}, {"args": ["--platform", "iPhone 14", "--version", "17.5", "--xcodebuild-sim-runner", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ios_crash_xcuitests_module iPhone 14 17.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_17_5", "path": "Runtime-ios-17.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "ios_crash_xcuitests_module", "test_id_prefix": "ninja://third_party/crashpad/crashpad/test/ios:ios_crash_xcuitests_module/", "variant_id": "iPhone 14 17.5"}, {"args": ["--platform", "iPhone 15", "--version", "18.5", "--xcodebuild-sim-runner", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ios_crash_xcuitests_module iPhone 15 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "ios_crash_xcuitests_module", "test_id_prefix": "ninja://third_party/crashpad/crashpad/test/ios:ios_crash_xcuitests_module/", "variant_id": "iPhone 15 18.5"}, {"args": ["--platform", "iPhone 14", "--version", "17.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ios_net_unittests iPhone 14 17.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_17_5", "path": "Runtime-ios-17.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>", "shards": 3}, "test": "ios_net_unittests", "test_id_prefix": "ninja://ios/net:ios_net_unittests/", "variant_id": "iPhone 14 17.5"}, {"args": ["--platform", "iPhone 15", "--version", "18.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ios_net_unittests iPhone 15 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>", "shards": 3}, "test": "ios_net_unittests", "test_id_prefix": "ninja://ios/net:ios_net_unittests/", "variant_id": "iPhone 15 18.5"}, {"args": ["--platform", "iPhone 14", "--version", "17.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ios_testing_unittests iPhone 14 17.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_17_5", "path": "Runtime-ios-17.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "ios_testing_unittests", "test_id_prefix": "ninja://ios/testing:ios_testing_unittests/", "variant_id": "iPhone 14 17.5"}, {"args": ["--platform", "iPhone 15", "--version", "18.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ios_testing_unittests iPhone 15 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "ios_testing_unittests", "test_id_prefix": "ninja://ios/testing:ios_testing_unittests/", "variant_id": "iPhone 15 18.5"}, {"args": ["--platform", "iPad Air 11-inch (M2)", "--version", "18.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ios_web_inttests iPad Air (6th generation) 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "ios_web_inttests", "test_id_prefix": "ninja://ios/web:ios_web_inttests/", "variant_id": "iPad Air (6th generation) 18.5"}, {"args": ["--platform", "iPhone 15", "--version", "18.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ios_web_inttests iPhone 15 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "ios_web_inttests", "test_id_prefix": "ninja://ios/web:ios_web_inttests/", "variant_id": "iPhone 15 18.5"}, {"args": ["--platform", "iPhone SE (3rd generation)", "--version", "18.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ios_web_inttests iPhone SE (3rd generation) 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "ios_web_inttests", "test_id_prefix": "ninja://ios/web:ios_web_inttests/", "variant_id": "iPhone SE (3rd generation) 18.5"}, {"args": ["--platform", "iPad Air 11-inch (M2)", "--version", "18.5", "--xcodebuild-sim-runner", "--record-video", "failed_only", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ios_web_shell_eg2tests_module iPad Air (6th generation) 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "ios_web_shell_eg2tests_module", "test_id_prefix": "ninja://ios/web/shell/test:ios_web_shell_eg2tests_module/", "variant_id": "iPad Air (6th generation) 18.5"}, {"args": ["--platform", "iPhone 14", "--version", "17.5", "--xcodebuild-sim-runner", "--record-video", "failed_only", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ios_web_shell_eg2tests_module iPhone 14 17.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_17_5", "path": "Runtime-ios-17.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "ios_web_shell_eg2tests_module", "test_id_prefix": "ninja://ios/web/shell/test:ios_web_shell_eg2tests_module/", "variant_id": "iPhone 14 17.5"}, {"args": ["--platform", "iPhone 15", "--version", "18.5", "--xcodebuild-sim-runner", "--record-video", "failed_only", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ios_web_shell_eg2tests_module iPhone 15 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "ios_web_shell_eg2tests_module", "test_id_prefix": "ninja://ios/web/shell/test:ios_web_shell_eg2tests_module/", "variant_id": "iPhone 15 18.5"}, {"args": ["--platform", "iPad Air 11-inch (M2)", "--version", "18.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ios_web_unittests iPad Air (6th generation) 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "ios_web_unittests", "test_id_prefix": "ninja://ios/web:ios_web_unittests/", "variant_id": "iPad Air (6th generation) 18.5"}, {"args": ["--platform", "iPhone 15", "--version", "18.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ios_web_unittests iPhone 15 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "ios_web_unittests", "test_id_prefix": "ninja://ios/web:ios_web_unittests/", "variant_id": "iPhone 15 18.5"}, {"args": ["--platform", "iPhone SE (3rd generation)", "--version", "18.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ios_web_unittests iPhone SE (3rd generation) 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "ios_web_unittests", "test_id_prefix": "ninja://ios/web:ios_web_unittests/", "variant_id": "iPhone SE (3rd generation) 18.5"}, {"args": ["--platform", "iPad Air 11-inch (M2)", "--version", "18.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ios_web_view_inttests iPad Air (6th generation) 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "ios_web_view_inttests", "test_id_prefix": "ninja://ios/web_view:ios_web_view_inttests/", "variant_id": "iPad Air (6th generation) 18.5"}, {"args": ["--platform", "iPhone 15", "--version", "18.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ios_web_view_inttests iPhone 15 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "ios_web_view_inttests", "test_id_prefix": "ninja://ios/web_view:ios_web_view_inttests/", "variant_id": "iPhone 15 18.5"}, {"args": ["--platform", "iPhone SE (3rd generation)", "--version", "18.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ios_web_view_inttests iPhone SE (3rd generation) 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "ios_web_view_inttests", "test_id_prefix": "ninja://ios/web_view:ios_web_view_inttests/", "variant_id": "iPhone SE (3rd generation) 18.5"}, {"args": ["--platform", "iPad Air 11-inch (M2)", "--version", "18.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ios_web_view_unittests iPad Air (6th generation) 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "ios_web_view_unittests", "test_id_prefix": "ninja://ios/web_view:ios_web_view_unittests/", "variant_id": "iPad Air (6th generation) 18.5"}, {"args": ["--platform", "iPhone 15", "--version", "18.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ios_web_view_unittests iPhone 15 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "ios_web_view_unittests", "test_id_prefix": "ninja://ios/web_view:ios_web_view_unittests/", "variant_id": "iPhone 15 18.5"}, {"args": ["--platform", "iPhone SE (3rd generation)", "--version", "18.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ios_web_view_unittests iPhone SE (3rd generation) 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "ios_web_view_unittests", "test_id_prefix": "ninja://ios/web_view:ios_web_view_unittests/", "variant_id": "iPhone SE (3rd generation) 18.5"}, {"args": ["--platform", "iPhone 14", "--version", "17.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "net_unittests iPhone 14 17.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_17_5", "path": "Runtime-ios-17.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "net_unittests", "test_id_prefix": "ninja://net:net_unittests/", "variant_id": "iPhone 14 17.5"}, {"args": ["--platform", "iPhone 15", "--version", "18.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "net_unittests iPhone 15 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "net_unittests", "test_id_prefix": "ninja://net:net_unittests/", "variant_id": "iPhone 15 18.5"}, {"args": ["--platform", "iPhone 14", "--version", "17.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "services_unittests iPhone 14 17.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_17_5", "path": "Runtime-ios-17.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "services_unittests", "test_id_prefix": "ninja://services:services_unittests/", "variant_id": "iPhone 14 17.5"}, {"args": ["--platform", "iPhone 15", "--version", "18.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "services_unittests iPhone 15 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "services_unittests", "test_id_prefix": "ninja://services:services_unittests/", "variant_id": "iPhone 15 18.5"}, {"args": ["--platform", "iPad Air 11-inch (M2)", "--version", "18.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "skia_unittests iPad Air (6th generation) 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "skia_unittests", "test_id_prefix": "ninja://skia:skia_unittests/", "variant_id": "iPad Air (6th generation) 18.5"}, {"args": ["--platform", "iPhone 15", "--version", "18.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "skia_unittests iPhone 15 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "skia_unittests", "test_id_prefix": "ninja://skia:skia_unittests/", "variant_id": "iPhone 15 18.5"}, {"args": ["--platform", "iPhone SE (3rd generation)", "--version", "18.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "skia_unittests iPhone SE (3rd generation) 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "skia_unittests", "test_id_prefix": "ninja://skia:skia_unittests/", "variant_id": "iPhone SE (3rd generation) 18.5"}, {"args": ["--platform", "iPhone 14", "--version", "17.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "sql_unittests iPhone 14 17.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_17_5", "path": "Runtime-ios-17.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "sql_unittests", "test_id_prefix": "ninja://sql:sql_unittests/", "variant_id": "iPhone 14 17.5"}, {"args": ["--platform", "iPhone 15", "--version", "18.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "sql_unittests iPhone 15 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "sql_unittests", "test_id_prefix": "ninja://sql:sql_unittests/", "variant_id": "iPhone 15 18.5"}, {"args": ["--platform", "iPad Air 11-inch (M2)", "--version", "18.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ui_base_unittests iPad Air (6th generation) 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "ui_base_unittests", "test_id_prefix": "ninja://ui/base:ui_base_unittests/", "variant_id": "iPad Air (6th generation) 18.5"}, {"args": ["--platform", "iPhone 15", "--version", "18.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ui_base_unittests iPhone 15 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "ui_base_unittests", "test_id_prefix": "ninja://ui/base:ui_base_unittests/", "variant_id": "iPhone 15 18.5"}, {"args": ["--platform", "iPhone SE (3rd generation)", "--version", "18.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "ui_base_unittests iPhone SE (3rd generation) 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "ui_base_unittests", "test_id_prefix": "ninja://ui/base:ui_base_unittests/", "variant_id": "iPhone SE (3rd generation) 18.5"}, {"args": ["--platform", "iPhone 14", "--version", "17.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "url_unittests iPhone 14 17.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_17_5", "path": "Runtime-ios-17.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "url_unittests", "test_id_prefix": "ninja://url:url_unittests/", "variant_id": "iPhone 14 17.5"}, {"args": ["--platform", "iPhone 15", "--version", "18.5", "--out-dir", "${ISOLATED_OUTDIR}", "--xcode-build-version", "16f6", "--xctest"], "merge": {"script": "//testing/merge_scripts/standard_isolated_script_merge.py"}, "name": "url_unittests iPhone 15 18.5", "resultdb": {"enable": true, "has_native_resultdb_integration": true}, "swarming": {"cipd_packages": [{"cipd_package": "infra/tools/mac_toolchain/${platform}", "location": ".", "revision": "git_revision:a18b7d95d26f3c6bf9591978b19cf0ca8268ac7d"}], "dimensions": {"cpu": "arm64", "os": "Mac-15"}, "named_caches": [{"name": "runtime_ios_18_5", "path": "Runtime-ios-18.5"}, {"name": "xcode_ios_16f6", "path": "Xcode.app"}], "service_account": "<EMAIL>"}, "test": "url_unittests", "test_id_prefix": "ninja://url:url_unittests/", "variant_id": "iPhone 15 18.5"}]}}