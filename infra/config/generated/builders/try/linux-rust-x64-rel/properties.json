{"$build/chromium_tests_builder_config": {"builder_config": {"additional_exclusions": ["infra/config/generated/builders/try/linux-rust-x64-rel/gn-args.json"], "builder_db": {"entries": [{"builder_id": {"bucket": "ci", "builder": "linux-rust-x64-rel", "project": "chromium"}, "builder_spec": {"builder_group": "chromium.rust", "execution_mode": "COMPILE_AND_TEST", "legacy_chromium_config": {"apply_configs": ["mb"], "build_config": "Release", "config": "chromium", "target_bits": 64, "target_platform": "linux"}, "legacy_gclient_config": {"config": "chromium"}}}]}, "builder_ids": [{"bucket": "ci", "builder": "linux-rust-x64-rel", "project": "chromium"}], "targets_spec_directory": "src/infra/config/generated/builders/try/linux-rust-x64-rel/targets"}}, "$build/siso": {"configs": ["builder"], "enable_cloud_monitoring": true, "enable_cloud_profiler": true, "enable_cloud_trace": true, "experiments": [], "metrics_project": "chromium-reclient-metrics", "project": "rbe-chromium-untrusted", "remote_jobs": 150}, "$recipe_engine/resultdb/test_presentation": {"column_keys": [], "grouping_keys": ["status", "v.test_suite"]}, "builder_group": "tryserver.chromium.rust", "recipe": "chromium_trybot"}