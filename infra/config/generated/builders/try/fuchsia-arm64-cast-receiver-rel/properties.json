{"$build/chromium_tests_builder_config": {"builder_config": {"additional_exclusions": ["infra/config/generated/builders/try/fuchsia-arm64-cast-receiver-rel/gn-args.json"], "builder_db": {"entries": [{"builder_id": {"bucket": "ci", "builder": "fuchsia-arm64-cast-receiver-rel", "project": "chromium"}, "builder_spec": {"build_gs_bucket": "chromium-linux-archive", "builder_group": "chromium.fuchsia", "execution_mode": "COMPILE_AND_TEST", "legacy_chromium_config": {"apply_configs": ["mb"], "build_config": "Release", "config": "chromium", "target_arch": "arm", "target_bits": 64, "target_platform": "fuchsia"}, "legacy_gclient_config": {"apply_configs": ["fuchsia_arm64", "fuchsia_arm64_host"], "config": "chromium"}}}]}, "builder_ids": [{"bucket": "ci", "builder": "fuchsia-arm64-cast-receiver-rel", "project": "chromium"}], "targets_spec_directory": "src/infra/config/generated/builders/try/fuchsia-arm64-cast-receiver-rel/targets"}}, "$build/flakiness": {"check_for_flakiness": true, "check_for_flakiness_with_resultdb": true}, "$build/siso": {"configs": ["builder", "remote-link"], "enable_cloud_monitoring": true, "enable_cloud_profiler": true, "enable_cloud_trace": true, "experiments": [], "metrics_project": "chromium-reclient-metrics", "output_local_strategy": "greedy", "project": "rbe-chromium-untrusted", "remote_jobs": -1}, "$recipe_engine/resultdb/test_presentation": {"column_keys": [], "grouping_keys": ["status", "v.test_suite"]}, "builder_group": "tryserver.chromium.fuchsia", "cq": "path-based", "recipe": "chromium_trybot"}