{"$build/chromium_tests_builder_config": {"builder_config": {"additional_exclusions": ["infra/config/generated/builders/try/win_optional_gpu_tests_rel/gn-args.json"], "builder_db": {"entries": [{"builder_id": {"bucket": "try", "builder": "win_optional_gpu_tests_rel", "project": "chromium"}, "builder_spec": {"build_gs_bucket": "chromium-gpu-fyi-archive", "builder_group": "tryserver.chromium.win", "execution_mode": "COMPILE_AND_TEST", "legacy_chromium_config": {"apply_configs": ["mb"], "build_config": "Release", "config": "chromium", "target_bits": 64, "target_platform": "win"}, "legacy_gclient_config": {"config": "chromium"}}}]}, "builder_ids": [{"bucket": "try", "builder": "win_optional_gpu_tests_rel", "project": "chromium"}], "retry_failed_shards": false, "targets_spec_directory": "src/infra/config/generated/builders/try/win_optional_gpu_tests_rel/targets"}}, "$build/flakiness": {"check_for_flakiness": true, "check_for_flakiness_with_resultdb": true}, "$build/siso": {"configs": ["builder", "remote-link"], "enable_cloud_monitoring": true, "enable_cloud_profiler": true, "enable_cloud_trace": true, "experiments": [], "metrics_project": "chromium-reclient-metrics", "output_local_strategy": "greedy", "project": "rbe-chromium-untrusted", "remote_jobs": -1}, "$recipe_engine/resultdb/test_presentation": {"column_keys": [], "grouping_keys": ["status", "v.test_suite"]}, "builder_group": "tryserver.chromium.win", "cq": "path-based", "recipe": "chromium_trybot"}