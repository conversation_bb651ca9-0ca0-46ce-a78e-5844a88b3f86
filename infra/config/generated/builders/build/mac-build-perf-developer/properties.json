{"$build/chromium_tests_builder_config": {"builder_config": {"additional_exclusions": ["infra/config/generated/builders/build/mac-build-perf-developer/gn-args.json"], "builder_db": {"entries": [{"builder_id": {"bucket": "build", "builder": "mac-build-perf-developer", "project": "chromium"}, "builder_spec": {"builder_group": "chromium.build", "execution_mode": "COMPILE_AND_TEST", "legacy_chromium_config": {"apply_configs": ["mb"], "config": "chromium", "target_platform": "mac"}, "legacy_gclient_config": {"apply_configs": ["siso_latest"], "config": "chromium"}}}]}, "builder_ids": [{"bucket": "build", "builder": "mac-build-perf-developer", "project": "chromium"}]}}, "$build/reclient": {"instance": "rbe-chromium-untrusted", "jobs": 640, "metrics_project": "chromium-reclient-metrics", "scandeps_server": true}, "$build/siso": {"configs": [], "disable_batch_mode": true, "enable_cloud_profiler": true, "enable_cloud_trace": true, "experiments": ["no-fallback"], "project": "rbe-chromium-untrusted", "remote_jobs": 5120}, "$recipe_engine/resultdb/test_presentation": {"column_keys": [], "grouping_keys": ["status", "v.test_suite"]}, "builder_group": "chromium.build", "recipe": "chrome_build/build_perf_developer"}