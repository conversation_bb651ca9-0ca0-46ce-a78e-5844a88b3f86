set noparent
file://infra/config/groups/sheriff-rotations/CHROMIUM_OWNERS

per-file android.txt=file://infra/config/groups/sheriff-rotations/ANDROID_OWNERS
per-file angle.txt=file://infra/config/groups/sheriff-rotations/ANGLE_OWNERS
per-file cft.txt=file://infra/config/groups/sheriff-rotations/CFT_OWNERS
per-file chromium.clang.txt=file://infra/config/groups/sheriff-rotations/CHROMIUM_CLANG_OWNERS
per-file chromium.gpu.txt=file://infra/config/groups/sheriff-rotations/CHROMIUM_GPU_OWNERS
per-file chromium.txt=file://infra/config/groups/sheriff-rotations/CHROMIUM_OWNERS
per-file chromiumos.txt=file://infra/config/groups/sheriff-rotations/CHROMIUMOS_OWNERS
per-file dawn.txt=file://infra/config/groups/sheriff-rotations/DAWN_OWNERS
per-file fuchsia.txt=file://infra/config/groups/sheriff-rotations/FUCHSIA_OWNERS
per-file cronet.txt=file://infra/config/groups/sheriff-rotations/CRONET_OWNERS

# CBRI Branch Day Automation go/automate-branch-day
# The chrome-branch-day service account needs to modify files in this directory
# as part of branch day automation despite the file having set noparent.
<EMAIL>
