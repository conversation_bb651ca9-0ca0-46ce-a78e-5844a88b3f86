// Copyright 2015 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// Use the <code>chrome.languageSettingsPrivate</code> API to get or change
// language and input method settings.
namespace languageSettingsPrivate {
  enum MoveType {
    TOP,
    UP,
    DOWN,
    UNKNOWN
  };

  dictionary Language {
    // The unique code identifying the language.
    DOMString code;

    // The name of the language, in the current UI language.
    DOMString displayName;

    // The name of the language as it is in its own language.
    DOMString nativeDisplayName;

    // Whether the UI can be displayed in this language. Defaults to false.
    boolean? supportsUI;

    // Whether this language can be used for spell checking. Defaults to false.
    boolean? supportsSpellcheck;

    // Whether this language has translations for the current target language.
    // Defaults to false.
    boolean? supportsTranslate;

    // Whether this language is prohibited as a UI locale (not in the list of
    // the 'AllowedLanguages' policy). Defaults to false.
    boolean? isProhibitedLanguage;
  };

  dictionary SpellcheckDictionaryStatus {
    // The language code of the dictionary that the status describes.
    DOMString languageCode;

    // Whether the dictionary is ready (has been loaded from disk or
    // successfully downloaded).
    boolean isReady;

    // Whether the dictionary is being downloaded. Defaults to false.
    boolean? isDownloading;

    // Whether the dictionary download failed. Defaults to false.
    boolean? downloadFailed;
  };

  dictionary InputMethod {
    // The ID of the input method descriptor.
    DOMString id;

    // The human-readable name of the input method.
    DOMString displayName;

    // The language codes this input method supports.
    DOMString[] languageCodes;

    // The search terms for the input method.
    DOMString[] tags;

    // True if the input method is enabled.
    boolean? enabled;

    // True if the input method extension has an options page.
    boolean? hasOptionsPage;

    // True if the input method is not allowed by policy.
    boolean? isProhibitedByPolicy;
  };

  dictionary InputMethodLists {
    // The list of component extension input methods.
    InputMethod[] componentExtensionImes;

    // The list of third-party extension input methods.
    InputMethod[] thirdPartyExtensionImes;
  };

  callback GetLanguageListCallback = void (Language[] languages);
  callback GetAlwaysTranslateLanguagesCallback =
      void (DOMString[] languageCodes);
  callback GetNeverTranslateLanguagesCallback =
      void (DOMString[] languageCodes);
  callback GetInputMethodListsCallback = void (InputMethodLists lists);
  callback GetSpellcheckWordsCallback = void (DOMString[] words);
  callback GetTranslateTargetLanguageCallback = void (DOMString languageCode);
  callback GetSpellcheckDictionaryStatusesCallback =
      void (SpellcheckDictionaryStatus[] status);

  interface Functions {
    // Gets languages available for translate, spell checking, input and locale.
    static void getLanguageList(
        GetLanguageListCallback callback);

    // Enables a language, adding it to the Accept-Language list (used to decide
    // which languages to translate, generate the Accept-Language header, etc.).
    static void enableLanguage(DOMString languageCode);

    // Disables a language, removing it from the Accept-Language list.
    static void disableLanguage(DOMString languageCode);

    // Enables or disables translation for a given language.
    static void setEnableTranslationForLanguage(
        DOMString languageCode, boolean enable);

    // Moves a language inside the language list.
    static void moveLanguage(DOMString languageCode, MoveType moveType);

    // Gets languages that should always be automatically translated.
    static void getAlwaysTranslateLanguages(
        GetAlwaysTranslateLanguagesCallback callback);

    // Sets whether a given language should always be automatically translated.
    static void setLanguageAlwaysTranslateState(
        DOMString languageCode, boolean alwaysTranslate);

    // Gets languages that should never be offered to translate.
    static void getNeverTranslateLanguages(
        GetNeverTranslateLanguagesCallback callback);

    // Gets the current status of the chosen spell check dictionaries.
    static void getSpellcheckDictionaryStatuses(
        GetSpellcheckDictionaryStatusesCallback callback);

    // Gets the custom spell check words, in sorted order.
    static void getSpellcheckWords(
        GetSpellcheckWordsCallback callback);

    // Adds a word to the custom dictionary.
    static void addSpellcheckWord(DOMString word);

    // Removes a word from the custom dictionary.
    static void removeSpellcheckWord(DOMString word);

    // Gets the translate target language (in most cases, the display locale).
    static void getTranslateTargetLanguage(
        GetTranslateTargetLanguageCallback callback);

    // Sets the translate target language given a language code.
    static void setTranslateTargetLanguage(DOMString languageCode);

    // Gets all supported input methods, including third-party IMEs.
    // Chrome OS only.
    static void getInputMethodLists(
        GetInputMethodListsCallback callback);

    // Adds the input method to the current user's list of enabled input
    // methods, enabling the input method for the current user. Chrome OS only.
    static void addInputMethod(DOMString inputMethodId);

    // Removes the input method from the current user's list of enabled input
    // methods, disabling the input method for the current user. Chrome OS only.
    static void removeInputMethod(DOMString inputMethodId);

    // Tries to download the dictionary after a failed download.
    static void retryDownloadDictionary(DOMString languageCode);
  };

  interface Events {
    // Called when the pref for the dictionaries used for spell checking changes
    // or the status of one of the spell check dictionaries changes.
    static void onSpellcheckDictionariesChanged(
        SpellcheckDictionaryStatus[] statuses);

    // Called when words are added to and/or removed from the custom spell check
    // dictionary.
    static void onCustomDictionaryChanged(
        DOMString[] wordsAdded, DOMString[] wordsRemoved);

    // Called when an input method is added.
    static void onInputMethodAdded(DOMString inputMethodId);

    // Called when an input method is removed.
    static void onInputMethodRemoved(DOMString inputMethodId);
  };
};
