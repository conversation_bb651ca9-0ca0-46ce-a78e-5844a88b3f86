// Copyright 2012 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

[
  {
    "namespace": "inputMethodPrivate",
    "compiler_options": {
      "implemented_in": "chrome/browser/ash/extensions/input_method_api.h"
    },
    "description": "none",
    "types": [
      {
        "id": "MenuItemStyle",
        "type": "string",
        "description": "The type of menu item. Radio buttons between separators are considered grouped.",
        "enum": ["check", "radio", "separator"]
      },
      {
        "id": "MenuItem",
        "type": "object",
        "description": "A menu item used by an input method to interact with the user from the language menu.",
        "properties": {
          "id": {"type": "string", "description": "String that will be passed to callbacks referencing this MenuItem."},
          "label": {"type": "string", "optional": true, "description": "Text displayed in the menu for this item."},
          "style": {
            "$ref": "MenuItemStyle",
            "optional": true,
            "description": "The type of menu item."
          },
          "visible": {"type": "boolean", "optional": true, "description": "Indicates this item is visible."},
          "checked": {"type": "boolean", "optional": true, "description": "Indicates this item should be drawn with a check."},
          "enabled": {"type": "boolean", "optional": true, "description": "Indicates this item is enabled."}
        }
      },
      {
        "id": "UnderlineStyle",
        "type": "string",
        "description": "The type of the underline to modify a composition segment.",
        "enum": ["underline", "doubleUnderline", "noUnderline"]
      },
      {
        "id": "FocusReason",
        "type": "string",
        "description": "Describes how the text field was focused",
        "enum": ["mouse", "touch", "pen", "other"]
      },
      {
        "id": "InputModeType",
        "type": "string",
        "description": "Type of keyboard to show for this text field, (Text, Number, URL, etc) set by mode property of input tag",
        "enum": ["noKeyboard", "text", "tel", "url", "email", "numeric", "decimal", "search"]
      },
      {
        "id": "InputContextType",
        "type": "string",
        "description": "Type of value this text field edits, (Text, Number, URL, etc)",
        "enum": ["text", "search", "tel", "url", "email", "number", "password", "null"]
      },
      {
        "id": "AutoCapitalizeType",
        "type": "string",
        "description": "The auto-capitalize type of the text field.",
        "enum": ["off", "characters", "words", "sentences"]
      },
      {
        "id": "LanguagePackStatus",
        "type": "string",
        "description": "The aggregated status of all language packs for a given input method.",
        "enum": [
          // This should only be used when we have uninitialised data.
          "unknown",
          "notInstalled",
          "inProgress",
          // If there are no language packs associated with an input method,
          // such as for input methods which aren't Google 1P, installed is
          // returned.
          "installed",
          "errorOther",
          "errorNeedsReboot"
        ]
      },
      {
        "id": "LanguagePackStatusChange",
        "type": "object",
        "description": "Object returned by callbacks when the status of language packs change.",
        "properties": {
          "engineIds": {
            "type": "array",
            "items": {
              "type": "string"
            },
            "description": "All engine IDs for which this language pack status change applies to."
          },
          "status": {
            "$ref": "LanguagePackStatus",
            "description": "The new language pack status."
          }
        }
      },
      {
        "id": "InputContext",
        "type": "object",
        "description": "Describes an input Context",
        "properties": {
          "contextID": {"type": "integer", "description": "This is used to specify targets of text field operations.  This ID becomes invalid as soon as onBlur is called."},
          "type": {"$ref": "InputContextType", "description": "Type of value this text field edits, (Text, Number, URL, etc)"},
          "mode": {"$ref": "InputModeType", "description": "Type of keyboard to show for this field (Text, Number, URL, etc)"},
          "autoCorrect": {"type": "boolean", "description": "Whether the text field wants auto-correct."},
          "autoComplete": {"type": "boolean", "description": "Whether the text field wants auto-complete."},
          "autoCapitalize": {"$ref": "AutoCapitalizeType", "description": "The auto-capitalize type of the text field."},
          "spellCheck": {"type": "boolean", "description": "Whether the text field wants spell-check."},
          "shouldDoLearning": {"type": "boolean", "description": "Whether text entered into the text field should be used to improve typing suggestions for the user."},
          "focusReason": {"$ref": "FocusReason", "description": "How the text field was focused"},
          "appKey": {"type": "string", "optional": true, "description": "Key of the app associated with this text field if any."}
        }
      },
      {
        "id": "InputMethodSettings",
        "type": "object",
        "description": "User preference settings for a specific input method. Japanese input methods are not included because they are managed separately by Mozc module.",
        "properties": {
          "enableCompletion": { "type": "boolean", "optional": true, "description": "Whether to enable auto completion."},
          "enableDoubleSpacePeriod": { "type": "boolean", "optional": true, "description": "Whether to auto transform double spaces to type period."},
          "enableGestureTyping": { "type": "boolean", "optional": true, "description": "Whether to enable gesture typing."},
          "enablePrediction": { "type": "boolean", "optional": true, "description": "Whether to enable word prediction."},
          "enableSoundOnKeypress": { "type": "boolean", "optional": true, "description": "Whether to enable sound on keypress."},
          "physicalKeyboardAutoCorrectionEnabledByDefault": {"type": "boolean", "optional": true, "description": "Whether auto correction should be enabled for physical keyboard by default."},
          "physicalKeyboardAutoCorrectionLevel": { "type": "integer", "optional": true, "description": "The level of auto correction for physical keyboard (0: Off, 1: Modest, 2: Aggressive)."},
          "physicalKeyboardEnableCapitalization": { "type": "boolean", "optional": true, "description": "Whether to enable auto capitalization for physical keyboard."},
          "physicalKeyboardEnableDiacriticsOnLongpress": { "type": "boolean", "optional": true, "description": "Whether to enable diacritics on longpress for physical keyboard."},
          "physicalKeyboardEnablePredictiveWriting" : {"type": "boolean", "optional": true, "description": "Whether to enable physical keyboard predictive writing"},
          "virtualKeyboardAutoCorrectionLevel": { "type": "integer", "optional": true, "description": "The level of auto correction for virtual keyboard (0: Off, 1: Modest, 2: Aggressive)."},
          "virtualKeyboardEnableCapitalization": { "type": "boolean", "optional": true, "description": "Whether enable auto capitalization for virtual keyboard."},
          "xkbLayout": { "type": "string", "optional": true, "description": "The xkb keyboard (system provided keyboard) layout."},
          "koreanEnableSyllableInput": { "type": "boolean", "optional": true, "description": "Whether input one syllable at a time in korean input method."},
          "koreanKeyboardLayout": { "type": "string", "optional": true, "description": "The layout of korean keyboard."},
          "koreanShowHangulCandidate": { "type": "boolean", "optional": true, "description": "Whether to show hangul candidates in korean input method."},
          "pinyinChinesePunctuation": { "type": "boolean", "optional": true, "description": "Whether to use Chinese punctuations in pinyin."},
          "pinyinDefaultChinese": {
            "type": "boolean",
            "optional": true,
            "description": "User can use shortcuts to switch between Chinese and English quickly when using pinyin, this flag indicates whether the default language is Chinese."
          },
          "pinyinEnableFuzzy": { "type": "boolean", "optional": true, "description": "Whether to enable fuzzy pinyin."},
          "pinyinEnableLowerPaging": { "type": "boolean", "optional": true, "description": "Whether to enable using ','/'.' to page up/down the candidates in pinyin."},
          "pinyinEnableUpperPaging": { "type": "boolean", "optional": true, "description": "Whether to enable using '-'/'=' to page up/down the candidates in pinyin."},
          "pinyinFullWidthCharacter": { "type": "boolean", "optional": true, "description": "Whether to output full width letters and digits in pinyin."},
          "pinyinFuzzyConfig": {
            "type": "object",
            "optional": true,
            "description": "The configuration of which fuzzy pairs are enable.",
            "properties": {
              "an_ang": {"type": "boolean", "optional": true, "description": "Whether to enable an_ang fuzzy"},
              "c_ch": {"type": "boolean", "optional": true, "description": "Whether to enable c_ch fuzzy"},
              "en_eng": {"type": "boolean", "optional": true, "description": "Whether to enable en_eng fuzzy"},
              "f_h": {"type": "boolean", "optional": true, "description": "Whether to enable f_h fuzzy"},
              "ian_iang": {"type": "boolean", "optional": true, "description": "Whether to enable ian_iang fuzzy"},
              "in_ing": {"type": "boolean", "optional": true, "description": "Whether to enable in_ing fuzzy"},
              "k_g": {"type": "boolean", "optional": true, "description": "Whether to enable k_g fuzzy"},
              "l_n": {"type": "boolean", "optional": true, "description": "Whether to enable l_n fuzzy"},
              "r_l": {"type": "boolean", "optional": true, "description": "Whether to enable r_l fuzzy"},
              "s_sh": {"type": "boolean", "optional": true, "description": "Whether to enable s_sh fuzzy"},
              "uan_uang": {"type": "boolean", "optional": true, "description": "Whether to enable uan_uang fuzzy"},
              "z_zh": {"type": "boolean", "optional": true, "description": "Whether to enable z_zh fuzzy"}
            }
          },
          "zhuyinKeyboardLayout": { "type": "string", "optional": true, "description": "The layout of zhuyin keyboard."},
          "zhuyinPageSize": { "type": "integer", "optional": true, "description": "The page size of zhuyin candidate page."},
          "zhuyinSelectKeys": { "type": "string", "optional": true, "description": "The keys used to select candidates in zhuyin."},
          "vietnameseVniAllowFlexibleDiacritics": { "type": "boolean", "optional": true, "description": "Enable VNI flexible Vietnamese typing mode"},
          "vietnameseVniNewStyleToneMarkPlacement": { "type": "boolean", "optional": true, "description": "Enable VNI modern tone mark placement"},
          "vietnameseVniInsertDoubleHornOnUo": { "type": "boolean", "optional": true, "description": "Enable VNI insert-double-horn-on-UO shortcut"},
          "vietnameseVniShowUnderline": { "type": "boolean", "optional": true, "description": "Enable VNI showing underline on composition text"},
          "vietnameseTelexAllowFlexibleDiacritics": { "type": "boolean", "optional": true, "description": "Enable Telex flexible Vietnamese typing mode"},
          "vietnameseTelexNewStyleToneMarkPlacement": { "type": "boolean", "optional": true, "description": "Enable Telex modern tone mark placement"},
          "vietnameseTelexInsertDoubleHornOnUo": { "type": "boolean", "optional": true, "description": "Enable Telex insert-double-horn-on-UO horn shortcut"},
          "vietnameseTelexInsertUHornOnW": { "type": "boolean", "optional": true, "description": "Enable Telex inser-U-Horn-on-W shortcut"},
          "vietnameseTelexShowUnderline": { "type": "boolean", "optional": true, "description": "Enable Telex showing underline on composition text"}
        }
      }
    ],
    "functions": [
      {
        "name": "getInputMethodConfig",
        "type": "function",
        "description": "Gets configurations for input methods.",
        "parameters": [],
        "returns_async": {
          "name": "callback",
          "description": "Callback which is called with the config object.",
          "parameters": [
            {
              "name": "config",
              "type": "object",
              "description": "The input method config object.",
              "properties": {
                "isPhysicalKeyboardAutocorrectEnabled": {"type": "boolean"},
                "isImeMenuActivated": {"type": "boolean"}
              }
            }
          ]
        }
      }, {
        "name": "getInputMethods",
        "type": "function",
        "description": "Gets all enabled input methods, sorted in ascending order of their localized full display names, according to the lexicographical order defined by the current system locale aka. display language.",
        "parameters": [],
        "returns_async": {
          "name": "callback",
          "description": "Callback which is called with the input method objects.",
          "parameters": [
            {
              "name": "inputMethods",
              "type": "array",
              "description": "Enabled input method objects, sorted in ascending order of their localized full display names, according to the lexicographical order defined by the current system locale aka. display language.",
              "items": {
                "type": "object",
                "properties": {
                  "id": {"type": "string"},
                  "name": {"type": "string"},
                  "indicator": {"type": "string"}
                }
              }
            }
          ]
        }
      }, {
        "name": "getCurrentInputMethod",
        "type": "function",
        "description": "Gets the current input method.",
        "parameters": [],
        "returns_async": {
          "name": "callback",
          "description": "Callback which is called with the current input method.",
          "parameters": [
            {
              "name": "inputMethodId",
              "type": "string",
              "description": "Current input method."
            }
          ]
        }
      }, {
        "name": "setCurrentInputMethod",
        "type": "function",
        "description": "Sets the current input method.",
        "parameters": [
          {
            "name": "inputMethodId",
            "type": "string",
            "description": "The input method ID to be set as current input method."
          }
        ],
        "returns_async": {
          "name": "callback",
          "optional": true,
          "description": "Callback which is called once the current input method is set. If unsuccessful $(ref:runtime.lastError) is set.",
          "parameters": []
        }
      }, {
          "name": "switchToLastUsedInputMethod",
          "type": "function",
          "description": "Switches to the last used input method. If no last used input method, this is a no-op.",
          "parameters": [],
          "returns_async": {
            "name": "callback",
            "optional": true,
            "description": "Callback which is called once the input method is swapped (if applicable). If unsuccessful $(ref:runtime.lastError) is set.",
            "parameters": []
          }
      }, {
        "name": "fetchAllDictionaryWords",
        "type": "function",
        "description": "Fetches a list of all the words currently in the dictionary.",
        "parameters": [],
        "returns_async": {
          "name": "callback",
          "description": "Callback which is called once the list of dictionary words are ready.",
          "parameters": [
            {
              "name": "words",
              "type": "array",
              "description": "List of dictionary words.",
              "items": {
                "type": "string"
              }
            }
          ]
        }
      }, {
        "name": "addWordToDictionary",
        "type": "function",
        "description": "Adds a single word to be stored in the dictionary.",
        "parameters": [
          {
            "name": "word",
            "type": "string",
            "description": "A new word to add to the dictionary."
          }
        ],
        "returns_async": {
          "name": "callback",
          "optional": true,
          "description": "Callback which is called once the word is added. If unsuccessful $(ref:runtime.lastError) is set.",
          "parameters": []
        }
      }, {
        "name": "setXkbLayout",
        "type": "function",
        "description": "Sets the XKB layout for the given input method.",
        "parameters": [
          {
            "name": "xkb_name",
            "type": "string",
            "description": "The XKB layout name."
          }
        ],
        "returns_async": {
          "name": "callback",
          "optional": true,
          "description": "Callback which is called when the layout is set.",
          "parameters": []
        }
      }, {
        "name": "finishComposingText",
        "type": "function",
        "description": "Commits the text currently being composed without moving the selected text range. This is a no-op if the context is incorrect.",
        "parameters": [
          {
            "name": "parameters",
            "type": "object",
            "properties": {
              "contextID": {
                "description": "ID of the context where we want to finish composing.",
                "type": "integer"
              }
            }
          }
        ],
        "returns_async": {
          "name": "callback",
          "optional": true,
          "description": "Called when the operation completes.",
          "parameters": []
        }
      }, {
        "name": "showInputView",
        "type": "function",
        "description": "Shows the input view window. If the input view window is already shown, this function will do nothing.",
        "parameters": [],
        "returns_async": {
          "name": "callback",
          "optional": true,
          "description": "Called when the operation completes.",
          "parameters": []
        }
      }, {
        "name": "hideInputView",
        "type": "function",
        "description": "Hides the input view window. If the input view window is already hidden, this function will do nothing.",
        "parameters": [],
        "returns_async": {
          "name": "callback",
          "optional": true,
          "description": "Called when the operation completes.",
          "parameters": []
        }
      }, {
        "name": "openOptionsPage",
        "type": "function",
        "description": "Opens the options page for the input method extension. If the input method does not have options, this function will do nothing.",
        "parameters": [
          {
            "name": "inputMethodId",
            "type": "string",
            "description": "ID of the input method to open options for."
          }
        ]
      }, {
        "name": "getSurroundingText",
        "type": "function",
        "description": "Gets the surrounding text of the current selection. WARNING: This could return a stale cache that doesn't reflect reality, due to async between IMF and TextInputClient.",
        "parameters": [
          {
            "name": "beforeLength",
            "type": "integer",
            "description": "The number of characters before the current selection."
          },
          {
            "name": "afterLength",
            "type": "integer",
            "description": "The number of characters after the current selection."
          }
        ],
        "returns_async": {
          "name": "callback",
          "description": "Callback which is called to provide the result",
          "parameters": [
            {
              "name": "surroundingInfo",
              "type": "object",
              "description": "The surrouding text info.",
              "properties": {
                "before": {"type": "string"},
                "selected": {"type": "string"},
                "after": {"type": "string"}
              }
            }
          ]
        }
      }, {
        "name": "getSettings",
        "type": "function",
        "description": "Gets the current values of all settings for a particular input method",
        "parameters": [
          {
            "name": "engineID",
            "type": "string",
            "description": "The ID of the engine (e.g. 'zh-t-i0-pinyin', 'xkb:us::eng')"
          }
        ],
        "returns_async": {
          "name": "callback",
          "description": "Callback to receive the settings",
          "parameters": [
            {
              "name": "settings",
              "$ref": "InputMethodSettings",
              "optional": true,
              "description": "The requested setting, or null if there's no value"
            }
          ]
        }
      }, {
        "name": "setSettings",
        "type": "function",
        "description": "Sets the value of all settings for a particular input method",
        "parameters": [
          {
            "name": "engineID",
            "type": "string",
            "description": "The ID of the engine (e.g. 'zh-t-i0-pinyin', 'xkb:us::eng')"
          },
          {
            "name": "settings",
            "$ref": "InputMethodSettings",
            "description": "The settings to set"
          }
        ],
        "returns_async": {
          "name": "callback",
          "optional": true,
          "description": "Callback to notify that the new value has been set",
          "parameters": []
        }
      }, {
        "name": "setCompositionRange",
        "type": "function",
        "description": "(Deprecated) Set the composition range. If this extension does not own the active IME, this fails. Use setComposingRange instead.",
        "parameters": [
          {
            "name": "parameters",
            "type": "object",
            "properties": {
              "contextID": {
                "description": "ID of the context where the composition text will be set",
                "type": "integer"
              },
              "selectionBefore": {
                "description": "How much before the current selection to set as composition.",
                "type": "integer"
              },
              "selectionAfter": {
                "description": "How much after the current selection to set as composition.",
                "type": "integer"
              },
              "segments": {
                "description": "List of segments and their associated types.",
                "type": "array",
                "optional": true,
                "items": {
                  "type": "object",
                  "properties": {
                    "start": {
                      "description": "Index of the character to start this segment at",
                      "type": "integer"
                    },
                    "end": {
                      "description": "Index of the character to end this segment after.",
                      "type": "integer"
                    },
                    "style": {
                      "$ref": "UnderlineStyle",
                      "description": "The type of the underline to modify this segment."
                    }
                  }
                }
              }
            }
          }
        ],
        "returns_async": {
          "name": "callback",
          "optional": true,
          "description": "Called when the operation completes with a boolean indicating if the text was accepted or not. On failure, $(ref:runtime.lastError) is set.",
          "parameters": [
            {
              "name": "success",
              "type": "boolean"
            }
          ]
        }
      }, {
        "name": "reset",
        "type": "function",
        "description": "Resets the current engine to its initial state. Fires an OnReset event.",
        "parameters": []
      },
      {
        "name": "onAutocorrect",
        "type": "function",
        "description": "Called after a word has been autocorrected to show some UI for autocorrect.",
        "platforms": ["chromeos"],
      "parameters": [{
            "name": "parameters",
            "type": "object",
            "properties": {
                "contextID": {
                    "description": "ID of the context where the autocorrect occurred.",
                    "type": "integer"
                },
                "typedWord": {
                    "type": "string",
                    "description": "Corrected word will be replaced by this when clicking undo"
                },
                "correctedWord": {
                    "type": "string",
                    "description": "Needed primarily to know the length of the autocorrected text to show the correct length of underline. String content technically redundant; required however, as what IMF knows may be stale due to async."
                },
                "startIndex": {
                    "type": "integer",
                    "description": "Offset index (in code units) in surroundingInfo (see onSurroundingTextChanged) for the start of the autocorrected text"
                }
            }
        }]
    },
    {
      "name" : "notifyInputMethodReadyForTesting",
      "type" : "function",
      "description" : "Notifies Chrome that the current input method is ready to accept key events from Tast.",
      "parameters": []
    },
    {
      "name": "getLanguagePackStatus",
      "type": "function",
      "description": "Gets the aggregate status of all language packs for a given input method.",
      "parameters": [
        {
          "name": "inputMethodId",
          "type": "string",
          "description": "Fully qualified ID of the input method"
        }
      ],
      "returns_async": {
        "name": "callback",
        "description": "Called with a LanguagePackStatus when the operation completes.",
        "parameters": [
          {
            "name": "status",
            "$ref": "LanguagePackStatus",
            "description": "The aggregated status of all language packs for the given input method, or 'installed' if there are no language packs."
          }
        ]
      }
    }
    ],
    "events": [
      {
        "name": "onCaretBoundsChanged",
        "type": "function",
        "description": "Fired when the caret bounds change.",
        "parameters": [
          {
            "name": "caretBounds",
            "type": "object",
            "description": "The bounds information for the caret.",
            "properties": {
              "x": {"type": "integer"},
              "y": {"type": "integer"},
              "w": {"type": "integer"},
              "h": {"type": "integer"}
            }
          }
        ]
      },
      {
        "name": "onChanged",
        "type": "function",
        "description": "Fired when the input method is changed.",
        "parameters": [
          {
            "name": "newInputMethodId",
            "type": "string",
            "description": "New input method which is being used."
          }
        ]
      }, {
        "name": "onDictionaryLoaded",
        "type": "function",
        "description": "Fired when the custom spelling dictionary is loaded.",
        "parameters": []
      }, {
        "name": "onDictionaryChanged",
        "type": "function",
        "description": "Fired when words are added or removed from the custom spelling dictionary.",
        "parameters": [
          {
            "name": "added",
            "type": "array",
            "description": "List of added words.",
            "items": {
              "type": "string"
            }
          }, {
            "name": "removed",
            "type": "array",
            "description": "List of removed words.",
            "items": {
              "type": "string"
            }
          }
        ]
      }, {
        "name": "onImeMenuActivationChanged",
        "type": "function",
        "description": "Fired when the IME menu is activated or deactivated.",
        "parameters": [
          {
            "name": "activation",
            "type": "boolean",
            "description": "Whether the IME menu is currently active."
          }
        ]
      }, {
        "name": "onImeMenuListChanged",
        "type": "function",
        "description": "Fired when the input method or the list of active input method IDs is changed.",
        "parameters": []
      }, {
        "name": "onImeMenuItemsChanged",
        "type": "function",
        "description": "Fired when the input.ime.setMenuItems or input.ime.updateMenuItems API is called.",
        "parameters": [
          {
            "name": "engineID",
            "type": "string",
            "description": "ID of the engine to use"
          },
          {
            "name": "items",
            "type": "array",
            "items": {
              "$ref": "MenuItem"
            },
            "description": "MenuItems to add or update."
          }
        ]
      }, {
        "name": "onFocus",
        "type": "function",
        "description": "This event is sent when focus enters a text box. It is sent to all extensions that are listening to this event, and enabled by the user.",
        "parameters": [
          {
            "$ref": "InputContext",
            "name": "context",
            "description": "Describes the text field that has acquired focus."
          }
        ]
      }, {
        "name": "onSettingsChanged",
        "type": "function",
        "description": "This event is sent when the settings for any input method changed. It is sent to all extensions that are listening to this event, and enabled by the user.",
        "parameters": [
          {
            "name": "engineID",
            "type": "string",
            "description": "ID of the engine that changed"
          },
          {
            "name": "settings",
            "$ref": "InputMethodSettings",
            "description": "The new settings"
          }
        ]
      }, {
        "name": "onScreenProjectionChanged",
        "type": "function",
        "description": "This event is sent when the screen is being mirrored or the desktop is being cast.",
        "parameters": [
          {
            "name": "isProjected",
            "type": "boolean",
            "description": "Whether the screen is projected."
          }
        ]
      }, {
        "name": "onSuggestionsChanged",
        "type": "function",
        "description": "This event is sent when a new set of suggestions has been generated",
        "parameters": [
          {
            "name": "suggestions",
            "type": "array",
            "description": "List of suggestions to display, in order of relevance",
            "items": {
              "type": "string"
            }
          }
        ]
      }, {
        "name": "onInputMethodOptionsChanged",
        "type": "function",
        "description": "This event is sent when input method options are changed.",
        "parameters": [
          {
            "name": "engineID",
            "type": "string",
            "description": "The engine ID for the input method being changed."
          }
        ]
      }, {
        "name": "onLanguagePackStatusChanged",
        "type": "function",
        "description": "This event is sent when any IME's language pack status is changed.",
        "parameters": [
          {
            "name": "change",
            "$ref": "LanguagePackStatusChange",
            "description": "Information about what changed."
          }
        ]
      }
    ]
  }
]
