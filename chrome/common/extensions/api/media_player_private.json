// Copyright 2012 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

[
  {
    "namespace": "mediaPlayerPrivate",
    "compiler_options": {
      "implemented_in": "chrome/browser/ash/extensions/media_player_api.h"
    },
    "description": "none",
    "events": [
      {
        "name": "onNextTrack",
        "type": "function",
        "description": "Notifies that the next track was requested.",
        "parameters": []
      },
      {
        "name": "onPrevTrack",
        "type": "function",
        "description": "Notifies that the previous tack was requested.",
        "parameters": []
      },
      {
        "name": "onTogglePlayState",
        "type": "function",
        "description": "Notifies that a play/pause toggle was requested.",
        "parameters": []
      }
    ]
  }
]
