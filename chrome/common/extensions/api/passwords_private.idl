// Copyright 2015 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// Use the <code>chrome.passwordsPrivate</code> API to add or remove password
// data from the settings UI.
namespace passwordsPrivate {
  // Possible reasons why a plaintext password was requested.
  enum PlaintextReason {
    // The user wants to view the password.
    VIEW,
    // The user wants to copy the password.
    COPY,
    // The user wants to edit the password.
    EDIT
  };

  enum ExportProgressStatus {
    // No export was started.
    NOT_STARTED,
    // Data is being written to the destination.
    IN_PROGRESS,
    // Data has been written.
    SUCCEEDED,
    // The user rejected the file selection prompts.
    FAILED_CANCELLED,
    // Writing to the destination failed.
    FAILED_WRITE_FAILED
  };

  enum CompromiseType {
    // If the credentials was leaked by a data breach.
    LEAKED,
    // If the credentials was reused on a phishing site.
    PHISHED,
    // If the credentials have a password which was reused by other credentials.
    REUSED,
    // If the credentials have a weak password.
    WEAK
  };

  enum PasswordStoreSet {
    // Corresponds to profile-scoped password store.
    DEVICE,
    // Corresponds to Gaia-account-scoped password store (i.e. account store).
    ACCOUNT,
    // Corresponds to both profile-scoped and Gaia-account-scoped password
    // stores.
    DEVICE_AND_ACCOUNT
  };

  enum PasswordCheckState {
    // idle state, e.g. after successfully running to completion.
    IDLE,
    // Running, following an explicit user action to start the check.
    RUNNING,
    // Canceled, entered when the user explicitly cancels a check.
    CANCELED,
    // Offline, the user is offline.
    OFFLINE,
    // The user is not signed into Chrome.
    SIGNED_OUT,
    // The user does not have any passwords saved.
    NO_PASSWORDS,
    // The user hit the quota limit.
    QUOTA_LIMIT,
    // Any other error state.
    OTHER_ERROR
  };

  enum ImportResultsStatus {
    // Any other error state.
    UNKNOWN_ERROR,
    // Data was fully or partially imported.
    SUCCESS,
    // Failed to read provided file.
    IO_ERROR,
    // Header is missing, invalid or could not be read.
    BAD_FORMAT,
    // File selection dismissed.
    DISMISSED,
    // Size of the chosen file exceeds the limit.
    MAX_FILE_SIZE,
    // User has already started the import flow in a different window.
    IMPORT_ALREADY_ACTIVE,
    // User tried to import too many passwords from one file.
    NUM_PASSWORDS_EXCEEDED,
    // Conflicts found and they need to be resolved by the user.
    CONFLICTS
  };

  enum ImportEntryStatus {
    // Any other error state.
    UNKNOWN_ERROR,
    // Missing password field.
    MISSING_PASSWORD,
    // Missing url field.
    MISSING_URL,
    // Bad url formatting.
    INVALID_URL,
    // URL contains non-ASCII chars.
    NON_ASCII_URL,
    // URL is too long.
    LONG_URL,
    // Password is too long.
    LONG_PASSWORD,
    // Username is too long.
    LONG_USERNAME,
    // Credential is already stored in profile store.
    CONFLICT_PROFILE,
    // Credential is already stored in account store.
    CONFLICT_ACCOUNT,
    // Note is too long.
    LONG_NOTE,
    // Concatenation of imported and local notes is too long.
    LONG_CONCATENATED_NOTE,
    // Valid credential.
    VALID
  };

  enum FamilyFetchStatus {
    // Unknown or network error.
    UNKNOWN_ERROR,
    // No family members found.
    NO_MEMBERS,
    // At least one family member found.
    SUCCESS
  };

  dictionary PublicKey {
    // The value of the public key.
    DOMString value;
    // The version of the public key.
    long version;
  };

  dictionary RecipientInfo {
    // User ID of the recipient.
    DOMString userId;
    // Email of the recipient.
    DOMString email;
    // Name of the recipient.
    DOMString displayName;
    // Profile image URL of the recipient.
    DOMString profileImageUrl;
    // Whether the user can receive passwords.
    boolean isEligible;
    // The public key of the recipient.
    PublicKey? publicKey;
  };

  dictionary FamilyFetchResults {
    // Status of the family members fetch.
    FamilyFetchStatus status;
    // List of family members.
    RecipientInfo[] familyMembers;
  };

  dictionary ImportEntry {
    // The parsing status of individual row that represents
    // credential during import process.
    ImportEntryStatus status;
    // The url of the credential.
    DOMString url;
    // The username of the credential.
    DOMString username;
    // The password of the credential.
    DOMString password;
    // Unique identifier of the credential.
    long id;
  };

  dictionary ImportResults {
    // General status of the triggered passwords import process.
    ImportResultsStatus status;
    // Number of successfully imported passwords.
    long numberImported;
    // Possibly emtpy, list of credentials that couldn't be imported.
    ImportEntry[] displayedEntries;
    // Name of file that user has chosen for the import.
    DOMString fileName;
  };

  dictionary UrlCollection {
    // The signon realm of the credential.
    DOMString signonRealm;

    // A human readable version of the URL of the credential's origin. For
    // android credentials this is usually App name.
    DOMString shown;

    // The URL that will be linked to when an entry is clicked.
    DOMString link;
  };

  // Information specific to compromised credentials.
  dictionary CompromisedInfo {
    // The timestamp of when this credential was determined to be compromised.
    // Specified in milliseconds since the UNIX epoch. Intended to be passed to
    // the JavaScript Date() constructor.
    double compromiseTime;

    // The elapsed time since this credential was determined to be compromised.
    // This is passed as an already formatted string, since JavaScript lacks the
    // required formatting APIs. Example: "5 minutes ago"
    DOMString elapsedTimeSinceCompromise;

    // The types of credential issues.
    CompromiseType[] compromiseTypes;

    // Indicates whether this credential is muted.
    boolean isMuted;
  };

  // Structure which hold required information to display a link.
  dictionary DomainInfo {
    // A human readable version of the URL of the credential's origin. For
    // android credentials this is usually the app name.
    DOMString name;

    // The URL that will be linked to when an entry is clicked.
    DOMString url;

    // The signon_realm of corresponding PasswordForm.
    DOMString signonRealm;
  };

  // Entry used to display a password in the settings UI.
  dictionary PasswordUiEntry {
    // The URL collection corresponding to this saved password entry.
    DomainInfo[] affiliatedDomains;

    // The username used in conjunction with the saved password.
    DOMString username;

    // If this is a passkey, the user's display name. Empty otherwise.
    DOMString? displayName;

    // The password of the credential. Empty by default, only set if explicitly
    // requested.
    DOMString? password;

    // Text shown if the password was obtained via a federated identity.
    DOMString? federationText;

    // An index to refer back to a unique password entry record.
    long id;

    // Corresponds to where the credential is stored.
    PasswordStoreSet storedIn;

    // Indicates whether this credential is a passkey.
    boolean isPasskey;

    // The value of the attached note.
    DOMString? note;

    // The URL where the insecure password can be changed. Might be not set for
    // Android apps.
    DOMString? changePasswordUrl;

    // Additional information in case a credential is compromised.
    CompromisedInfo? compromisedInfo;

    // The timestamp of when this credential was created, or undefined if not a
    // passkey. Specified in milliseconds since the UNIX epoch. Intended to be
    // passed to the JavaScript Date() constructor.
    double? creationTime;
  };

  // Group representing affiliated PasswordUiEntries.
  dictionary CredentialGroup {
    // Group name being displayed.
    DOMString name;

    // Icon url for the given group.
    DOMString iconUrl;

    // Entries in the group.
    PasswordUiEntry[] entries;
  };

  dictionary ExceptionEntry {
    // The URL collection corresponding to this exception entry.
    UrlCollection urls;

    // An id to refer back to a unique exception entry record.
    long id;
  };

  dictionary PasswordExportProgress {
    // The current status of the export task.
    ExportProgressStatus status;

    // If |status| is $ref(ExportProgressStatus.SUCCEEDED), this will
    // be the full path of the written file.
    DOMString? filePath;

    // If |status| is $ref(ExportProgressStatus.FAILED_WRITE_FAILED), this will
    // be the name of the selected folder to export to.
    DOMString? folderName;
  };

  // Object describing the current state of the password check. The check could
  // be in any of the above described states.
  dictionary PasswordCheckStatus {
    // The state of the password check.
    PasswordCheckState state;

    // Total number of saved passwords.
    long? totalNumberOfPasswords;

    // How many passwords have already been processed. Populated if and only if
    // the password check is currently running.
    long? alreadyProcessed;

    // How many passwords are remaining in the queue. Populated if and only if
    // the password check is currently running.
    long? remainingInQueue;

    // The elapsed time since the last full password check was performed. This
    // is passed as a string, since JavaScript lacks the required formatting
    // APIs. If no check has been performed yet this is not set.
    DOMString? elapsedTimeSinceLastCheck;
  };

  // Object describing a password entry to be saved and storage to be used.
  dictionary AddPasswordOptions {
    // The url to save the password for.
    DOMString url;

    // The username to save the password for.
    DOMString username;

    // The password value to be saved.
    DOMString password;

    // The note attached the password.
    DOMString note;

    // True for account store, false for device store.
    boolean useAccountStore;
  };

  // An object holding an array of PasswordUiEntries.
  dictionary PasswordUiEntryList {
    PasswordUiEntry[] entries;
  };

  callback PlaintextPasswordCallback = void(DOMString password);
  callback PasswordListCallback = void(PasswordUiEntry[] entries);
  callback CredentialsGroupCallback = void(CredentialGroup[] entries);
  callback ExceptionListCallback = void(ExceptionEntry[] exceptions);
  callback ExportProgressStatusCallback = void(ExportProgressStatus status);
  callback VoidCallback = void();
  callback IsAccountStorageEnabledCallback = void(boolean enabled);
  callback PasswordCheckStatusCallback = void(PasswordCheckStatus status);
  callback ImportPasswordsCallback = void(ImportResults results);
  callback FetchFamilyResultsCallback = void(FamilyFetchResults results);
  callback GetUrlCollectionCallback = void(UrlCollection urlCollection);
  callback CredentialsWithReusedPasswordCallback =
      void(PasswordUiEntryList[] entries);
  callback PasswordManagerPinAvailableCallback = void(boolean available);
  callback PasswordManagerPinChangedCallback = void(boolean success);
  callback DisconnectCloudAuthenticatorCallback = void(boolean success);
  callback IsConnectedToCloudAuthenticatorCallback = void(boolean connected);
  callback DeleteAllPasswordManagerDataCallback = void(boolean success);
  callback AuthenticationResultCallback = void(boolean result);

  interface Functions {
    // Function that logs that the Passwords page was accessed from the Chrome
    // Settings WebUI.
    static void recordPasswordsPageAccessInSettings();

    // Changes the credential. Not all attributes can be updated.
    // Optional attributes that are not set will be unchanged.
    // Returns a promise that resolves if successful, and rejects otherwise.
    // |credential|: The credential to update. This will be matched to the
    // existing credential by id.
    static void changeCredential(
        PasswordUiEntry credential,
        optional VoidCallback callback);

    // Removes the credential corresponding to |id| in |fromStores|. If no
    // credential for this pair exists, this function is a no-op.
    // |id|: The id for the credential being removed.
    // |fromStores|: The store(s) from which the credential is being removed.
    static void removeCredential(long id, PasswordStoreSet fromStores);

    // Removes the saved password exception corresponding to |id|. If
    // no exception with this id exists, this function is a no-op. This will
    // remove exception from both stores.
    // |id|: The id for the exception url entry is being removed.
    static void removePasswordException(long id);

    // Undoes the last removal of saved password(s) or exception(s).
    static void undoRemoveSavedPasswordOrException();

    // Returns the plaintext password corresponding to |id|. Note that on
    // some operating systems, this call may result in an OS-level
    // reauthentication. Once the password has been fetched, it will be returned
    // via |callback|.
    // |id|: The id for the password entry being being retrieved.
    // |reason|: The reason why the plaintext password is requested.
    // |callback|: The callback that gets invoked with the retrieved password.
    static void requestPlaintextPassword(
        long id,
        PlaintextReason reason,
        PlaintextPasswordCallback callback);

    // Returns the PasswordUiEntries (with |password|, |note| field filled) corresponding
    // to |ids|. Note that on some operating systems, this call may result in an
    // OS-level reauthentication. Once the PasswordUiEntry has been fetched, it
    // will be returned via |callback|.
    // |ids|: Ids for the password entries being retrieved.
    // |callback|: The callback that gets invoked with the retrieved
    // PasswordUiEntries.
    static void requestCredentialsDetails(
        long[] ids,
        PasswordListCallback callback);

    // Returns the list of saved passwords.
    // |callback|: Called with the list of saved passwords.
    static void getSavedPasswordList(
        PasswordListCallback callback);

    // Returns the list of Credential groups.
    // |callback|: Called with the list of groups.
    static void getCredentialGroups(CredentialsGroupCallback callback);

    // Returns the list of password exceptions.
    // |callback|: Called with the list of password exceptions.
    static void getPasswordExceptionList(
        ExceptionListCallback callback);

    // Moves passwords currently stored on the device to being stored in the
    // signed-in, non-syncing Google Account. For each id, the result is a
    // no-op if any of these is true: |id| is invalid; |id| corresponds to a
    // password already stored in the account; or the user is not using the
    // account-scoped password storage.
    // |ids|: The ids for the password entries being moved.
    static void movePasswordsToAccount(long[] ids);

    // Fetches family members (password share recipients).
    static void fetchFamilyMembers(
        FetchFamilyResultsCallback callback);

    // Sends sharing invitations to the recipients.
    // |id|: The id of the password entry to be shared.
    // |recipients|: The list of selected recipients.
    // |callback|: The callback that gets invoked on success.
    static void sharePassword(
        long id,
        RecipientInfo[] recipients,
        optional VoidCallback callback);

    // Triggers the Password Manager password import functionality.
    static void importPasswords(
        PasswordStoreSet toStore,
        ImportPasswordsCallback callback);

    // Resumes the password import process when user has selected which
    // passwords to replace.
    // |selectedIds|: The ids of passwords that need to be replaced.
    static void continueImport(
        long[] selectedIds,
        ImportPasswordsCallback callback);

    // Resets the PasswordImporter if it is in the CONFLICTS/FINISHED state
    // and the user closes the dialog. Only when the PasswordImporter is in
    // FINISHED state, |deleteFile| option is taken into account.
    // |deleteFile|: Whether to trigger deletion of the last imported file.
    static void resetImporter(
        boolean deleteFile,
        optional VoidCallback callback);

    // Triggers the Password Manager password export functionality. Completion
    // Will be signaled by the onPasswordsFileExportProgress event.
    // |callback| will be called when the request is started or rejected. If
    // rejected $(ref:runtime.lastError) will be set to
    // <code>'in-progress'</code> or <code>'reauth-failed'</code>.
    static void exportPasswords(VoidCallback callback);

    // Requests the export progress status. This is the same as the last value
    // seen on the onPasswordsFileExportProgress event. This function is useful
    // for checking if an export has already been initiated from an older tab,
    // where we might have missed the original event.
    static void requestExportProgressStatus(
        ExportProgressStatusCallback callback);

    // Requests the account-storage enabled state of the current user.
    static void isAccountStorageEnabled(
        IsAccountStorageEnabledCallback callback);

    // Triggers the enabling / disabling flow for the account storage.
    static void setAccountStorageEnabled(boolean enabled);

    // Requests the latest insecure credentials.
    static void getInsecureCredentials(
        PasswordListCallback callback);

    // Requests group of credentials which reuse passwords. Each group contains
    // credentials with the same password value.
    static void getCredentialsWithReusedPassword(
        CredentialsWithReusedPasswordCallback callback);

    // Requests to mute |credential| from the password store.
    // Invokes |callback| on completion.
    static void muteInsecureCredential(
        PasswordUiEntry credential, optional VoidCallback callback);

    // Requests to unmute |credential| from the password store.
    // Invokes |callback| on completion.
    static void unmuteInsecureCredential(
        PasswordUiEntry credential, optional VoidCallback callback);

    // Starts a check for insecure passwords. Invokes |callback| on completion.
    static void startPasswordCheck(
        optional VoidCallback callback);

    // Returns the current status of the check via |callback|.
    static void getPasswordCheckStatus(
        PasswordCheckStatusCallback callback);

    // Requests whether the given |url| meets the requirements to save a
    // password for it (e.g. valid, has proper scheme etc.) and returns the
    // corresponding URLCollection on success. Otherwise it raises an error.
    static void getUrlCollection(
        DOMString url,
        GetUrlCollectionCallback callback);

    // Saves a new password entry described by the given |options|. Invokes
    // |callback| or raises an error depending on whether the operation
    // succeeded.
    // |options|: Details about a new password and storage to be used.
    // |callback|: The callback that gets invoked on success.
    static void addPassword(
        AddPasswordOptions options,
        optional VoidCallback callback);

    // Restarts the authentication timeout timer if the user is authenticated.
    // |callback|: The callback that gets invoked on success.
    static void extendAuthValidity(
        optional VoidCallback callback);

    // Switches Biometric authentication before filling state after
    // successful authentication.
    // |callback|: The callback that gets invoked with the authentication
    // result.
    [platforms = ("win", "mac", "chromeos")] static void
        switchBiometricAuthBeforeFillingState(
            AuthenticationResultCallback callback);

    // Shows a dialog for creating a shortcut for the Password Manager page.
    static void showAddShortcutDialog();

    // Opens a file with exported passwords in the OS shell.
    static void showExportedFileInShell(DOMString file_path);

    // Shows a dialog for changing the Password Manager PIN.
    static void changePasswordManagerPin(
        optional PasswordManagerPinChangedCallback callback);

    // Checks whether changing Password Manager PIN is possible.
    static void isPasswordManagerPinAvailable(
        PasswordManagerPinAvailableCallback callback);

    // Disconnects the Chrome client from the cloud authenticator.
    static void disconnectCloudAuthenticator(
        DisconnectCloudAuthenticatorCallback callback);

    // Checks whether the Chrome client is registered with/connected to
    // the cloud authenticator.
    static void isConnectedToCloudAuthenticator(
        IsConnectedToCloudAuthenticatorCallback callback);

    // Deletes all password manager data (passwords, passkeys, etc.)
    // |callback|: The callback that gets invoked with true on successful
    // deletion and false on failure (e.g. not all data was deleted).
    static void deleteAllPasswordManagerData(
        DeleteAllPasswordManagerDataCallback callback);
  };

  interface Events {
    // Fired when the saved passwords list has changed, meaning that an entry
    // has been added or removed.
    // |entries|: The updated list of password entries.
    static void onSavedPasswordsListChanged(PasswordUiEntry[] entries);

    // Fired when the password exceptions list has changed, meaning that an
    // entry has been added or removed.
    // |exceptions|: The updated list of password exceptions.
    static void onPasswordExceptionsListChanged(ExceptionEntry[] exceptions);

    // Fired when the status of the export has changed.
    // |status|: The progress status and an optional UI message.
    static void onPasswordsFileExportProgress(PasswordExportProgress status);

    // Fired when the enabled state for the account-scoped storage has changed.
    // |enabled|: The new enabled state.
    static void onAccountStorageEnabledStateChanged(boolean enabled);

    // Fired when the insecure credentials changed.
    // |insecureCredentials|: The updated insecure credentials.
    static void onInsecureCredentialsChanged(
        PasswordUiEntry[] insecureCredentials);

    // Fired when the status of the password check changes.
    // |status|: The updated status of the password check.
    static void onPasswordCheckStatusChanged(PasswordCheckStatus status);

    // Fired when the password manager access timed out.
    static void onPasswordManagerAuthTimeout();
  };
};
