// Copyright 2023 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// Use the <code>chrome.odfsConfigPrivate</code> API to configure the
// Microsoft OneDrive integration.
[implemented_in = "chrome/browser/chromeos/extensions/odfs_config_private/odfs_config_private_api.h",
modernised_enums]
namespace odfsConfigPrivate {

  enum Mount {
    allowed,
    disallowed,
    automated
  };

  dictionary MountInfo {
    Mount mode;
  };

  dictionary AccountRestrictionsInfo {
    DOMString[] restrictions;
  };

  callback GetMountCallback = void(MountInfo mount);
  callback GetAccountRestrictionsCallback = void(
      AccountRestrictionsInfo restrictions);
  callback ShowAutomatedMountErrorCallback = void();
  callback OpenInOfficeAppCallback = void();
  callback BoolCallback = void(boolean result);

  interface Functions {
    // Returns the OneDrive mount mode from the admin policy.
    // |callback|: Invoked when the policy value was retrieved.
    static void getMount(
        GetMountCallback callback);

    // Returns the OneDrive account restrictions from the admin policy.
    // |callback|: Invoked when the policy value was retrieved.
    static void getAccountRestrictions(
        GetAccountRestrictionsCallback callback);

    // Shows a notification that the automated mount failed.
    static void showAutomatedMountError(
        ShowAutomatedMountErrorCallback callback);

    // Returns whether the FileSystemProviderCloudFileSystem feature flag is
    // enabled.
    static void isCloudFileSystemEnabled(BoolCallback callback);

    // Returns whether the FileSystemProviderContentCache feature flag is
    // enabled.
    static void isContentCacheEnabled(BoolCallback callback);

    // Opens the tab inside the M365 PWA. This will not cause a new navigation
    // but instead re-parent the tab to a new instance of the M365 PWA. It does
    // nothing if the M365 PWA is not installed.
    //
    // |tabId| : Specifies the tab which should be opened in Office
    static void openInOfficeApp(long tabId, OpenInOfficeAppCallback callback);
  };

  interface Events {
    // Fired when the OneDrive mount mode changed.
    // |event|: A OneDrive mount mode changed event.
    static void onMountChanged(MountInfo event);

    // Fired when the OneDrive restrictions changed.
    // |event|: A OneDrive restrictions changed event.
    static void onAccountRestrictionsChanged(
        AccountRestrictionsInfo event);
  };

};
