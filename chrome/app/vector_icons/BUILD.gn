# Copyright 2017 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/chromeos/ui_mode.gni")
import("//components/vector_icons/vector_icons.gni")
import("//device/vr/buildflags/buildflags.gni")
import("//ui/webui/webui_features.gni")

aggregate_vector_icons("chrome_vector_icons") {
  icon_directory = "."

  sources = [
    # go/keep-sorted start
    "account_add_chrome_refresh.icon",
    "account_box.icon",
    "account_child_circle.icon",
    "account_circle.icon",
    "account_circle_chrome_refresh.icon",
    "account_manage_chrome_refresh.icon",
    "add.icon",
    "add_chrome_refresh.icon",
    "auto_tab_groups.icon",
    "autofill/local_offer_flipped_refresh.icon",
    "autofill/webauthn_dialog_header.icon",
    "autofill/webauthn_dialog_header_dark.icon",
    "back_arrow_touch.icon",
    "backspace.icon",
    "battery_saver_refresh.icon",
    "bookmark_all_tabs_chrome_refresh.icon",
    "bookmarkbar_overflow_refresh.icon",
    "bookmarks_lists_menu.icon",
    "bookmarks_manager.icon",
    "bookmarks_side_panel.icon",
    "bookmarks_side_panel_refresh.icon",
    "browser_logo.icon",
    "browser_tools.icon",
    "browser_tools_chrome_refresh.icon",
    "browser_tools_error.icon",
    "browser_tools_touch.icon",
    "browser_tools_update.icon",
    "browser_tools_update_chrome_refresh.icon",
    "cast_chrome_refresh.icon",
    "chevron_right.icon",
    "chevron_right_chrome_refresh.icon",
    "chromium_minimize.icon",
    "click_to_call_illustration.icon",
    "click_to_call_illustration_dark.icon",
    "close_chrome_refresh.icon",
    "close_group_refresh.icon",
    "close_tab_chrome_refresh.icon",
    "compare.icon",
    "computer_with_circle_background.icon",
    "copy.icon",
    "copy_menu.icon",
    "crashed_tab.icon",
    "create_new_tab_group.icon",
    "credit_card.icon",
    "credit_card_chrome_refresh.icon",
    "cut_menu.icon",
    "default_touch_favicon.icon",
    "default_touch_favicon_mask.icon",
    "desktop_windows_chrome_refresh.icon",
    "developer_tools.icon",
    "devices.icon",
    "devices_chrome_refresh.icon",
    "download_in_progress_chrome_refresh.icon",
    "download_in_progress_touch.icon",
    "download_menu.icon",
    "download_toolbar_button.icon",
    "download_toolbar_button_chrome_refresh.icon",
    "download_toolbar_button_touch.icon",
    "download_warning.icon",
    "drag_handle.icon",
    "drive_shortcut_chrome_refresh.icon",
    "edit_chrome_refresh.icon",
    "exit_menu.icon",
    "extension_crashed.icon",
    "file_download_shelf.icon",
    "file_save.icon",
    "file_save_chrome_refresh.icon",
    "file_save_off_chrome_refresh.icon",
    "fingerprint.icon",
    "forward_arrow_touch.icon",
    "fullscreen.icon",
    "fullscreen_refresh.icon",
    "generic_stop.icon",
    "globe.icon",
    "guardian.icon",
    "hardware_computer.icon",
    "hardware_computer_small.icon",
    "hardware_smartphone.icon",
    "help_menu.icon",
    "history.icon",
    "incognito.icon",
    "incognito_profile.icon",
    "incognito_refresh_menu.icon",
    "info.icon",
    "input.icon",
    "install_desktop_chrome_refresh.icon",
    "journeys.icon",
    "keep.icon",
    "keep_off.icon",
    "key.icon",
    "keyboard_arrow_down.icon",
    "keyboard_arrow_down_chrome_refresh.icon",
    "keyboard_arrow_right.icon",
    "keyboard_arrow_up.icon",
    "keyboard_arrow_up_chrome_refresh.icon",
    "laptop.icon",
    "leading_scroll.icon",
    "left_panel_close.icon",
    "link_chrome_refresh.icon",
    "logout.icon",
    "media_controls_arrow_drop_down.icon",
    "media_controls_arrow_drop_up.icon",
    "media_toolbar_button_chrome_refresh.icon",
    "media_toolbar_button_touch.icon",
    "menu_book_chrome_refresh.icon",
    "mixed_content.icon",
    "more_tools_menu.icon",
    "move_group_to_new_window_refresh.icon",
    "my_location.icon",
    "name_window.icon",
    "navigate_home.icon",
    "navigate_home_chrome_refresh.icon",
    "navigate_home_touch.icon",
    "navigate_stop.icon",
    "navigate_stop_chrome_refresh.icon",
    "navigate_stop_touch.icon",
    "new_tab_in_group_refresh.icon",
    "new_tab_refresh.icon",
    "new_window.icon",
    "notes.icon",
    "open_in_browser.icon",
    "open_in_full.icon",
    "open_in_new.icon",
    "open_in_new_chrome_refresh.icon",
    "open_in_new_off_chrome_refresh.icon",
    "overflow_button.icon",
    "overflow_button_touch.icon",
    "paintbrush.icon",
    "password_field.icon",
    "paste_menu.icon",
    "payments/save_card_and_vcn_success_confirmation.icon",
    "payments/save_card_and_vcn_success_confirmation_dark.icon",
    "people_group.icon",
    "performance.icon",
    "performance_speedometer.icon",
    "person.icon",
    "person_filled_padded_small.icon",
    "picture_in_picture_alt.icon",
    "print_menu.icon",
    "privacy_tip.icon",
    "qr_code_chrome_refresh.icon",
    "qrcode_generator.icon",
    "read_later_add.icon",
    "reading_list.icon",
    "release_alert.icon",
    "reload_touch.icon",
    "remove.icon",
    "report.icon",
    "request_mobile_site_checked.icon",
    "request_mobile_site_unchecked.icon",
    "resize_handle.icon",
    "right_panel_close.icon",
    "sad_tab.icon",
    "safety_check.icon",
    "save_group_refresh.icon",
    "save_page.icon",
    "saved_tab_group_bar_everything.icon",
    "science.icon",
    "search_menu.icon",
    "security.icon",
    "settings_menu.icon",
    "sharing_hub_screenshot.icon",
    "side_panel.icon",
    "smartphone.icon",
    "smartphone_refresh.icon",
    "speaker.icon",
    "speaker_group.icon",
    "split_scene.icon",
    "split_scene_down.icon",
    "split_scene_left.icon",
    "split_scene_right.icon",
    "split_scene_up.icon",
    "submit_feedback.icon",
    "supervisor_account_circle.icon",
    "sync_chrome_refresh.icon",
    "sync_circle.icon",
    "sync_error_circle.icon",
    "sync_paused_circle.icon",
    "sync_refresh.icon",
    "tab.icon",
    "tab_audio.icon",
    "tab_close_inactive.icon",
    "tab_close_normal.icon",
    "tab_group.icon",
    "tab_group_sharing.icon",
    "tab_groups_sync.icon",
    "tab_usb_connected.icon",
    "tablet.icon",
    "task_manager.icon",
    "text_analysis.icon",
    "toolbar_chrome_refresh.icon",
    "trailing_scroll.icon",
    "translate.icon",
    "trash_can.icon",
    "trash_can_refresh.icon",
    "tv.icon",
    "ungroup_refresh.icon",
    "user_account_avatar.icon",
    "user_account_avatar_refresh.icon",
    "view_list.icon",
    "web.icon",
    "webauthn/camera.icon",
    "webauthn/icloud_keychain.icon",
    "webauthn/icloud_keychain_color.icon",
    "webauthn/passkey_aoa.icon",
    "webauthn/passkey_aoa_dark.icon",
    "webauthn/passkey_error.icon",
    "webauthn/passkey_error_bluetooth.icon",
    "webauthn/passkey_error_bluetooth_dark.icon",
    "webauthn/passkey_error_dark.icon",
    "webauthn/passkey_fingerprint.icon",
    "webauthn/passkey_fingerprint_dark.icon",
    "webauthn/passkey_phone.icon",
    "webauthn/passkey_phone_dark.icon",
    "webauthn/passkey_usb.icon",
    "webauthn/passkey_usb_dark.icon",
    "webauthn/usb_security_key.icon",
    "webauthn/webauthn_error.icon",
    "webauthn/webauthn_error_dark.icon",
    "webauthn/windows_hello_color.icon",
    "webid/webid_arrow.icon",
    "webid/webid_globe.icon",
    "zoom_in.icon",
    "zoom_minus.icon",
    "zoom_minus_chrome_refresh.icon",
    "zoom_minus_menu_refresh.icon",
    "zoom_plus_chrome_refresh.icon",
    "zoom_plus_menu_refresh.icon",

    # go/keep-sorted end
  ]

  if (is_mac) {
    sources += [ "new_tab_mac_touchbar.icon" ]
  }

  if (is_win) {
    sources += [
      "back_arrow_windows.icon",
      "back_arrow_windows_touch.icon",
      "navigate_stop_windows.icon",
      "navigate_stop_windows_touch.icon",
      "open_in_phone.icon",
      "payments/secure_payment_confirmation_face.icon",
      "payments/secure_payment_confirmation_face_dark.icon",
      "reload_windows.icon",
      "reload_windows_touch.icon",
    ]
  } else {
    sources += [
      "payments/secure_payment_confirmation_fingerprint.icon",
      "payments/secure_payment_confirmation_fingerprint_dark.icon",
    ]
  }

  if (is_chromeos) {
    sources += [
      "autocorrect_undo.icon",
      "crostini_mascot.icon",
      "floating_workspace_notification.icon",
      "game_controls_add.icon",
      "game_controls_delete.icon",
      "game_controls_done.icon",
      "game_controls_dpad_keyboard.icon",
      "game_controls_edit_pen.icon",
      "game_controls_single_button.icon",
      "mouse_left_click_edit.icon",
      "mouse_left_click_view.icon",
      "mouse_right_click_edit.icon",
      "mouse_right_click_view.icon",
      "notification_battery.icon",
      "notification_captive_portal.icon",
      "notification_cellular_alert.icon",
      "notification_end_of_support.icon",
      "notification_family_link.icon",
      "notification_google.icon",
      "notification_image.icon",
      "notification_installed.icon",
      "notification_messages.icon",
      "notification_mobile_data.icon",
      "notification_mobile_data_off.icon",
      "notification_plugin_vm.icon",
      "notification_printing.icon",
      "notification_printing_done.icon",
      "notification_printing_warning.icon",
      "notification_storage_full.icon",
      "notification_vpn.icon",
      "notification_wifi.icon",
      "notification_wifi_off.icon",
      "person_add.icon",
      "sharesheet_share_with_others.icon",
      "shutdown_guest_os.icon",
      "terminal_ssh.icon",
      "tip.icon",
    ]
  }

  if (!is_android) {
    sources += [ "nearby_share.icon" ]
  }

  if (enable_webui_tab_strip) {
    sources += [ "new_tab_toolbar_button.icon" ]
  }
}

source_set("vector_icons") {
  sources = get_target_outputs(":chrome_vector_icons")

  deps = [
    ":chrome_vector_icons",
    "//base",
    "//skia",
    "//ui/gfx",
  ]
}
