// Copyright 2021 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

CANVAS_DIMENSIONS, 448,
PATH_COLOR_ARGB, 0xFF, 0x18, 0x5A, 0xBC,
R_<PERSON>OVE_TO, 257.48, 74.37,
R_<PERSON><PERSON><PERSON>C_TO, 0, -6.73, 3.9, -12.55, 9.56, -15.32,
R_<PERSON>UBIC_TO, -2.2, -6.2, -5.78, -12.02, -10.75, -16.99,
R_<PERSON>UBIC_TO, -17.84, -17.84, -46.77, -17.84, -64.61, 0,
<PERSON>_<PERSON>UBIC_TO, -4.96, 4.96, -8.54, 10.78, -10.75, 16.99,
<PERSON>_<PERSON>UBIC_TO, 5.67, 2.77, 9.56, 8.59, 9.56, 15.32,
R_<PERSON>U<PERSON><PERSON>_TO, 0, 6.73, -3.9, 12.55, -9.56, 15.32,
<PERSON>_<PERSON><PERSON><PERSON><PERSON>_TO, 2.2, 6.2, 5.78, 12.02, 10.75, 16.99,
<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>, 17.84, 17.84, 46.77, 17.84, 64.61, 0,
<PERSON>_<PERSON><PERSON><PERSON><PERSON>_TO, 4.96, -4.96, 8.54, -10.78, 10.75, -16.99,
R_CUBIC_TO, -5.66, -2.77, -9.56, -8.6, -9.56, -15.32,
<PERSON>LOSE,
NEW_PATH,
PATH_COLOR_AR<PERSON>, 0xFF, 0x8<PERSON>, 0xB4, 0xF8,
R_MOVE_TO, 239.83, 53.86,
R_<PERSON>UBIC_TO, -0.22, 0, -0.43, -0.05, -0.63, -0.16,
R_CUBIC_TO, -5.23, -2.7, -9.75, -3.84, -15.17, -3.84,
R_CUBIC_TO, -5.4, 0, -10.51, 1.28, -15.17, 3.84,
R_CUBIC_TO, -0.65, 0.35, -1.47, 0.11, -1.85, -0.55,
R_CUBIC_TO, -0.35, -0.65, -0.11, -1.5, 0.55, -1.85,
R_CUBIC_TO, 5.07, -2.75, 10.62, -4.17, 16.48, -4.17,
R_CUBIC_TO, 5.8, 0, 10.87, 1.28, 16.42, 4.14,
R_CUBIC_TO, 0.68, 0.35, 0.93, 1.17, 0.57, 1.82,
R_CUBIC_TO, -0.24, 0.5, -0.7, 0.77, -1.2, 0.77,
CLOSE,
R_MOVE_TO, -38.97, 14.3,
R_CUBIC_TO, -0.27, 0, -0.55, -0.08, -0.79, -0.25,
R_CUBIC_TO, -0.63, -0.43, -0.76, -1.28, -0.33, -1.9,
R_CUBIC_TO, 2.7, -3.81, 6.13, -6.81, 10.21, -8.9,
R_CUBIC_TO, 8.55, -4.41, 19.5, -4.44, 28.08, -0.03,
R_CUBIC_TO, 4.08, 2.1, 7.52, 5.07, 10.21, 8.85,
R_CUBIC_TO, 0.43, 0.6, 0.3, 1.47, -0.33, 1.9,
R_CUBIC_TO, -0.63, 0.43, -1.47, 0.3, -1.9, -0.33,
R_CUBIC_TO, -2.45, -3.43, -5.55, -6.13, -9.23, -8,
R_CUBIC_TO, -7.82, -4, -17.81, -4, -25.6, 0.03,
R_CUBIC_TO, -3.7, 1.9, -6.81, 4.63, -9.26, 8.06,
R_CUBIC_TO, -0.22, 0.38, -0.63, 0.57, -1.06, 0.57,
CLOSE,
R_MOVE_TO, 17.02, 32.87,
R_CUBIC_TO, -0.35, 0, -0.7, -0.13, -0.95, -0.41,
R_CUBIC_TO, -2.37, -2.37, -3.65, -3.9, -5.47, -7.19,
R_CUBIC_TO, -1.88, -3.35, -2.86, -7.43, -2.86, -11.82,
R_CUBIC_TO, 0, -8.09, 6.92, -14.68, 15.41, -14.68,
R_CUBIC_TO, 8.5, 0, 15.41, 6.59, 15.41, 14.68,
R_CUBIC_TO, 0, 0.76, -0.6, 1.36, -1.36, 1.36,
R_CUBIC_TO, -0.76, 0, -1.36, -0.6, -1.36, -1.36,
R_CUBIC_TO, 0, -6.59, -5.7, -11.95, -12.69, -11.95,
R_CUBIC_TO, -7, 0, -12.69, 5.37, -12.69, 11.95,
R_CUBIC_TO, 0, 3.92, 0.87, 7.55, 2.53, 10.48,
R_CUBIC_TO, 1.75, 3.13, 2.95, 4.47, 5.04, 6.59,
R_CUBIC_TO, 0.52, 0.55, 0.52, 1.39, 0, 1.93,
R_CUBIC_TO, -0.28, 0.27, -0.64, 0.4, -1, 0.4,
CLOSE,
R_MOVE_TO, 19.53, -5.04,
R_CUBIC_TO, -3.24, 0, -6.1, -0.82, -8.45, -2.42,
R_CUBIC_TO, -4.06, -2.75, -6.48, -7.22, -6.48, -11.95,
R_CUBIC_TO, 0, -0.76, 0.6, -1.36, 1.36, -1.36,
R_CUBIC_TO, 0.76, 0, 1.36, 0.6, 1.36, 1.36,
R_CUBIC_TO, 0, 3.84, 1.96, 7.46, 5.28, 9.7,
R_CUBIC_TO, 1.93, 1.3, 4.2, 1.93, 6.92, 1.93,
R_CUBIC_TO, 0.65, 0, 1.75, -0.08, 2.83, -0.27,
R_CUBIC_TO, 0.73, -0.13, 1.45, 0.35, 1.58, 1.12,
R_CUBIC_TO, 0.13, 0.73, -0.35, 1.45, -1.12, 1.58,
R_CUBIC_TO, -1.56, 0.28, -2.92, 0.31, -3.3, 0.31,
CLOSE,
R_MOVE_TO, -5.48, 5.61,
R_CUBIC_TO, -0.11, 0, -0.25, -0.03, -0.35, -0.05,
R_CUBIC_TO, -4.33, -1.2, -7.16, -2.8, -10.13, -5.72,
R_CUBIC_TO, -3.81, -3.78, -5.91, -8.83, -5.91, -14.22,
R_CUBIC_TO, 0, -4.41, 3.76, -8, 8.39, -8,
R_CUBIC_TO, 4.63, 0, 8.39, 3.6, 8.39, 8,
R_CUBIC_TO, 0, 2.92, 2.53, 5.28, 5.67, 5.28,
R_CUBIC_TO, 3.13, 0, 5.67, -2.37, 5.67, -5.28,
R_CUBIC_TO, 0, -10.27, -8.85, -18.6, -19.75, -18.6,
R_CUBIC_TO, -7.73, 0, -14.81, 4.3, -18, 10.98,
R_CUBIC_TO, -1.06, 2.2, -1.6, 4.8, -1.6, 7.63,
R_CUBIC_TO, 0, 2.12, 0.19, 5.47, 1.82, 9.83,
R_CUBIC_TO, 0.27, 0.7, -0.08, 1.5, -0.79, 1.75,
R_CUBIC_TO, -0.7, 0.27, -1.5, -0.11, -1.75, -0.79,
R_CUBIC_TO, -1.33, -3.57, -1.99, -7.11, -1.99, -10.78,
R_CUBIC_TO, 0, -3.27, 0.63, -6.23, 1.85, -8.83,
R_CUBIC_TO, 3.62, -7.6, 11.65, -12.53, 20.45, -12.53,
R_CUBIC_TO, 12.39, 0, 22.47, 9.56, 22.47, 21.33,
R_CUBIC_TO, 0, 4.41, -3.76, 8, -8.39, 8,
R_CUBIC_TO, -4.63, 0, -8.39, -3.6, -8.39, -8,
R_CUBIC_TO, 0, -2.92, -2.53, -5.28, -5.67, -5.28,
R_CUBIC_TO, -3.13, 0, -5.67, 2.37, -5.67, 5.28,
R_CUBIC_TO, 0, 4.65, 1.8, 9.01, 5.1, 12.28,
R_CUBIC_TO, 2.59, 2.56, 5.07, 3.98, 8.9, 5.04,
R_CUBIC_TO, 0.73, 0.19, 1.15, 0.95, 0.95, 1.66,
R_CUBIC_TO, -0.13, 0.61, -0.7, 1.02, -1.27, 1.02,
CLOSE,
NEW_PATH,
PATH_COLOR_ARGB, 0xFF, 0xFB, 0xBC, 0x04,
R_MOVE_TO, 175.26, 137.79,
R_CUBIC_TO, -2.21, 1.85, -5.51, 1.55, -7.35, -0.66,
R_CUBIC_TO, -1.85, -2.21, -1.55, -5.51, 0.66, -7.35,
R_LINE_TO, 8.01, -6.69,
R_CUBIC_TO, 2.21, -1.85, 5.51, -1.55, 7.35, 0.66,
R_CUBIC_TO, 1.85, 2.21, 1.55, 5.51, -0.66, 7.35,
CLOSE,
NEW_PATH,
PATH_COLOR_ARGB, 0xFF, 0x34, 0xA8, 0x53,
R_MOVE_TO, 277.97, 24,
R_CUBIC_TO, -1.2, 0.21, -1.72, 1.63, -0.94, 2.57,
R_LINE_TO, 9.35, 11.2,
R_CUBIC_TO, 0.78, 0.93, 2.28, 0.67, 2.69, -0.47,
R_LINE_TO, 5.02, -13.69,
R_CUBIC_TO, 0.42, -1.15, -0.55, -2.32, -1.75, -2.11,
CLOSE,
NEW_PATH,
PATH_COLOR_ARGB, 0xFF, 0x42, 0x85, 0xF4,
R_MOVE_TO, 152, 26.5,
R_CUBIC_TO, -0.26, -0.54, -0.04, -1.19, 0.49, -1.45,
R_LINE_TO, 7.81, -3.84,
R_CUBIC_TO, 0.54, -0.26, 1.19, -0.04, 1.45, 0.49,
R_LINE_TO, 3.84, 7.81,
R_CUBIC_TO, 0.26, 0.54, 0.05, 1.19, -0.49, 1.45,
R_LINE_TO, -7.81, 3.84,
R_CUBIC_TO, -0.54, 0.27, -1.19, 0.05, -1.45, -0.49,
CLOSE,
NEW_PATH,
PATH_COLOR_ARGB, 0xFF, 0x34, 0xA8, 0x53,
STROKE, 1.49,
MOVE_TO, 133.45, 105.41,
LINE_TO, 142.93, 102.24,
CLOSE,
NEW_PATH,
PATH_COLOR_ARGB, 0xFF, 0xEA, 0x43, 0x35,
STROKE, 1.49,
MOVE_TO, 305.58, 60.83,
LINE_TO, 314.55, 58.24,
CLOSE,
NEW_PATH,
PATH_COLOR_ARGB, 0xFF, 0xFB, 0xBC, 0x04,
STROKE, 1.49,
MOVE_TO, 229.57, 22.8,
LINE_TO, 232.04, 9.75,
CLOSE,
NEW_PATH,
PATH_COLOR_ARGB, 0xFF, 0x18, 0x5A, 0xBC,
STROKE, 2.24,
CIRCLE, 274.53, 74.37, 17.04,
NEW_PATH,
PATH_COLOR_ARGB, 0xFF, 0x18, 0x5A, 0xBC,
STROKE, 2.24,
CIRCLE, 173.46, 74.37, 17.04,
NEW_PATH,
PATH_COLOR_ARGB, 0xFF, 0x34, 0xA8, 0x53,
STROKE, 2.24,
R_MOVE_TO, 179.52, 82.71,
R_H_LINE_TO, -12.11,
R_CUBIC_TO, -0.47, 0, -0.85, -0.38, -0.85, -0.85,
R_V_LINE_TO, -9.4,
R_CUBIC_TO, 0, -0.47, 0.38, -0.85, 0.85, -0.85,
R_H_LINE_TO, 12.11,
R_CUBIC_TO, 0.47, 0, 0.85, 0.38, 0.85, 0.85,
R_V_LINE_TO, 9.4,
R_CUBIC_TO, 0, 0.47, -0.38, 0.85, -0.85, 0.85,
CLOSE,
NEW_PATH,
PATH_COLOR_ARGB, 0xFF, 0x34, 0xA8, 0x53,
STROKE, 2.24,
R_MOVE_TO, 176.34, 71.6,
R_V_LINE_TO, -4.03,
R_CUBIC_TO, 0, -1.59, -1.29, -2.88, -2.88, -2.88,
R_CUBIC_TO, -1.59, 0, -2.88, 1.29, -2.88, 2.88,
R_V_LINE_TO, 4.03,
CLOSE,
NEW_PATH,
PATH_COLOR_ARGB, 0xFF, 0x34, 0xA8, 0x53,
STROKE, 2.80,
MOVE_TO, 265.45, 76.65,
LINE_TO, 271.07, 82.27,
LINE_TO, 283.62, 69.73,
MOVE_TO, 283.62, 69.73,
CLOSE
