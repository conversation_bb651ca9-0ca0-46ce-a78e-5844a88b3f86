<script>
audioContext = new AudioContext();
dstNode = audioContext.createMediaStreamDestination();
mediaStream = dstNode.stream;

// Allocate AudioNode with a reference to AudioSourceProvider owned by
// the media stream track.
srcNode = audioContext.createMediaStreamSource(mediaStream);

// Free media stream track.
mediaTrack = mediaStream.getAudioTracks()[0];
mediaStream.removeTrack(mediaTrack);
mediaTrack = null;
gc();

// Use AudioSourceProvider owned by the media stream track, and ensure there
// is no crash.
srcNode.connect(audioContext.destination);
</script>