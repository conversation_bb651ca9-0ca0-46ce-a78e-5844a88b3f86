# Copyright 2023 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("../../build_webui_tests.gni")

build_webui_tests("build") {
  files = [
    "extension_approvals_test.ts",
    "parent_access_after_test.ts",
    "parent_access_app_test.ts",
    "parent_access_controller_test.ts",
    "parent_access_disabled_test.ts",
    "parent_access_test_client.ts",
    "parent_access_test_utils.ts",
    "parent_access_ui_handler_test.ts",
    "parent_access_ui_test.ts",
    "test_parent_access_ui_handler.ts",
    "webview_manager_test.ts",
  ]

  static_files = [ "test_content.html" ]

  ts_path_mappings = [
    "chrome://parent-access/strings.m.js|" +
        rebase_path("//tools/typescript/definitions/strings.d.ts",
                    target_gen_dir),
    "chrome://parent-access/*|" + rebase_path(
            "$root_gen_dir/chrome/browser/resources/chromeos/parent_access/tsc/*",
            target_gen_dir),
    "chrome://webui-test/chromeos/*|" +
        rebase_path("$root_gen_dir/chrome/test/data/webui/chromeos/tsc/*",
                    target_gen_dir),
  ]

  ts_deps = [
    "//ash/webui/common/resources:build_ts",
    "//chrome/browser/resources/chromeos/parent_access:build_ts",
    "//chrome/test/data/webui/chromeos:build_ts",
    "//third_party/polymer/v3_0:library",
    "//ui/webui/resources/js:build_ts",
    "//ui/webui/resources/mojo:build_ts",
  ]

  ts_definitions = [
    "//tools/typescript/definitions/chrome_event.d.ts",
    "//tools/typescript/definitions/chrome_test.d.ts",
    "//tools/typescript/definitions/context_menus.d.ts",
    "//tools/typescript/definitions/extension_types.d.ts",
    "//tools/typescript/definitions/tabs.d.ts",
    "//tools/typescript/definitions/web_request.d.ts",
    "//tools/typescript/definitions/webview_tag.d.ts",
  ]
}
