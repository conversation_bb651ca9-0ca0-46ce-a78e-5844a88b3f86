/*
 *  Copyright 2024 The Chromium Authors
 *  Use of this source code is governed by a BSD-style license that can be
 *  found in the LICENSE file.
 */
.hidden {
  display: none;
}

.warning {
  color: red;
  font-weight: 400;
}

@media screen and (min-width: 1000px) {
  /* hack! to detect non-touch devices */
  div#links a {
    line-height: 0.8em;
  }
}

audio {
  max-width: 100%;
}

body {
  font-family: 'Roboto', sans-serif;
  font-weight: 300;
  margin: 0;
  padding: 1em;
  word-break: break-word;
}

code {
  font-family: 'Roboto', sans-serif;
  font-weight: 400;
}

div#container {
  margin: 0 auto 0 auto;
  max-width: 60em;
  padding: 1em 1.5em 1.3em 1.5em;
}

h1 {
  border-bottom: 1px solid #ccc;
  font-family: 'Roboto', sans-serif;
  font-weight: 500;
  margin: 0 0 0.8em 0;
  padding: 0 0 0.2em 0;
}

h2 {
  color: #444;
  font-weight: 500;
}

h3 {
  border-top: 1px solid #eee;
  color: #666;
  font-weight: 500;
  margin: 10px 0 10px 0;
  white-space: nowrap;
}

html {
  /* avoid annoying page width change
  when moving from the home page */
  overflow-y: scroll;
}

p {
  color: #444;
  font-weight: 300;
}

p#data {
  border-top: 1px dotted #666;
  font-family: Courier New, monospace;
  line-height: 1.3em;
  max-height: 1000px;
  overflow-y: auto;
  padding: 1em 0 0 0;
}

div#errorMsg p {
  color: #f00;
}

strong {
  font-weight: 500;
}

video {
  background: #222;
  margin: 0 0 20px 0;
  --width: 100%;
  width: var(--width);
  height: calc(var(--width) * 0.75);
}

ul {
  margin: 0 0 0.5em 0;
}

fieldset {
  margin: 0 0 1em 0;
}

fieldset > select {
  margin-top: 1em;
}

@media screen and (max-width: 650px) {
  .highlight {
    font-size: 1em;
    margin: 0 0 20px 0;
    padding: 0.2em 1em;
  }

  h1 {
    font-size: 24px;
  }
}

@media screen and (max-width: 550px) {
  button:active {
    background-color: darkRed;
  }

  h1 {
    font-size: 22px;
  }
}

@media screen and (max-width: 450px) {
  h1 {
    font-size: 20px;
  }
}
