<!DOCTYPE html>
<html>
<head>
  <title>Picture-in-Picture iframe Test</title>
</head>
<body>
  <video controls preload=auto src='../bigbuck.webm'></video>
</body>
<script>
  let src = 'iframe-content.html';
  if (location.search) {
    const param = 'embed_url=';
    src = location.search.slice(location.search.search(param) + param.length);
  }
  const iframe = document.createElement('iframe');
  iframe.src = src;
  document.body.appendChild(iframe);

  function removeFrame() {
    document.body.removeChild(document.querySelector('iframe'));
  }

  const video = document.querySelector('video');

  async function enterPictureInPicture() {
    await _waitForMetadata();
    await video.requestPictureInPicture();
    return true;
  }

  function _waitForMetadata() {
    return new Promise(resolve => {
      if (video.readyState >= HTMLMediaElement.HAVE_METADATA) {
        resolve();
        return;
      }

      video.addEventListener('loadedmetadata', () => {
        resolve();
      }, { once: true });
    });
  }
</script>
</html>
