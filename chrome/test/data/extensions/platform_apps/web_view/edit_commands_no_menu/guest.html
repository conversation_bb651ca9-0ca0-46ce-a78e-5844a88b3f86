<!--
Copyright 2013 The Chromium Authors
Use of this source code is governed by a BSD-style license that can be
found in the LICENSE file.
-->
<html>
  <head>
    <script type="text/javascript">
      // A guest that monitors focus and edit commands.
      // Notifies the embedder about changes in focus via postMessage.
      // Note that the embedder has to initiate a postMessage first so that
      // the guest has a reference to the embedder's window.

      // The window reference of the embedder to send post message reply.
      var embedderWindowChannel = null;
      var embedderTestName = '';

      var notifyEmbedder = function(msg_array) {
        embedderWindowChannel.postMessage(JSON.stringify(msg_array), '*');
      };

      var onPostMessageReceived = function(e) {
        embedderWindowChannel = e.source;
        var data = JSON.parse(e.data);
        if (data[0] == 'create-channel') {
          embedderTestName = data[1];
          notifyEmbedder(['channel-created']);
          return;
        }
        if (data[0] == 'end-of-line') {
          window.getSelection().empty();
          var testinput = document.getElementById('testinput');
          var length = testinput.value.length;
          testinput.focus();
          testinput.selectionStart = length;
          return;
        }
      };
      window.addEventListener('message', onPostMessageReceived, false);

      window.addEventListener('load', function(e) {
        document.getElementById('testinput').addEventListener('keyup',
            function(e) {
          var response = 'caret-position-' + this.selectionStart;
          notifyEmbedder([response, embedderTestName]);
        });
        document.getElementById('testinput').addEventListener('focus',
            function(e) {
          var response = 'testinput-focused';
          notifyEmbedder([response, embedderTestName]);
        });
      });

    </script>
  </head>
  <body>
    <div>This is a guest that verifies edit commands are sent.</div>
    <input id='testinput' type="text" value="test">
  </body>
</html>
