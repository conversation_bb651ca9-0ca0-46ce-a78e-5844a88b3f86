{
  // Extension ID: knldjmfmopnpolahpmmgbagdohdnhkik
  "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDcBHwzDvyBQ6bDppkIs9MP4ksKqCMyXQ/A52JivHZKh4YO/9vJsT3oaYhSpDCE9RPocOEQvwsHsFReW2nUEc6OLLyoCFFxIb7KkLGsmfakkut/fFdNJYh0xOTbSN8YvLWcqph09XAY2Y/f0AL7vfO1cuCqtkMt8hFrBGWxDdf9CQIDAQAB",
  "version": "*******",
  "manifest_version": 2,
  "name": "Desktop Capture API delegation test",
  "description": "Test desktop capture API",
  "background": {
    "scripts": ["background.js"]
  },
  "permissions": [
    "desktopCapture"
  ],
  "externally_connectable": {
    "matches": ["*://localhost/*"]
  }
}
