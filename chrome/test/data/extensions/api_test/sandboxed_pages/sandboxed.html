This page should be sandboxed.

<script>
// We're not served with the extension default CSP, we can use inline script.
onload = function() {
  var secret = 'sandboxed_window_secret';

  if (chrome.extension) {
    chrome.test.notifyFail('Extension APIs should not be injected.');
    return;
  }

  var mainWindow = window.opener || window.top;
  var mainWindowSecret = undefined;
  try {
    mainWindowSecret = mainWindow.secret;
  } catch(e) {}
  mainWindow.postMessage(mainWindowSecret, '*');
};
</script>
