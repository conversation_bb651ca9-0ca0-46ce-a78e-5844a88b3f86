{
  // extension id: jofgjd<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>mellaapdjjadibj
  "name": "Test Setting Bluetooth Adapter State",
  "description": "Tests chrome.bluetoothPrivate.setAdapterState function.",
  "version": "1.0",
  "manifest_version": 2,
  "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA2nI64+TVbJNYUfte1hEwrWpjgiH3ucfKZ12NC6IT/Pm2pQdjR/3alrdW+rCkYbs0KmfUymb0GOE7fwZ0Gs+EqfxoKmwJaaZiv86GXEkPJctDvjqrJRUrKvM6aXZEkTQeaFLQVY9NDk3grSZzvC365l3c4zRN3A2i8KMWzB9HRQzKnN49zjgcTTu5DERYTzbJZBd0m9Ln1b3x3UVkVgoTUq7DexGMcOq1KYz0VHrFRo/LN1yJvECFmBb2pdl40g4UHq3UqrWDDInZZJ3sr01EePxYYwimMFsGnvH6sz8wHC09rXZ+w1YFYjsQ3P/3Bih1q/NdZ0aop3MEOCbHb4gipQIDAQAB",
  "app": {
    "background": {
      "scripts": ["test.js"]
    }
  },
  "bluetooth": {},
  "permissions": [ "bluetoothPrivate" ]
}
