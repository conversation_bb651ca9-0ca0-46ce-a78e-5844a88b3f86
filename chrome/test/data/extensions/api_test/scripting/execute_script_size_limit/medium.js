// Copyright 2023 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

var div = document.createElement('div');
div.id = 'medium';
document.body.appendChild(div);
/*
PADDING OUT THE FILE TO BE OVER 500 BYTES
PADDING OUT THE FILE TO BE OVER 500 BYTES
PADDING OUT THE FILE TO BE OVER 500 BYTES
PADDING OUT THE FILE TO BE OVER 500 BYTES
PADDING OUT THE FILE TO BE OVER 500 BYTES
PADDING OUT THE FILE TO BE OVER 500 BYTES
PADDING OUT THE FILE TO BE OVER 500 BYTES
*/
