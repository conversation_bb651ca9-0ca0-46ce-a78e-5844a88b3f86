{
  // Extension ID: knldjmfmopnpolahpmmgbagdohdnhkik
  "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDcBHwzDvyBQ6bDppkIs9MP4ksKqCMyXQ/A52JivHZKh4YO/9vJsT3oaYhSpDCE9RPocOEQvwsHsFReW2nUEc6OLLyoCFFxIb7KkLGsmfakkut/fFdNJYh0xOTbSN8YvLWcqph09XAY2Y/f0AL7vfO1cuCqtkMt8hFrBGWxDdf9CQIDAQAB",
  "name": "chrome.webrtcLoggingPrivate.start/stop test",
  "version": "0.1",
  "manifest_version": 2,
  "description": "Tests that WebRTC logging can be started and stopped properly.",
  "app": {
    "background": {
      "scripts": ["test.js"]
    }
  },
  "permissions": ["webrtcLoggingPrivate", "webview"]
}
