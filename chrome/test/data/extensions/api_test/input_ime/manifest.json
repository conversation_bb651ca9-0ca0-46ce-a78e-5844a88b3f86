{
  "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC9fDu8apG3Dz72XTT3Ym1SfGt06tdowTlYQ+3lGlCbVpfnMOmewgRgYxzUtUPso9aQERZcmI2+7UtbWjtk6/usl9Hr7a1JBQwfaUoUygEe56ajUeZhe/ErkH5CXT84U0pokfPr5vMvc7RVPduU+UBiF0DnGb/hSpzz/1UhJ5H9AwIDAQAB",
  "name": "input.ime test",
  "version": "0.1",
  "manifest_version": 2,
  "description": "Input method ime tests.",
  "background": {
    "scripts": ["background.js"]
  },
  "permissions": [ "input", "inputMethodPrivate" ],
  "input_components": [{
    "name": "Test IME",
    "id": "test",
    "language": "en",  // The primary language this IME is used for
    "layouts": ["us::eng"]
  }]
}
