{
  // chrome-extension://pkplfbidichfdicaijlchgnapepdginl
  "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtDfX9dHNh948bt00YhZBm3P6E5QLaOt+v8kXVtibQfiPtOD2FTScB/f0wX/EQWVO7BkaSOsRkTPcPIgocyMPYr2FLgqGLFlYT9nQpKJZUFNF5oJ5rG6Nv7ppf4zEB3j6da1IBRTz2yOZ+6O1TMZxol/V62/QcqrJeggsHTEPGLdr9Ua4b1Ka0xKJnJngZljsbw93FI1o+P9dAh5BS6wTPiZI/vmJVjvMTkSTnaZ3n9Go2t7A0XLcSxLcVyuLAd2mAvSN0mIviOukdM66wr7llif71nKuUt+4qvlr/r9HfwzN6pA4jkwhtS1UD+3CmB+wsHwsnohNcuu4FIQ6rgq/7QIDAQAB",
  "name": "chrome.fileSystemProvider.onConfigureRequested (unresponsive extension)",
  "version": "0.1",
  "manifest_version": 2,
  "description":
      "Test for chrome.fileSystemProvider.onConfigureRequested() being unresponsive within an extension.",
  "permissions": [
    "fileSystemProvider",
    {
      "fileSystem": ["requestFileSystem", "write"]
    },
    "fileManagerPrivate"
  ],
  "file_system_provider_capabilities": {
    "configurable": true,
    "source": "device"
  },
  "background": {
    "scripts": [
      "chrome-extension://gfnblenhaahcnmfdbebgincjohfkbnch/test_util.js",
      "test.js"
    ]
  },
  // gfnblenhaahcnmfdbebgincjohfkbnch is the ../test_utils component extension ID.
  "content_security_policy": "default-src 'none'; script-src 'self' blob: filesystem: chrome-extension://gfnblenhaahcnmfdbebgincjohfkbnch;"
}
