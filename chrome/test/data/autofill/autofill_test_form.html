<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN">
<!-- Autofill generic test form. -->
<html>
<!-- The following call prevents the zoom-on-focus animation which caused test flakes. -->
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <head>
    <title>Autofill Test Form</title>
  </head>
  <body>
    <h3>Autofill Test Form</h3>
    <form name="testform" method="post" id="testform">
    <p>
      <label for="NAME_FIRST">First Name:</label> <input type="text" id="NAME_FIRST">
      <label for="NAME_LAST">Last Name:</label> <input type="text" id="NAME_LAST">
      <label for="ADDRESS_HOME_LINE1">Address:</label> <input type="text" id="ADDRESS_HOME_LINE1">
      <label for="ADDRESS_HOME_CITY">City:</label> <input type="text" id="ADDRESS_HOME_CITY" >
      <label for="ADDRESS_HOME_STATE">State:</label> <input type="text" id="ADDRESS_HOME_STATE" >
      <label for="ADDRESS_HOME_ZIP">Zip:</label> <input type="text" id="ADDRESS_HOME_ZIP" >
      <label for="ADDRESS_HOME_COUNTRY">Country:</label> <input type="text" id="ADDRESS_HOME_COUNTRY" >
      <label for="EMAIL_ADDRESS">Email:</label> <input type="text" id="EMAIL_ADDRESS">
      <label for="PHONE_HOME_WHOLE_NUMBER">Phone:</label> <input type="text" id="PHONE_HOME_WHOLE_NUMBER">
      <input type="submit" value="send"> <input type="reset">
    </p>
    </form>
  </body>
</html>

