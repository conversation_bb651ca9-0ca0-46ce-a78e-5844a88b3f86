<!DOCTYPE html>
<html>
  <script>
    if (window.location.hash.indexOf('clear') != -1) {
      localStorage.removeItem('Foo');
      document.cookie = '';
    }

    function setStorage() {
      localStorage.setItem('Foo', 'Bar');
      document.cookie = 'Foo=Bar';
    }

    function hasAllStorage() {
      return localStorage.getItem('Foo') == 'Bar'
             && document.cookie == 'Foo=Bar';
    }
  </script>
  <body>Test for setting and getting local storage.</body>
</html>
