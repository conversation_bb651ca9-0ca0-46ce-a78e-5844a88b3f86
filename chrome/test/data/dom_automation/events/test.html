<html>
<script>
function renameLink() {
  document.links[0].innerHTML = "clicked";
}
function changeTextfield() {
  document.getElementById("textfield").value = "clicked";
}
</script>
<body>
<a href="javascript:renameLink();">link</a>
<textarea>textarea</textarea>
<form action="">
  <input id="textfield" type='text' value='textfield'></input>
  <input id="button" type='button' value='button' onclick="changeTextfield()">
  </input>
</form>
</body>
</html>
