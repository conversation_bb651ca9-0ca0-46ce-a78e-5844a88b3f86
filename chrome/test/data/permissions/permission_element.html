<!DOCTYPE html>
<html>
<head>
  <style>
    permission {
      font-size: xxx-large;
      margin: 0px;
      border: 0px;
      width: 188px;
      height: 1.2em;
    }
  </style>
</head>
<body>
  <div id="parent">
    <permission id="geolocation" type="geolocation"></permission>
    <permission id="microphone" type="microphone"></permission>
    <permission id="camera" type="camera"></permission>
    <permission id="invalid" type="invalid microphone"></permission>
    <permission id="camera-microphone" type="camera microphone"></permission>
  </div>
  <script>
    document.getElementById('parent').addEventListener('resolve', (event) => {
      console.log(event.target.id + '-resolve');
    });
    document.getElementById('parent').addEventListener('dismiss', (event) => {
      console.log(event.target.id + '-dismiss');
    });
    document.getElementById('parent').addEventListener('promptaction', (event) => {
      console.log(event.target.id + '-promptaction');
    });
    document.getElementById('parent').addEventListener('promptdismiss', (event) => {
      console.log(event.target.id + '-promptdismiss');
    });

    function clickById(element_id) {
      click(document.getElementById(element_id));
    }

    function click(element) {
      element.click();
    }

    // Waits until the given permission element is granted.
    function notifyWhenGranted(id) {
      if (document.getElementById(id).matches(':granted')) {
        console.log(id + '-granted');
      } else {
        window.setTimeout(() => {
          notifyWhenGranted(id);
        }, 50);
      }
    }

    function setFontSizeSmall() {
      document.querySelectorAll('permission').forEach((el) => {
        el.style.fontSize = 'small';
      });
    }

    window.addEventListener('message', function(event) {
      setFontSizeSmall();
      clickById(event.data);
    });
  </script>
</body>
</html>
