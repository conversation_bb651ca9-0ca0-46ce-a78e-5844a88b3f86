# Copyright 2020 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/android/config.gni")
import("//build/config/android/rules.gni")

# TODO(crbug.com/40682351): Extract //chrome/browser/payments/android source files
# from //chrome/browser/BUILD.gn into an independent build target here.

# TODO(crbug.com/40741093): remove the dependency on chrome_java.
robolectric_library("junit_test_support") {
  deps = [
    "//base:base_java",
    "//chrome/android:chrome_java",
    "//chrome/browser/android/lifecycle:java",
    "//chrome/browser/profiles/android:java",
    "//chrome/browser/tabmodel:java",
    "//components/payments/content/android:java",
    "//components/payments/content/android:junit_test_support",
    "//components/payments/content/android:service_java",
    "//components/payments/mojom:mojom_java",
    "//content/public/android:content_java",
    "//mojo/public/java:bindings_java",
    "//mojo/public/java:system_java",
    "//third_party/androidx:androidx_annotation_annotation_java",
    "//third_party/blink/public/mojom:android_mojo_bindings_java",
    "//third_party/mockito:mockito_java",
    "//ui/android:ui_no_recycler_view_java",
    "//url:gurl_java",
    "//url:gurl_junit_test_support",
  ]
  sources = [
    "java/src/org/chromium/chrome/browser/payments/test_support/MockPaymentUiServiceBuilder.java",
    "java/src/org/chromium/chrome/browser/payments/test_support/PaymentRequestParamsBuilder.java",
    "java/src/org/chromium/chrome/browser/payments/test_support/ShadowProfile.java",
  ]
}

# TODO(crbug.com/40741093): remove the dependency on chrome_java.
robolectric_library("junit") {
  deps = [
    ":junit_test_support",
    "//base:base_java_test_support",
    "//base:base_junit_test_support",
    "//base:callback_java",
    "//chrome/android:chrome_java",
    "//chrome/browser/autofill/android:java",
    "//chrome/browser/profiles/android:java",
    "//components/autofill/android:main_autofill_java",
    "//components/payments/content/android:java",
    "//components/payments/content/android:junit_test_support",
    "//components/payments/content/android:service_java",
    "//components/payments/mojom:mojom_java",
    "//components/security_state/core:security_state_enums_java",
    "//content/public/android:content_java",
    "//content/public/test/android:content_java_test_support",
    "//mojo/public/java:bindings_java",
    "//mojo/public/java:system_java",
    "//services/service_manager/public/java:service_manager_java",
    "//third_party/androidx:androidx_appcompat_appcompat_java",
    "//third_party/androidx:androidx_test_rules_java",
    "//third_party/androidx:androidx_test_runner_java",
    "//third_party/blink/public/mojom:android_mojo_bindings_java",
    "//third_party/jni_zero:jni_zero_java",
    "//third_party/junit",
    "//third_party/mockito:mockito_java",
    "//ui/android:ui_no_recycler_view_java",
  ]
  sources = [
    "java/src/org/chromium/chrome/browser/payments/AutofillContactTest.java",
    "java/src/org/chromium/chrome/browser/payments/AutofillContactUnitTest.java",
    "java/src/org/chromium/chrome/browser/payments/ChromePaymentRequestFactoryTest.java",
    "java/src/org/chromium/chrome/browser/payments/DialogControllerImplUnitTest.java",
    "java/src/org/chromium/chrome/browser/payments/PaymentRequestIntegrationTest.java",
    "java/src/org/chromium/chrome/browser/payments/WindowAndroidIntentLauncherUnitTest.java",
    "java/src/org/chromium/chrome/browser/payments/handler/toolbar/PaymentHandlerToolbarMediatorTest.java",
  ]
}
