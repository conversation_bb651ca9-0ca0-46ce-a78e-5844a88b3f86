// Copyright 2022 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

syntax = "proto2";

package arc.input_overlay;

option optimize_for = LITE_RUNTIME;

// Input device source for each action. Each type of the actions can be bound
// to different types of input device sources. Some actions may be bound to
// different types of device sources.
enum InputSource {
  IS_NONE = 0;
  IS_KEYBOARD = 1;  // 1 << 0
  IS_MOUSE = 2;     // 1 << 1
  // TODO(cuicuiruan): Add Gamepad support.
}

// Mouse action enum.
enum MouseAction {
  NONE = 0;
  PRIMARY_CLICK = 1;
  SECONDARY_CLICK = 2;
  HOVER_MOVE = 3;
  PRIMARY_DRAG_MOVE = 4;
  SECONDARY_DRAG_MOVE = 5;
}

// Action types according to the touch events.
enum ActionType {
  // `TAP` involves touch down and up.
  TAP = 0;
  // `MOVE` involves touch down, move and up.
  MOVE = 1;
}

message InputElementProto {
  // Input sources for each action. One input binding may have more than one
  // source. For example, the action type may need to combine key input and
  // mouse input together.
  required uint32 input_sources = 1;
  // DomCode/key strings for the input binding. For reference of the name
  // list, please refer to ui/events/keycodes/dom/dom_code_data.inc.
  repeated string dom_codes = 2;
  // Mouse action for the input binding.
  optional MouseAction mouse_action = 3;
}

message PositionProto {
  // Anchor to target with x and y values.
  repeated float anchor_to_target = 1;
}

message ActionProto {
  // Action ID. Each action has a unique ID within the game/app.
  required int32 id = 1;
  // Input Element. Customized input binding for this action.
  optional InputElementProto input_element = 2;
  // Customized Positions for this action. Now it only saves one position. Use
  // keyword `repeated` for future expansion.
  repeated PositionProto positions = 3;
  // Action type for this action.
  optional ActionType action_type = 4;
  // This action is deleted or not. It is deprecated because "restore to
  // default" function is no longer needed.
  optional bool deleted = 5 [deprecated = true];
  // The index of the name for this action.
  optional int32 name_index = 6 [default = -1];
}

message AppDataProto {
  // Actions. A list of actions for one game.
  repeated ActionProto actions = 1;
  // Input control is on or off. This is associated with the toggle of game
  // control/assist on the menu.
  optional bool input_control = 2;
  // Input mapping hint is on or off. This is associated with the toggle of
  // show hint overlay on the menu.
  optional bool input_mapping_hint = 3;
  // Customized Positions for the menu entry icon. It is saved when the app
  // is closed.
  optional PositionProto menu_entry_position = 4;
  // System version. Latest version of Game controls. If it is not set, it's
  // Alpha version. Otherwise, it is AlphaV2+ version.
  optional string system_version = 5;
}
