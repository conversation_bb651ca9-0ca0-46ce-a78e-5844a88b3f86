// Copyright 2022 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#include "chrome/browser/ash/arc/net/cert_manager_impl.h"

#include <optional>
#include <string>

#include "base/functional/callback.h"
#include "base/run_loop.h"
#include "base/strings/string_number_conversions.h"
#include "base/task/bind_post_task.h"
#include "base/test/test_future.h"
#include "chrome/test/base/testing_profile.h"
#include "content/public/test/browser_task_environment.h"
#include "crypto/scoped_test_nss_db.h"
#include "net/cert/nss_cert_database.h"
#include "net/cert/scoped_nss_types.h"
#include "net/cert/x509_util_nss.h"
#include "testing/gtest/include/gtest/gtest.h"
#include "third_party/boringssl/src/pki/pem.h"

namespace arc {

namespace {

constexpr char kPrivateKey[] =
******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

constexpr char kWrongPrivateKey[] =
******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

constexpr char kUserCert[] =
    "-----BEGIN CERTIFICATE-----\n"
    "MIIDazCCAlOgAwIBAgIUVLGPFfkgeQbMUT0k/FX3sL101dAwDQYJKoZIhvcNAQEL\n"
    "BQAwRTELMAkGA1UEBhMCQVUxEzARBgNVBAgMClNvbWUtU3RhdGUxITAfBgNVBAoM\n"
    "GEludGVybmV0IFdpZGdpdHMgUHR5IEx0ZDAeFw0yMjAyMTYxMzIwMjdaFw0yMjAz\n"
    "MTgxMzIwMjdaMEUxCzAJBgNVBAYTAkFVMRMwEQYDVQQIDApTb21lLVN0YXRlMSEw\n"
    "HwYDVQQKDBhJbnRlcm5ldCBXaWRnaXRzIFB0eSBMdGQwggEiMA0GCSqGSIb3DQEB\n"
    "AQUAA4IBDwAwggEKAoIBAQDXt3N1RS4Ntj7oHucHcqO+nRuf/3dK1zDxhtnyrjBF\n"
    "BuhTTvNOL2Njm/LvB5EaaIc2UhavIPnnQEAtOmhvEsi8A3HB4EU+Pu6UJnUtmEPM\n"
    "C/9WTZnLYAA/gMKYZ8KPZQ1FqNi3pkeWcxTNtwnTDEK4qtd6+1veqWTYuxU6IUNr\n"
    "E4GX1yhSV4fAq6PKqdTz7VLIZ/wasADMMZ/So3/MDR7rHH9hVBPby6liEunXUjzT\n"
    "7L5t+ZN3vUejOlcqdfikuB73oPitZ1vfQ3Ux2+67hWMLXFrw+4JkBMxyHL2fLfGw\n"
    "czRMy82UVIgqJMYAOM5iTQeQcxVlqwAyEhFUXSXSF4PFAgMBAAGjUzBRMB0GA1Ud\n"
    "DgQWBBS1UScRUjNDA88mUXUuzwOJ35aCnDAfBgNVHSMEGDAWgBS1UScRUjNDA88m\n"
    "UXUuzwOJ35aCnDAPBgNVHRMBAf8EBTADAQH/MA0GCSqGSIb3DQEBCwUAA4IBAQCf\n"
    "gcfXjLfaOots4AFtd4sQglWA0RaIfaZl1QTAp6QvJQY7jFyyeNuYV3DfT2DCzJOY\n"
    "NwRluNCfUZG/YbpTtMUDODpDjASF0z9kQc1bVg3NdcscI+LFtMivuvG7v3Bp7G3I\n"
    "bhnbhDmWRU9Wss4P3F7x2EULX6NwUzcmyHtEQ+A9xjm6BpshCx3qZNEOYFqV7U82\n"
    "ioxoifZ5JDF7fIkF22rsI+Ufo3mMvjYz5vSRxc0OVmbpfgmnhB5ValEAopwP5FN3\n"
    "Jn8C5u+DExI0s93xzNMmJL0ONVeQORY8YkV+7E8wuD+VLSuoo4S/VZ108DRl+kxx\n"
    "e8AkIK+5yjLpD/0P4TN0\n"
    "-----END CERTIFICATE-----";

class ImportDoneWaiter
    : public base::test::TestFuture<std::optional<std::string>,
                                    std::optional<int>> {
 public:
  CertManager::ImportPrivateKeyAndCertCallback GetCallback() {
    return TestFuture::GetCallback<const std::optional<std::string>&,
                                   const std::optional<int>&>();
  }

  std::optional<std::string> imported_cert_id() { return Get<0>(); }
  std::optional<int> imported_slot_id() { return Get<1>(); }
};

}  // namespace

class CertManagerImplTest : public testing::Test {
 public:
  CertManagerImplTest() = default;

  CertManagerImplTest(const CertManagerImplTest&) = delete;
  CertManagerImplTest& operator=(const CertManagerImplTest&) = delete;

  ~CertManagerImplTest() override = default;

  void SetUp() override {
    profile_ = std::make_unique<TestingProfile>();
    cert_manager_ = std::make_unique<CertManagerImpl>(profile());
    nss_db_ = std::make_unique<crypto::ScopedTestNSSDB>();
    cert_db_ = std::make_unique<net::NSSCertDatabase>(
        crypto::ScopedPK11Slot(PK11_ReferenceSlot(nss_db_->slot())),
        crypto::ScopedPK11Slot(PK11_ReferenceSlot(nss_db_->slot())));
  }

  void TearDown() override {
    // Ensure that nothing is running before tearing down.
    base::RunLoop().RunUntilIdle();

    profile_.reset();
    cert_manager_.reset();
    cert_db_.reset();
    nss_db_.reset();
  }

  Profile* profile() { return profile_.get(); }
  CertManagerImpl* cert_manager() { return cert_manager_.get(); }
  net::NSSCertDatabase* cert_db() { return cert_db_.get(); }

 private:
  std::unique_ptr<TestingProfile> profile_;
  std::unique_ptr<CertManagerImpl> cert_manager_;

  std::unique_ptr<crypto::ScopedTestNSSDB> nss_db_;
  std::unique_ptr<net::NSSCertDatabase> cert_db_;

  content::BrowserTaskEnvironment task_environment_;
};

// Imports with a valid certificate and key succeed.
TEST_F(CertManagerImplTest, ImportKeyAndCertTest) {
  ImportDoneWaiter import_future;
  cert_manager()->ImportPrivateKeyAndCertWithDB(
      kPrivateKey, kUserCert, import_future.GetCallback(), cert_db());
  EXPECT_TRUE(import_future.Wait());
  EXPECT_TRUE(import_future.imported_cert_id().has_value());
  EXPECT_TRUE(import_future.imported_slot_id().has_value());

  // Assert that the imported key and certificate have the same ID.
  bssl::PEMTokenizer tokenizer(kUserCert, {kCertificatePEMHeader});
  EXPECT_TRUE(tokenizer.GetNext());
  std::vector<uint8_t> cert_der(tokenizer.data().begin(),
                                tokenizer.data().end());
  net::ScopedCERTCertificate cert(
      net::x509_util::CreateCERTCertificateFromBytes(cert_der));

  // Get the certificate ID.
  crypto::ScopedSECItem cert_sec_item(
      PK11_GetLowLevelKeyIDForCert(nullptr, cert.get(), nullptr));
  EXPECT_TRUE(cert_sec_item);
  std::string cert_id =
      base::HexEncode(cert_sec_item->data, cert_sec_item->len);

  // Get the key ID.
  crypto::ScopedPK11Slot private_slot = cert_db()->GetPrivateSlot();
  EXPECT_TRUE(private_slot);
  crypto::ScopedSECKEYPrivateKey key(
      PK11_FindPrivateKeyFromCert(private_slot.get(), cert.get(), nullptr));
  EXPECT_TRUE(key);
  crypto::ScopedSECItem key_sec_item(
      PK11_GetLowLevelKeyIDForPrivateKey(key.get()));
  EXPECT_TRUE(key_sec_item);
  std::string key_id = base::HexEncode(key_sec_item->data, key_sec_item->len);

  EXPECT_EQ(key_id, cert_id);
  EXPECT_EQ(import_future.imported_cert_id().value(), cert_id);
  EXPECT_EQ(import_future.imported_slot_id().value(),
            static_cast<int>(PK11_GetSlotID(private_slot.get())));
}

// Importing a certificate with the wrong key fail.
TEST_F(CertManagerImplTest, ImportCertWithWrongKeyTest) {
  ImportDoneWaiter import_future;
  cert_manager()->ImportPrivateKeyAndCertWithDB(
      kWrongPrivateKey, kUserCert, import_future.GetCallback(), cert_db());
  EXPECT_TRUE(import_future.Wait());
  EXPECT_FALSE(import_future.imported_cert_id().has_value());
  EXPECT_FALSE(import_future.imported_slot_id().has_value());
}

// Imports with invalid certificate database fail.
TEST_F(CertManagerImplTest, ImportKeyAndCertWithInvalidDBTest) {
  ImportDoneWaiter import_future;
  cert_manager()->ImportPrivateKeyAndCertWithDB(kPrivateKey, kUserCert,
                                                import_future.GetCallback(),
                                                /*database=*/nullptr);
  EXPECT_TRUE(import_future.Wait());
  EXPECT_FALSE(import_future.imported_cert_id().has_value());
  EXPECT_FALSE(import_future.imported_slot_id().has_value());
}

// Imports with invalid certificates or keys fail.
TEST_F(CertManagerImplTest, ImportInvalidDataTest) {
  ImportDoneWaiter import_future;
  cert_manager()->ImportPrivateKeyAndCertWithDB(
      /*key_pem=*/"", /*cert_pem=*/"", import_future.GetCallback(), cert_db());
  EXPECT_TRUE(import_future.Wait());
  EXPECT_FALSE(import_future.imported_cert_id().has_value());
  EXPECT_FALSE(import_future.imported_slot_id().has_value());
}

}  // namespace arc
