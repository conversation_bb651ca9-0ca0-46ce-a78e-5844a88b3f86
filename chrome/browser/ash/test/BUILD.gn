# Copyright 2024 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/chromeos/ui_mode.gni")

assert(is_chromeos)

static_library("test_support") {
  testonly = true

  sources = [
    "child_logged_in_browser_test_mixin.cc",
    "child_logged_in_browser_test_mixin.h",
    "kiosk_app_logged_in_browser_test_mixin.cc",
    "kiosk_app_logged_in_browser_test_mixin.h",
    "public_account_logged_in_browser_test_mixin.cc",
    "public_account_logged_in_browser_test_mixin.h",
    "regular_logged_in_browser_test_mixin.cc",
    "regular_logged_in_browser_test_mixin.h",
    "web_kiosk_app_logged_in_browser_test_mixin.cc",
    "web_kiosk_app_logged_in_browser_test_mixin.h",
  ]

  deps = [
    "//ash/constants",
    "//base",
    "//chrome/test:test_support_ui",
    "//chromeos/ash/components/policy/device_local_account",
    "//chromeos/ash/components/settings",
    "//chromeos/ash/components/settings:test_support",
    "//components/account_id",
    "//components/policy/proto",
    "//components/prefs",
    "//components/user_manager:test_support",
  ]
}

source_set("browser_tests") {
  testonly = true

  defines = [ "HAS_OUT_OF_PROC_TEST_RUNNER" ]

  sources = [ "device_restriction_schedule_controller_browsertest.cc" ]

  deps = [
    "//ash/constants",
    "//base",
    "//base/test:test_support",
    "//chrome/browser",
    "//chrome/browser:browser_process",
    "//chrome/browser/ash/login:test_support",
    "//chrome/browser/lifetime:termination_notification",
    "//chrome/browser/ui/webui/ash/login",
    "//chromeos/ash/components/policy",
    "//chromeos/ash/components/policy/weekly_time:test_support",
    "//chromeos/strings",
    "//components/policy/proto",
    "//content/test:test_support",
    "//ui/base",
    "//ui/message_center",
  ]
}
