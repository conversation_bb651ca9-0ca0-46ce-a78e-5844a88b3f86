include_rules = [
  # ChromeOS should not depend on //chrome. See //docs/chromeos/code.md for
  # details.
  "-chrome",

  # This directory is in //chrome, which violates the rule above. Allow this
  # directory to #include its own files.
  "+chrome/browser/ash/accessibility/live_caption",

  # Existing dependencies within //chrome. There is an active effort to
  # refactor //chrome/browser/ash to break these dependencies; see b/332804822.
  # Whenever possible, avoid adding new //chrome dependencies to this list.
  #
  # Files residing in certain directories (e.g., //chrome/browser) are listed
  # individually. Other dependencies within //chrome are listed on a per-
  # directory basis. See //tools/chromeos/gen_deps.sh for details.
  "+chrome/browser/accessibility",
  "+chrome/browser/accessibility/live_caption",
  "+chrome/browser/ash/login/session",
  "+chrome/browser/profiles",
  "+chrome/browser/speech",
  "+chrome/browser/ui/browser.h",
  "+chrome/browser/ui/browser_window.h",
  "+chrome/browser/ui/settings_window_manager_chromeos.h",
  "+chrome/test/base",
]

specific_include_rules = {
  ".*browsertest\.cc": [
    "+chrome/browser/browser_process.h",
  ],
}
