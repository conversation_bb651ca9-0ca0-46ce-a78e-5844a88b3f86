include_rules = [
  # Existing dependencies within //chrome. There is an active effort to
  # refactor //chrome/browser/ash to break these dependencies; see b/332804822.
  # Whenever possible, avoid adding new //chrome dependencies to this list.
  #
  # Files residing in certain directories (e.g., //chrome/browser) are listed
  # individually. Other dependencies within //chrome are listed on a per-
  # directory basis. See //tools/chromeos/gen_deps.sh for details.
  "+chrome/browser/apps/app_service",
  "+chrome/browser/ash/crostini",
  "+chrome/browser/ash/drive",
  "+chrome/browser/ash/extensions/file_manager",
  "+chrome/browser/ash/fileapi",
  "+chrome/browser/ash/file_manager",
  "+chrome/browser/ash/file_system_provider",
  "+chrome/browser/ash/login/users",
  "+chrome/browser/ash/policy/core",
  "+chrome/browser/ash/policy/login",
  "+chrome/browser/ash/system_web_apps",
  "+chrome/browser/chromeos/policy/dlp",
  "+chrome/browser/enterprise/connectors",
  "+chrome/browser/enterprise/data_controls",
  "+chrome/browser/extensions/api/file_system",
  "+chrome/browser/extensions/api/tab_capture",
  "+chrome/browser/file_select_helper.h",
  "+chrome/browser/media",
  "+chrome/browser/notifications",
  "+chrome/browser/policy/messaging_layer/public",
  "+chrome/browser/profiles",
  "+chrome/browser/ui/ash",
  "+chrome/browser/ui/browser.h",
  "+chrome/browser/ui/browser_list.h",
  "+chrome/browser/ui/browser_list_observer.h",
  "+chrome/browser/ui/browser_window.h",
  "+chrome/browser/ui/tabs",
  "+chrome/browser/ui/webui/ash/cloud_upload",
  "+chrome/common/chrome_features.h",
  "+chrome/common/chrome_paths.h",
  "+chrome/common/extensions",
  "+chrome/grit",
]

specific_include_rules = {
  ".*test(_.*)?\\.(cc|h)": [
    "+chrome/test/base",
  ],
  ".*browsertest\\.cc": [
    "+chrome/browser/browser_process.h",
    "+chrome/browser/ui/browser_commands.h",
  ],
}
