include_rules = [
  # ChromeOS should not depend on //chrome. See //docs/chromeos/code.md for
  # details.
  "-chrome",

  # This directory is in //chrome, which violates the rule above. Allow this
  # directory to #include its own files.
  "+chrome/browser/ash/auth",

  # Existing dependencies within //chrome. There is an active effort to
  # refactor //chrome/browser/ash to break these dependencies; see b/332804822.
  # Whenever possible, avoid adding new //chrome dependencies to this list.
  "+chrome/browser/ash/login/quick_unlock",
  "+chrome/browser/ash/login/users",
  "+chrome/browser/ash/profiles",
  "+chrome/browser/profiles",
]
