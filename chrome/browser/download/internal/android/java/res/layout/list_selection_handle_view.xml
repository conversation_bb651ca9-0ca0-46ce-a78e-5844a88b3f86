<?xml version="1.0" encoding="utf-8"?>
<!--
Copyright 2018 The Chromium Authors
Use of this source code is governed by a BSD-style license that can be
found in the LICENSE file.
-->

 <merge
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <org.chromium.ui.widget.ChromeImageView
        android:id="@+id/check"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="center"
        android:background="@drawable/list_item_icon_modern_bg"
        android:visibility="gone"
        app:tint="@macro/default_icon_color_inverse"
        app:layout_gravity="center"
        tools:ignore="ContentDescription" />

    <org.chromium.ui.widget.ChromeImageView
        android:id="@+id/circle"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:background="@null"
        android:src="@drawable/circle_white"
        android:visibility="gone"
        app:tint="@null"
        tools:ignore="ContentDescription"/>

</merge>
