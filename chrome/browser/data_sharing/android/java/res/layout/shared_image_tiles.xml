<?xml version="1.0" encoding="utf-8"?>
<!--
Copyright 2024 The Chromium Authors
Use of this source code is governed by a BSD-style license that can be
found in the LICENSE file.
-->

<org.chromium.chrome.browser.data_sharing.ui.shared_image_tiles.SharedImageTilesView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/dialog_data_sharing_shared_image_tiles"
    android:layout_height="wrap_content"
    android:layout_width="wrap_content"
    android:paddingEnd="@dimen/shared_image_tiles_overlap_margin"
    android:clipToPadding="false"
    android:orientation="horizontal">

    <LinearLayout
        android:id="@+id/last_tile_container"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/shared_image_tiles_icon_total_height"
        android:layout_marginEnd="@dimen/shared_image_tiles_overlap_margin_negative"
        android:minWidth="@dimen/shared_image_tiles_icon_total_height"
        android:background="@drawable/round_image_filled"
        android:orientation="vertical"
        android:gravity="center"
        android:visibility="gone" >

        <TextView
            android:id="@+id/tiles_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingStart="@dimen/shared_image_tiles_text_padding"
            android:paddingEnd="@dimen/shared_image_tiles_text_padding"
            android:maxHeight="@dimen/shared_image_tiles_icon_total_height"
            android:textAppearance="@style/TextAppearance.TextAccentMediumThick.Primary"
            android:visibility="gone"/>

    </LinearLayout>

</org.chromium.chrome.browser.data_sharing.ui.shared_image_tiles.SharedImageTilesView>
