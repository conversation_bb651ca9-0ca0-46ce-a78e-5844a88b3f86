# Copyright 2021 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//ui/webui/resources/tools/build_webui.gni")

assert(!is_android, "access_code_cast is not for android.")

build_webui("build") {
  grd_prefix = "access_code_cast"

  static_files = [ "index.html" ]

  web_component_files = [
    "access_code_cast.ts",
    "error_message/error_message.ts",
    "passcode_input/passcode_input.ts",
  ]

  ts_files = [ "browser_proxy.ts" ]

  mojo_files_deps = [
    "//chrome/browser/ui/webui/access_code_cast:mojo_bindings_ts__generator",
    "//components/media_router/common/mojom:route_request_result_code_ts__generator",
  ]
  mojo_files = [
    "$root_gen_dir/chrome/browser/ui/webui/access_code_cast/access_code_cast.mojom-webui.ts",
    "$root_gen_dir/components/media_router/common/mojom/route_request_result_code.mojom-webui.ts",
  ]

  ts_composite = true
  ts_deps = [
    "//third_party/polymer/v3_0:library",
    "//ui/webui/resources/cr_components/color_change_listener:build_ts",
    "//ui/webui/resources/cr_elements:build_ts",
    "//ui/webui/resources/js:build_ts",
    "//ui/webui/resources/mojo:build_ts",
  ]
  webui_context_type = "trusted"
}
