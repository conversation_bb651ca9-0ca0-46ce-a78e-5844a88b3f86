<ntp-module-header-v2
    header-text="${this.title}"
    .menuItemGroups="${this.getMenuItemGroups_()}"
    more-actions-text="${this.i18nRecursive(
        '', 'modulesMoreActions', 'modulesDummyLower')}"
    @disable-button-click="${this.onDisableButtonClick_}">
</ntp-module-header-v2>
<div id="moduleContent">
  <cr-grid id="tiles" columns="2">
    ${this.tiles.map(item => html`
      <div class="tile-item" title="${item.label}">
        <img is="cr-auto-img" .autoSrc="${item.imageUrl}"></img>
        <span>${item.value}</span>
      </div>
    `)}
  </cr-grid>
</div>
