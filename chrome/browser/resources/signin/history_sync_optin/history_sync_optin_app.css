/* Copyright 2025 The Chromium Authors
 * Use of this source code is governed by a BSD-style license that can be
 * found in the LICENSE file. */

/* #css_wrapper_metadata_start
 * #type=style-lit
 * #import=//resources/cr_elements/cr_shared_style_lit.css.js
 * #import=//resources/cr_elements/cr_shared_vars.css.js
 * #scheme=relative
 * #include=cr-shared-style-lit
 * #css_wrapper_metadata_end */

:host {
  border-radius: 12px;
  color: var(--cr-primary-text-color);
  display: block;
  overflow: hidden;
}

#contentContainer {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 20px;
}

#avatar {
  /** The user avatar may be transparent, add a background */
  background-color: var(--md-background-color);
  border: solid var(--md-background-color);
  border-radius: 50%;
  height: 64px;
  /* To place the avatar at the center of the background image,
     place it at top left point of its parent,
     then shift it back by half its width and height. */
  left: 50%;
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 64px;
}

.dialog-illustration {
  content: url(./images/avatar_surrounding_illustration_light.svg);
  width: 100%;
}

#imageContainer {
  align-self: center;
  display: flex;
  /* The height must be set so that the height of this relatively positioned
  container is taken into account in the height calculator of the entire screen. */
  height: 162px;
  justify-content: center;
  /* Establishes a positioning context for absolute children. */
  position: relative;
}

#buttonRow {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

#modalDescription {
  /* colors */
  background-color: var(--cr-fallback-color-neutral-container);
  border-top: 1px solid var(--cr-fallback-color-neutral-outline);
  /* fonts */
  font-size: 11px;
  line-height: 20px;
  /* layout */
  padding: 12px 20px 12px 20px;
}

h1 {
  font-size: 16px;
  font-weight: 500;
}

@media (prefers-color-scheme: dark) {
  .dialog-illustration {
   content: url(./images/avatar_surrounding_illustration_dark.svg);
  }
}
