    <style include="cr-shared-style settings-shared">
      :host {
        display: block;
      }

      .column-header {
        margin-bottom: 15px;
        margin-inline-start: 20px;
        margin-top: 15px;
      }
      #radioGroup {
        padding: 0 var(--cr-section-padding);
      }

      #radioGroupSubHeading {
        padding-bottom: 10px;
      }

      #appHandlerSubHeading {
        padding-bottom: 10px;
      }

      #appIcon {
        width: 16px;
        height: 16px;
        background-repeat: no-repeat;
        background-size: contain;
      }

      settings-collapse-radio-button {
        --settings-collapse-toggle-min-height: var(--cr-section-min-height);
      }

      settings-collapse-radio-button:not(:first-of-type) {
        --settings-collapse-separator-line: var(--cr-separator-line);
      }
    </style>
    <div id="radioGroup">
      <div class="secondary">
        $i18n{siteSettingsProtocolHandlersDescription}
      </div>
      <h2>$i18n{siteSettingsDefaultBehavior}</h2>
      <div id="radioSubHeading" class="secondary">
        $i18n{siteSettingsDefaultBehaviorDescription}
      </div>
      <settings-radio-group
        id="protcolHandlersRadio"
        pref="[[handlersEnabledPref_]]"
        on-change="onToggleChange_"
        selectable-elements="settings-collapse-radio-button">
        <settings-collapse-radio-button no-collapse
            id="protcolHandlersRadioAllow"
            pref="[[handlersEnabledPref_]]"
            label="$i18n{siteSettingsProtocolHandlersAllowed}"
            name="true"
            disabled$="[[isGuest_]]"
            icon="privacy:protocol-handler">
        </settings-collapse-radio-button>
        <settings-collapse-radio-button no-collapse
            id="protcolHandlersRadioBlock"
            pref="[[handlersEnabledPref_]]"
            label="$i18n{siteSettingsProtocolHandlersBlocked}"
            name="false"
            disabled$="[[isGuest_]]"
            icon="privacy:protocol-handler-off">
        </settings-collapse-radio-button>
      </settings-radio-group>
    </div>

    <template is="dom-repeat" items="[[protocols]]" as="protocol">
      <div class="column-header">[[protocol.protocol_display_name]]</div>

      <div class="list-frame menu-content vertical-list">
        <template is="dom-repeat" items="[[protocol.handlers]]">

          <div class="list-item">
            <site-favicon url="[[item.host]]"></site-favicon>
            <div class="middle" >
              <div class="protocol-host">
                <span class="url-directionality">[[item.host]]</span>
              </div>
              <div class="secondary protocol-default"
                  hidden$="[[!item.is_default]]">
                $i18n{handlerIsDefault}
              </div>
            </div>

            <cr-icon-button class="icon-more-vert" on-click="showMenu_"
                title="$i18n{moreActions}"></cr-icon-button>
          </div>
        </template>
      </div>
    </template>

    <cr-action-menu role-description="$i18n{menu}">
      <button class="dropdown-item" on-click="onDefaultClick_"
          id="defaultButton" disabled$="[[actionMenuModel_.is_default]]">
        $i18n{handlerSetDefault}
      </button>
      <button class="dropdown-item" on-click="onRemoveClick_"
          id="removeButton">
        $i18n{handlerRemove}
      </button>
    </cr-action-menu>

    <template is="dom-if" if="[[ignoredProtocols.length]]">
      <div class="column-header">
        $i18n{siteSettingsProtocolHandlersBlockedExceptions}
      </div>
      <div class="list-frame menu-content vertical-list">
        <template is="dom-repeat" items="[[ignoredProtocols]]">
          <div class="list-item">
            <site-favicon url="[[item.host]]"></site-favicon>
            <div class="middle" >
              <div class="protocol-host">
                <span class="url-directionality">[[item.host]]</span></div>
              <div class="secondary protocol-protocol">
                [[item.protocol_display_name]]
              </div>
            </div>
            <cr-icon-button class="icon-clear" id="removeIgnoredButton"
                on-click="onRemoveIgnored_" title="$i18n{handlerRemove}">
            </cr-icon-button>
          </div>
        </template>
      </div>
    </template>

    <div class="column-header" hidden$="[[!showAppsProtocolHandlersTitle_]]">
      <h2>$i18n{siteSettingsAppProtocolHandlers}</h2>
    </div>

    <div class="column-header" hidden$="[[!appAllowedProtocols.length]]">
      <div id="appHandlerSubHeading" class="secondary">
        $i18n{siteSettingsAppAllowedProtocolHandlersDescription}
      </div>
    </div>

    <template is="dom-repeat" items="[[appAllowedProtocols]]" as="appProtocol">
      <div class="column-header">[[appProtocol.protocol_display_name]]</div>
      <div class="list-frame menu-content vertical-list">
        <template is="dom-repeat" items="[[appProtocol.handlers]]">
          <div class="list-item">
            <div id="appIcon"
                style="background-image: url('chrome://app-icon/[[item.app_id]]/16')">
            </div>
            <div class="middle protocol-app-name">
              <span class$="[[getNameCssClass_(item)]]">
                [[getNameText_(item)]]
              </span>
            </div>
            <cr-icon-button class="icon-clear" id="removeAppHandlerButton"
                on-click="onRemoveAppAllowedHandlerButtonClick_"
                title="$i18n{handlerRemove}">
            </cr-icon-button>
          </div>
        </template>
      </div>
    </template>

    <div class="column-header" hidden$="[[!appDisallowedProtocols.length]]">
      <div id="appHandlerSubHeading" class="secondary">
        $i18n{siteSettingsAppDisallowedProtocolHandlersDescription}
      </div>
    </div>

    <template is="dom-repeat" items="[[appDisallowedProtocols]]"
        as="appProtocol">
      <div class="column-header">[[appProtocol.protocol_display_name]]</div>
      <div class="list-frame menu-content vertical-list">
        <template is="dom-repeat" items="[[appProtocol.handlers]]">
          <div class="list-item">
            <div id="appIcon"
                style="background-image: url('chrome://app-icon/[[item.app_id]]/16')">
            </div>
            <div class="middle protocol-app-name">
              <span class$="[[getNameCssClass_(item)]]">
                [[getNameText_(item)]]
              </span>
            </div>
            <cr-icon-button class="icon-clear" id="removeAppHandlerButton"
                on-click="onRemoveAppDisallowedHandlerButtonClick_"
                title="$i18n{handlerRemove}">
            </cr-icon-button>
          </div>
        </template>
      </div>
    </template>
