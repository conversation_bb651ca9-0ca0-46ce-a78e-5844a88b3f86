<style include="cr-hidden-style settings-shared">
  :host {
    align-items: center;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    padding-bottom: 16px;
    padding-inline-end: 16px;
    padding-inline-start: 16px;
  }

  #mainPageContainer {
    /* Main page container should take up all available vertical space. */
    flex: 1;
    max-width: 958px;
    min-width: 640px;
    width: 100%;
  }

  #managedHeader {
    border-top: none;
    font: var(--cros-body-2-font);
    margin-bottom: 8px;
    padding-bottom: 14px;
    padding-top: 14px;
    /* The next element spills over this element. This ensures the link
      * is clickable. */
    position: relative;
    z-index: 1;
    --cr-link-color: var(--cros-sys-primary);
    --cr-secondary-text-color: var(--cros-sys-secondary);
    --iron-icon-fill-color: var(--cros-sys-secondary);
  }
</style>
<template is="dom-if" if="[[!isShowingSubpage_]]" restamp>
  <managed-footnote id="managedHeader" show-device-info></managed-footnote>
</template>
<main-page-container id="mainPageContainer"
    prefs="{{prefs}}"
    page-availability="[[pageAvailability]]">
</main-page-container>
