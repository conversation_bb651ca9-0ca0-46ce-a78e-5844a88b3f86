<style include="settings-shared">
  cr-dialog::part(dialog) {
    height: 420px;
    width: 512px;
  }
</style>
<cr-dialog id="dialog" show-on-attach>
  <div id="content" slot="body" aria-live="polite">
    <cr-view-manager id="viewManager">
      <nearby-share-high-visibility-page id="[[PageEnum_.HIGH_VISIBILITY]]"
          slot="view" device-name="[[settings.deviceName]]"
          shutoff-timestamp="[[highVisibilityShutoffTimestamp_]]"
          register-result="[[registerForegroundReceiveSurfaceResult_]]"
          nearby-process-stopped="[[nearbyProcessStopped_]]"
          start-advertising-failed="[[startAdvertisingFailed_]]">
      </nearby-share-high-visibility-page>
      <nearby-share-confirm-page id="[[PageEnum_.CONFIRM]]" slot="view"
          share-target="[[shareTarget]]"
          connection-token="[[connectionToken]]"
          transfer-status="[[transferStatus_]]">
      </nearby-share-confirm-page>
      <nearby-onboarding-one-page id="[[PageEnum_.ONEPAGE_ONBOARDING]]"
          slot="view" settings="{{settings}}">
      </nearby-onboarding-one-page>
      <nearby-visibility-page id="[[PageEnum_.VISIBILITY]]" slot="view"
          settings="{{settings}}">
      </nearby-visibility-page>
    </cr-view-manager>
  </div>
</cr-dialog>
