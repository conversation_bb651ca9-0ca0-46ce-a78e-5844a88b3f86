# Copyright 2019 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

# Test support target that contains only public mocks and fakes.
source_set("test_support") {
  testonly = true

  sources = [
    "mock_display_agent.cc",
    "mock_display_agent.h",
    "mock_notification_background_task_scheduler.cc",
    "mock_notification_background_task_scheduler.h",
    "mock_notification_schedule_service.cc",
    "mock_notification_schedule_service.h",
    "mock_notification_scheduler_client.cc",
    "mock_notification_scheduler_client.h",
  ]

  deps = [
    "//base",
    "//chrome/browser/notifications/scheduler/public",
    "//skia",
    "//testing/gmock",
    "//testing/gtest",
  ]
}

# Test library that is used internally and can't be exposed to the embedder.
source_set("test_lib") {
  testonly = true
  visibility = [
    "//chrome/browser/notifications/scheduler/internal:unit_tests",
    "//chrome/browser/notifications/scheduler/public:unit_tests",
  ]

  sources = [
    "fake_clock.cc",
    "fake_clock.h",
    "mock_background_task_coordinator.cc",
    "mock_background_task_coordinator.h",
    "mock_display_decider.cc",
    "mock_display_decider.h",
    "mock_impression_history_tracker.cc",
    "mock_impression_history_tracker.h",
    "mock_scheduled_notification_manager.cc",
    "mock_scheduled_notification_manager.h",
    "test_utils.cc",
    "test_utils.h",
  ]

  deps = [
    "//base",
    "//chrome/browser/notifications/scheduler/internal:lib",
    "//chrome/browser/notifications/scheduler/public",
    "//skia",
    "//testing/gmock",
    "//testing/gtest",
  ]
}
