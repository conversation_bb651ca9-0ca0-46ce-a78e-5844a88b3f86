// Copyright 2019 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#include "chrome/browser/notifications/scheduler/test/mock_background_task_coordinator.h"

namespace notifications {
namespace test {

MockBackgroundTaskCoordinator::MockBackgroundTaskCoordinator() = default;

MockBackgroundTaskCoordinator::~MockBackgroundTaskCoordinator() = default;

}  // namespace test
}  // namespace notifications
