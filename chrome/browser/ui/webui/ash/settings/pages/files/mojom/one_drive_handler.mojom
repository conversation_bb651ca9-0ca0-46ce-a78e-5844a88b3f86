// Copyright 2023 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

module ash.settings.one_drive.mojom;

// Lives in the browser process. A renderer uses this to create a page handler
// that enables communication between a renderer and the browser process.
interface PageHandlerFactory {
  // Creates a PageHandler and connects it up to the Page.
  CreatePageHandler(pending_remote<Page> page,
                    pending_receiver<PageHandler> handler);
};

// Lives in the browser process. A renderer uses this to invoke methods that
// are implemented in the browser process.
interface PageHandler {
  // Returns the email address associated with the currently connected OneDrive
  // account.
  GetUserEmailAddress() => (string? email);

  // Emits a OneDrive mount request and returns whether the request succeeded.
  ConnectToOneDrive() => (bool success);

  // Emits a OneDrive unmount request and returns whether the request succeeded.
  DisconnectFromOneDrive() => (bool success);

  // Opens Files app on OneDrive folder location.
  OpenOneDriveFolder() => (bool success);
};

// Interface for the OneDrive settings. Implemented in Javascript and
// used by the page handler to send asynchronous updates.
interface Page {
  // Invoked on ODFS mount or unmount, indicating that the user has signed in
  // or out of OneDrive.
  OnODFSMountOrUnmount();

  // Invoked whenever AllowUserToRemoveODFS pref is updated, indicating
  // whether the user is allowed to remove OneDrive.
  OnAllowUserToRemoveODFSChanged(bool is_allowed);
};
