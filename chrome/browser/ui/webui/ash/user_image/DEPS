include_rules = [
  # ChromeOS should not depend on //chrome. See //docs/chromeos/code.md for
  # details.
  "-chrome",

  # This directory is in //chrome, which violates the rule above. Allow this
  # directory to #include its own files.
  "+chrome/browser/ui/webui/ash/user_image",

  # Existing dependencies within //chrome. There is an active effort to
  # refactor //chrome/browser/ui/ash to break these dependencies; see b/332804822.
  # Whenever possible, avoid adding new //chrome dependencies to this list.
  "+chrome/browser/ash/login/users/default_user_image",
  "+chrome/browser/browser_process.h",
  "+chrome/common",
]
