include_rules = [
  # ChromeOS should not depend on //chrome. See //docs/chromeos/code.md for
  # details.
  "-chrome",

  # This directory is in //chrome, which violates the rule above. Allow this
  # directory to #include its own files.
  "+chrome/browser/ui/webui/ash/assistant_optin",

  # Existing dependencies within //chrome. There is an active effort to
  # refactor //chrome/browser/ui/ash to break these dependencies; see b/332804822.
  # Whenever possible, avoid adding new //chrome dependencies to this list.
  "+chrome/browser/ash/assistant",
  "+chrome/browser/consent_auditor",
  "+chrome/browser/profiles",
  "+chrome/browser/signin",
  "+chrome/browser/ui/ash/login",
  "+chrome/browser/ui/views/chrome_web_dialog_view.h",
  "+chrome/browser/ui/webui/ash/login",
  "+chrome/browser/ui/webui/ash/system_web_dialog",
  "+chrome/browser/ui/webui/webui_util.h",
  "+chrome/common",
  "+chrome/grit",
]
