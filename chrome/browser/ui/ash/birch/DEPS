include_rules = [
  # ChromeOS should not depend on //chrome. See //docs/chromeos/code.md for
  # details.
  "-chrome",

  # This directory is in //chrome, which violates the rule above. Allow this
  # directory to #include its own files.
  "+chrome/browser/ui/ash/birch",

  # Existing dependencies within //chrome. There is an active effort to
  # refactor ash codes in //chrome to break these dependencies; see b/332804822.
  # Whenever possible, avoid adding new //chrome dependencies to this list.
  "+chrome/browser/ash/app_list/search",
  "+chrome/browser/ash/app_restore",
  "+chrome/browser/ash/calendar",
  "+chrome/browser/ash/crosapi",
  "+chrome/browser/ash/file_suggest",
  "+chrome/browser/ash/login/users",
  "+chrome/browser/ash/release_notes",
  "+chrome/browser/ash/video_conference",
  "+chrome/browser/favicon",
  "+chrome/browser/history",
  "+chrome/browser/prefs",
  "+chrome/browser/profiles",
  "+chrome/browser/signin",
  "+chrome/browser/sync",
  "+chrome/browser/ui/ash/holding_space",
  "+chrome/browser/ui/ash/main_extra_parts",
  "+chrome/common",
  "+chrome/grit",
  "+chrome/test",
]

specific_include_rules = {
  ".*test\.cc": [
    "+chrome/browser/apps/platform_apps/app_browsertest_util.h",
    "+chrome/browser/ash/system_web_apps/system_web_app_manager.h",
    "+chrome/browser/global_features.h",
    "+chrome/browser/ui/browser.h",
    "+chrome/browser/ui/browser_list.h",
    "+gmock/gmock.h",
  ],
}
