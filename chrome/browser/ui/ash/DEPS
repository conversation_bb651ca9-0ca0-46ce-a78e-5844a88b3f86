include_rules = [
  # //chrome/browser/ui/ash is conceptually part of "ash".
  "+ash",

  # ChromeOS should not depend on //chrome. See //docs/chromeos/code.md for
  # details.
  "-chrome",

  # Browser abstraction for use by ChromeOS feature code. Will eventually be
  # moved out of chrome/.
  "+chrome/browser/ash/browser_delegate/browser_controller.h",
  "+chrome/browser/ash/browser_delegate/browser_delegate.h",
  "+chrome/browser/ash/browser_delegate/browser_type.h"
]

specific_include_rules = {
  ".*test.*": [
    "+chrome/test",
    "+chrome/browser/ui/views/tabs",
    "+components/user_education/views",
    "+components/viz/test",
    "+media",
    "+ui/message_center",
  ],

  "test_util\.*": [
    "+ash",
    "+chrome/browser/ash/crosapi",
    "+chrome/browser/ui/ash",
    "+chrome/browser/ui/browser_commands.h",
    "+chrome/browser/ui/browser.h",
    "+chrome/browser/ui/exclusive_access",
    "+chrome/browser/ui/views",
    "+chrome/test/base",
  ],
}
