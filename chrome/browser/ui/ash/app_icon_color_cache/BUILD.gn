# Copyright 2024 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

assert(is_chromeos)

static_library("app_icon_color_cache") {
  sources = [
    "app_icon_color_cache.cc",
    "app_icon_color_cache.h",
  ]

  deps = [
    "//ash/constants",
    "//ash/public/cpp",
    "//base",
    "//chrome/browser/profiles:profile",
    "//components/prefs",
    "//services/preferences/public/cpp",
    "//ui/gfx",
  ]
}

source_set("unit_tests") {
  testonly = true

  sources = [ "app_icon_color_cache_unittest.cc" ]

  deps = [
    ":app_icon_color_cache",
    "//ash/public/cpp",
    "//base",
    "//base/test:test_support",
    "//chrome/test:test_support",
    "//content/test:test_support",
    "//ui/gfx",
  ]
}
