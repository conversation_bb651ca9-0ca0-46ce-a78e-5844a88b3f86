include_rules = [
  # ChromeOS should not depend on //chrome. See //docs/chromeos/code.md for
  # details.
  "-chrome",

  # This directory is in //chrome, which violates the rule above. Allow this
  # directory to #include its own files.
  "+chrome/browser/ui/ash/game_dashboard",

  # Existing dependencies within //chrome. There is an active effort to
  # refactor ash codes in //chrome to break these dependencies; see b/332804822.
  # Whenever possible, avoid adding new //chrome dependencies to this list.
  "+chrome/browser/apps/app_service/metrics",
  "+chrome/browser/ash/app_list/arc",
  "+chrome/browser/profiles/profile_manager.h",
  "+chrome/browser/ui/ash/multi_user",
]
