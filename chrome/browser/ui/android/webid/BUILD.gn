# Copyright 2021 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.
import("//build/config/android/config.gni")
import("//build/config/android/rules.gni")
import("//third_party/jni_zero/jni_zero.gni")

android_library("public_java") {
  resources_package = "org.chromium.chrome.browser.ui.android.webid"
  deps = [
    "//base:base_java",
    "//chrome/android:chrome_app_java_resources",
    "//components/browser_ui/bottomsheet/android:java",
    "//components/url_formatter/android:url_formatter_java",
    "//content/public/android:content_full_java",
    "//third_party/androidx:androidx_annotation_annotation_java",
    "//third_party/blink/public/mojom:mojom_platform_java",
    "//third_party/jni_zero:jni_zero_java",
    "//ui/android:ui_java",
    "//url:gurl_java",
  ]

  srcjar_deps = [
    ":java_enums_srcjar",
    ":jni_headers",
  ]
  sources = [
    "java/src/org/chromium/chrome/browser/ui/android/webid/AccountSelectionComponent.java",
    "java/src/org/chromium/chrome/browser/ui/android/webid/data/Account.java",
    "java/src/org/chromium/chrome/browser/ui/android/webid/data/ClientIdMetadata.java",
    "java/src/org/chromium/chrome/browser/ui/android/webid/data/IdentityCredentialTokenError.java",
    "java/src/org/chromium/chrome/browser/ui/android/webid/data/IdentityProviderData.java",
    "java/src/org/chromium/chrome/browser/ui/android/webid/data/IdentityProviderMetadata.java",
  ]
}

generate_jni("jni_headers") {
  sources = [
    "java/src/org/chromium/chrome/browser/ui/android/webid/data/Account.java",
    "java/src/org/chromium/chrome/browser/ui/android/webid/data/ClientIdMetadata.java",
    "java/src/org/chromium/chrome/browser/ui/android/webid/data/IdentityCredentialTokenError.java",
    "java/src/org/chromium/chrome/browser/ui/android/webid/data/IdentityProviderData.java",
    "java/src/org/chromium/chrome/browser/ui/android/webid/data/IdentityProviderMetadata.java",
  ]
  public_deps = [ "//chrome/browser/ui/android/webid/internal:jni" ]
}

java_cpp_enum("java_enums_srcjar") {
  sources = [ "//content/public/browser/identity_request_dialog_controller.h" ]
}
