// Copyright 2022 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#ifndef CHROME_BROWSER_POLICY_CLOUD_USER_POLICY_SIGNIN_SERVICE_UTIL_H_
#define CHROME_BROWSER_POLICY_CLOUD_USER_POLICY_SIGNIN_SERVICE_UTIL_H_

#include <string>
#include <vector>

// Util functions in this file are designed only to be used by
// user_policy_signin_service.cc and user_policy_signin_service_mobile.cc/

class Profile;
class ProfileManager;

namespace policy {

// Update the Profile attributes for when the account is signed out.
void UpdateProfileAttributesWhenSignout(Profile* profile,
                                        ProfileManager* profile_manager);

// Returns the device DM token if the user is affiliated. For most of platforms,
// this means DM token for CBCM.
// The function is used as `CloudPolicyClient::DeviceDMTokenCallback()`.
std::string GetDeviceDMTokenIfAffiliated(
    const std::vector<std::string>& user_affiliation_ids);

// Returns the Profile identification that is generated by `ProfileIdService`.
std::string GetProfileId(Profile* profile);

}  // namespace policy

#endif  // CHROME_BROWSER_POLICY_CLOUD_USER_POLICY_SIGNIN_SERVICE_UTIL_H_
