<?xml version="1.0" encoding="utf-8"?>
<!--
Copyright 2021 The Chromium Authors
Use of this source code is governed by a BSD-style license that can be
found in the LICENSE file.
-->
<ScrollView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

  <RelativeLayout
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:importantForAccessibility="no"
      android:textDirection="locale">

    <org.chromium.ui.widget.ChromeImageButton
        android:id="@+id/edit_button"
        android:layout_width="@dimen/address_prompt_edit_button_size"
        android:layout_height="@dimen/address_prompt_edit_button_size"
        android:layout_alignParentEnd="true"
        android:layout_marginEnd="@dimen/address_prompt_end_margin"
        android:padding="@dimen/address_prompt_edit_button_padding"
        android:background="?attr/selectableItemBackground"
        android:contentDescription="@string/autofill_save_address_prompt_edit_button_tooltip"
        android:scaleType="fitCenter"
        app:srcCompat="@drawable/edit_icon"
        app:tint="@color/default_icon_color_tint_list" />

    <LinearLayout
        android:id="@+id/address_data"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_toStartOf="@id/edit_button"
        android:paddingTop="@dimen/dialog_padding_top"
        android:paddingStart="@dimen/dialog_padding_sides"
        android:orientation="vertical">

      <org.chromium.ui.widget.TextViewWithLeading
          android:id="@+id/address"
          android:layout_width="match_parent"
          android:layout_height="wrap_content"
          android:layout_marginBottom="12dp"
          android:textAppearance="@style/TextAppearance.TextLarge.Secondary"
          app:leading="@dimen/text_size_large_leading"
          tools:text="John Doe\n1600 Amphitheatre Parkway\nMountain View, CA, 94043\nUnited States" />

      <TextView
          android:id="@+id/email"
          android:layout_width="match_parent"
          android:layout_height="wrap_content"
          android:textAppearance="@style/TextAppearance.TextLarge.Secondary"
          tools:text="<EMAIL>" />

      <TextView
          android:id="@+id/phone"
          android:layout_width="match_parent"
          android:layout_height="wrap_content"
          android:textAppearance="@style/TextAppearance.TextLarge.Secondary"
          tools:text="****** 111 0000" />

    </LinearLayout>

    <TextView
        android:id="@+id/autofill_address_profile_prompt_record_type_notice"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/address_prompt_end_margin"
        android:layout_marginTop="20dp"
        android:layout_below="@id/address_data"
        android:layout_marginStart="@dimen/dialog_padding_sides"
        android:breakStrategy="balanced"
        android:textAppearance="@style/TextAppearance.TextMedium.Secondary"
        tools:text="@string/autofill_save_in_account_prompt_address_source_notice" />
  </RelativeLayout>
</ScrollView>
