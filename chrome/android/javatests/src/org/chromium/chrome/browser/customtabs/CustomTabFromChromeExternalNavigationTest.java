// Copyright 2016 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

package org.chromium.chrome.browser.customtabs;

import android.content.Intent;
import android.net.Uri;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.filters.MediumTest;

import org.junit.Assert;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;

import org.chromium.base.IntentUtils;
import org.chromium.base.ThreadUtils;
import org.chromium.base.test.util.CommandLineFlags;
import org.chromium.base.test.util.Feature;
import org.chromium.chrome.browser.LaunchIntentDispatcher;
import org.chromium.chrome.browser.customtabs.CustomTabDelegateFactory.CustomTabNavigationDelegate;
import org.chromium.chrome.browser.flags.ChromeSwitches;
import org.chromium.chrome.browser.tab.Tab;
import org.chromium.chrome.browser.tab.TabDelegateFactory;
import org.chromium.chrome.browser.tab.TabTestUtils;
import org.chromium.chrome.test.ChromeJUnit4ClassRunner;

/** Tests for external navigation handling of Custom Tabs generated by Chrome. */
@RunWith(ChromeJUnit4ClassRunner.class)
@CommandLineFlags.Add({ChromeSwitches.DISABLE_FIRST_RUN_EXPERIENCE})
public class CustomTabFromChromeExternalNavigationTest {
    @Rule public CustomTabActivityTestRule mActivityRule = new CustomTabActivityTestRule();

    private Intent getCustomTabFromChromeIntent(final String url, final boolean markFromChrome) {
        return ThreadUtils.runOnUiThreadBlocking(
                () -> {
                    Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    intent =
                            LaunchIntentDispatcher.createCustomTabActivityIntent(
                                    ApplicationProvider.getApplicationContext(), intent);
                    IntentUtils.addTrustedIntentExtras(intent);
                    return intent;
                });
    }

    private void startCustomTabFromChrome(String url) {
        Intent intent = getCustomTabFromChromeIntent(url, true);
        mActivityRule.startCustomTabActivityWithIntent(intent);
    }

    @Test
    @Feature("CustomTabFromChrome")
    @MediumTest
    public void testUsingStandardExternalNavigationHandler() {
        startCustomTabFromChrome("about:blank");

        Tab tab = mActivityRule.getActivity().getActivityTab();
        TabDelegateFactory delegateFactory = TabTestUtils.getDelegateFactory(tab);
        Assert.assertTrue(delegateFactory instanceof CustomTabDelegateFactory);
        CustomTabDelegateFactory customTabDelegateFactory =
                ((CustomTabDelegateFactory) delegateFactory);
        Assert.assertFalse(
                customTabDelegateFactory.getExternalNavigationDelegate()
                        instanceof CustomTabNavigationDelegate);
    }
}
