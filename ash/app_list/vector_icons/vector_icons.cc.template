// Copyright 2023 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// vector_icons.cc.template is used to generate vector_icons.cc. Edit the former
// rather than the latter.

// Some of the icons in this target are passed over Mojo and thus rely on having
// a unique ID, so make sure there's a prefix that won't be reused elsewhere.
#define VECTOR_ICON_ID_PREFIX "app_list::"

#include "ash/app_list/vector_icons/vector_icons.h"

#include "components/vector_icons/cc_macros.h"
#include "ui/gfx/vector_icon_types.h"

#define DECLARE_VECTOR_COMMAND(x) using gfx::x;
DECLARE_VECTOR_COMMANDS

namespace app_list {

TEMPLATE_PLACEHOLDER

}  // namespace app_list
