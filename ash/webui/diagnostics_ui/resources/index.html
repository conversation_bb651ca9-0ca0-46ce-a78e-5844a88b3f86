<!-- Copyright 2020 The Chromium Authors
     Use of this source code is governed by a BSD-style license that can be
     found in the LICENSE file. -->
<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <title></title>
    <link rel="stylesheet" href="chrome://theme/typography.css">
    <link rel="stylesheet" href="chrome://resources/css/text_defaults_md.css">
    <link rel="stylesheet" href="chrome://resources/css/md_colors.css">
    <link rel="stylesheet" href="chrome://theme/colors.css?sets=legacy,sys">
  </head>
  <style>
    /* The :not(body) selector allows the variable definitions below to 'win'
       against the shared ones. */
    html:not(body) {
      /* Ensure cros colors are used by cr elements. Setting these on a
        specific element will still override this. */
      --cr-focus-outline-color: var(--cros-focus-ring-color);
      --cr-focused-item-color: var(--cros-highlight-color-focus);
      --cr-primary-text-color: var(--cros-text-color-primary);
      --cr-secondary-text-color: var(--cros-text-color-secondary);
      --iron-icon-fill-color: var(--cros-icon-color-primary);
      background-color: var(--cros-bg-color);
    }

    html:has(body.jelly-enabled) {
      background-color: var(--cros-sys-app_base_shaded);
    }

    html,
    body {
      height: 100%;
      margin: 0;
    }
  </style>
  <body class="jelly-enabled">
    <diagnostics-app></diagnostics-app>

    <script type="module" src="diagnostics_app.js"></script>
  </body>
</html>
