// Copyright 2024 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#ifndef ASH_WEBUI_GROWTH_INTERNALS_CONSTANTS_H_
#define ASH_WEBUI_GROWTH_INTERNALS_CONSTANTS_H_

#include <string_view>

namespace ash {

inline constexpr std::string_view kGrowthInternalsHost = "growth-internals";

}  // namespace ash

#endif  // ASH_WEBUI_GROWTH_INTERNALS_CONSTANTS_H_
