/* Copyright 2023 The Chromium Authors
 * Use of this source code is governed by a BSD-style license that can be
 * found in the LICENSE file. */

.menu-root {
  background: var(--cros-sys-base_elevated);
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3), 0 2px 6px rgba(0, 0, 0, 0.15);
  box-sizing: border-box;
  color: var(--cros-sys-on_surface);
  display: none;
  font: var(--cros-body-1-font);
  margin: 0;
  max-width: calc(100vw - 16px);
  padding-block: 8px;
  padding-inline: 0;
  position: fixed;
  width: max-content;
}

.menu-root[aria-expanded=true] {
  display: block;
}

.menu-root > .item {
  cursor: pointer;
  list-style-type: none;
  padding-block: 8px;
  padding-inline: 16px;
}

.menu-root > .item:is(:hover, :focus-visible) {
  background: var(--cros-sys-hover_on_subtle);
}
