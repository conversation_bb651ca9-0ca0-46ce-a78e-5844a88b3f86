# Copyright 2024 The Chromium Authors
# Use of this source code is governed by a BSD - style license that can be
# found in the LICENSE file.

import("//ash/webui/boca_ui/boca_app.gni")
import("//build/buildflag_header.gni")
import("//mojo/public/tools/bindings/mojom.gni")
import("//third_party/closure_compiler/compile_js.gni")
import("//tools/grit/preprocess_if_expr.gni")
import("//ui/webui/resources/tools/generate_grd.gni")

assert(is_chromeos, "Boca App is ChromeOS only")

static_library("boca_ui") {
  sources = [
    "boca_app_page_handler.cc",
    "boca_app_page_handler.h",
    "boca_ui.cc",
    "boca_ui.h",
    "provider/classroom_page_handler_impl.cc",
    "provider/classroom_page_handler_impl.h",
    "provider/content_settings_handler.cc",
    "provider/content_settings_handler.h",
    "provider/network_info_provider.cc",
    "provider/network_info_provider.h",
    "provider/tab_info_collector.cc",
    "provider/tab_info_collector.h",
    "webview_auth_delegate.cc",
    "webview_auth_delegate.h",
    "webview_auth_handler.cc",
    "webview_auth_handler.h",
  ]

  deps = [
    ":constants",
    "resources:resources",
    "//ash",
    "//ash/constants:constants",
    "//ash/public/cpp",
    "//ash/webui/boca_ui/mojom",
    "//ash/webui/common:chrome_os_webui_config",
    "//ash/webui/resources:boca_app_bundle_resources",
    "//ash/webui/system_apps/public:system_web_app_config",
    "//base",
    "//chrome/browser/ash/boca",
    "//chrome/browser/content_settings:content_settings_factory",
    "//chrome/browser/profiles:profile",
    "//chromeos/ash/components/boca",
    "//chromeos/ash/components/boca:spotlight",
    "//chromeos/ash/components/boca/on_task",
    "//chromeos/ash/components/boca/proto",
    "//chromeos/ash/components/boca/session_api",
    "//chromeos/ash/components/browser_context_helper",
    "//chromeos/ui/frame",
    "//chromeos/ui/wm",
    "//components/account_id",
    "//components/app_constants",
    "//components/app_restore",
    "//components/content_settings/core/browser",
    "//components/content_settings/core/common",
    "//components/permissions:permissions",
    "//components/signin/public/base",
    "//components/signin/public/identity_manager",
    "//components/user_manager",
    "//content/public/browser",
    "//google_apis",
    "//google_apis/classroom",
    "//google_apis/common",
    "//skia",
    "//ui/gfx",
    "//ui/webui",
    "//ui/wm",
  ]
}

source_set("constants") {
  sources = [ "url_constants.h" ]
}

# Used to turn off tests that only work with our CIPD package.
buildflag_header("buildflags") {
  header = "buildflags.h"

  flags = [ "ENABLE_CROS_BOCA_APP=$enable_cros_boca_app" ]
}

source_set("unit_tests") {
  testonly = true

  sources = [
    "boca_app_page_handler_unittest.cc",
    "provider/classroom_page_handler_impl_unittest.cc",
    "provider/network_info_provider_unittest.cc",
    "webview_auth_handler_unittest.cc",
  ]

  deps = [
    ":boca_ui",
    "//ash:test_support",
    "//ash/webui/boca_ui/mojom",
    "//base/test:test_support",
    "//chrome/test:test_support",
    "//chromeos/ash/components:test_support",
    "//chromeos/ash/components/boca",
    "//chromeos/ash/components/boca:spotlight",
    "//chromeos/ash/components/boca/proto",
    "//chromeos/ash/components/boca/session_api",
    "//chromeos/ash/components/browser_context_helper",
    "//chromeos/ash/components/browser_context_helper:test_support",
    "//chromeos/ash/services/network_config/public/cpp:test_support",
    "//chromeos/services/network_config/public/cpp:test_support",
    "//components/signin/public/identity_manager",
    "//components/user_manager",
    "//components/user_manager:test_support",
    "//content/test:test_support",
    "//google_apis:test_support",
    "//google_apis/common:test_support",
    "//mojo/public/cpp/bindings",
    "//net:test_support",
    "//services/network:test_support",
    "//testing/gmock",
    "//testing/gtest",
    "//ui/aura",
  ]
}
