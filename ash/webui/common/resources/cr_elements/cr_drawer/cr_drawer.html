<style>
  :host {
    --cr-drawer-background-color: var(--cros-sys-app_base_shaded);
    --cr-drawer-width: 256px;
  }

  :host dialog {
    --transition-timing: 200ms ease;
    background-color: var(--cr-drawer-background-color);
    border: none;
    border-start-end-radius: var(--cr-drawer-border-start-end-radius, 0);
    border-end-end-radius: var(--cr-drawer-border-end-end-radius, 0);
    bottom: 0;
    left: calc(-1 * var(--cr-drawer-width));
    margin: 0;
    max-height: initial;
    max-width: initial;
    overflow: hidden;
    padding: 0;
    position: absolute;
    top: 0;
    transition: left var(--transition-timing);
    width: var(--cr-drawer-width);
  }

  :host dialog,
  #container {
    height: 100%;
    word-break: break-word;
  }

  :host([show_]) dialog {
    left: 0;
  }

  :host([align=rtl]) dialog {
    left: auto;
    right: calc(-1 * var(--cr-drawer-width));
    transition: right var(--transition-timing);
  }

  :host([show_][align=rtl]) dialog {
    right: 0;
  }

  :host dialog::backdrop {
    background: rgba(0, 0, 0, 0.5);
    bottom: 0;
    left: 0;
    opacity: 0;
    position: absolute;
    right: 0;
    top: 0;
    transition: opacity var(--transition-timing);
  }

  :host([show_]) dialog::backdrop {
    opacity: 1;
  }

  .drawer-header {
    align-items: center;
    border-bottom: var(--cr-separator-line);
    color: var(--cr-drawer-header-color, inherit);
    display: flex;

    /* Declare "font" property before more granular font properties like
     * "font-weight", etc. to properly compute font styles.
     */
    font: var(--cr-drawer-header-font, inherit);
    font-size: 123.08%;  /* go to 16px from 13px */
    font-weight: var(--cr-drawer-header-font-weight, inherit);

    min-height: 56px;
    padding-inline-start: var(--cr-drawer-header-padding, 24px);
  }

  @media (prefers-color-scheme: dark) {
    .drawer-header {
      color: var(--cr-primary-text-color);
    }
  }

  #heading {
    outline: none;
  }

  :host ::slotted([slot='body']) {
    height: calc(100% - 56px);
    overflow: auto;
  }

  picture {
    margin-inline-end: 16px;
  }

  picture,
  #product-logo {
    height: 24px;
    width: 24px;
  }
</style>
<dialog id="dialog" on-cancel="onDialogCancel_" on-click="onDialogClick_"
    on-close="onDialogClose_">
  <div id="container" on-click="onContainerClick_">
    <div class="drawer-header">
      <slot name="header-icon">
        <picture>
          <source media="(prefers-color-scheme: dark)"
              srcset="//resources/images/chrome_logo_dark.svg">
          <img id="product-logo"
              srcset="chrome://theme/current-channel-logo@1x 1x,
                      chrome://theme/current-channel-logo@2x 2x"
              role="presentation">
        </picture>
      </slot>
      <div id="heading" tabindex="-1">[[heading]]</div>
    </div>
    <slot name="body"></slot>
  </div>
</dialog>
