# Copyright 2024 The Chromium Authors
# Use of this source code is governed by a BSD - style license that can be
# found in the LICENSE file.

import("//ui/webui/resources/tools/build_webui.gni")
import("//ui/webui/webui_features.gni")

assert(is_chromeos, "Personalization is for ChromeOS only.")

build_webui("build_ts") {
  grd_prefix = "ash_common_personalization"

  web_component_files = [ "wallpaper_grid_item_element.ts" ]

  ts_files = [ "wallpaper_state.ts" ]

  icons_html_files = [ "personalization_shared_icons.html" ]

  css_files = [
    "common.css",
    "cros_button_style.css",
    "wallpaper.css",
  ]

  ts_composite = true
  ts_deps = [
    "//ash/webui/common/resources/cr_elements:build_ts",
    "//third_party/polymer/v3_0:library",
    "//ui/webui/resources/js:build_ts",
    "//ui/webui/resources/mojo:build_ts",
  ]
  ts_extra_deps = [ "//ash/webui/common/resources:generate_definitions" ]
  ts_out_dir =
      "$root_gen_dir/ash/webui/common/resources/preprocessed/personalization"

  webui_context_type = "relative"
  generate_grdp = true
  grd_resource_path_prefix = "ash/common/personalization"
}
