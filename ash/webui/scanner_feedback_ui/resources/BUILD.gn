# Copyright 2024 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//ui/webui/resources/tools/build_webui.gni")

assert(is_chromeos)

build_webui("build") {
  grd_prefix = "ash_scanner_feedback_ui"

  static_files = [
    "index.css",
    "index.html",
  ]

  web_component_files = [ "app.ts" ]

  mojo_files_deps =
      [ "//ash/webui/scanner_feedback_ui/mojom:mojom_ts__generator" ]

  mojo_files = [ "$root_gen_dir/ash/webui/scanner_feedback_ui/mojom/scanner_feedback_ui.mojom-webui.ts" ]

  ts_deps = [
    "//ash/webui/common/resources/cr_elements:build_ts",
    "//third_party/cros-components:cros_components_ts",
    "//third_party/polymer/v3_0:library",
    "//ui/webui/resources/cr_components/color_change_listener:build_ts",
    "//ui/webui/resources/mojo:build_ts",
  ]

  ts_composite = true

  webui_context_type = "untrusted"

  grit_output_dir = "$root_gen_dir/ash/webui"
}
