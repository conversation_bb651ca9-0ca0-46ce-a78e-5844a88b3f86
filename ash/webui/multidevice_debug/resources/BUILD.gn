# Copyright 2019 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//third_party/closure_compiler/compile_js.gni")

assert(is_chromeos, "Proximity Auth is ChromeOS only")

js_type_check("closure_compile") {
  deps = [
    ":logs",
    ":webui",
  ]
}

js_library("webui") {
  externs_list = [ "$externs_path/chrome_send.js" ]
}

js_library("logs") {
  deps = [ ":webui" ]
}
