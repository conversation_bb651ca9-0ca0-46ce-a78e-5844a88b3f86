<style include="cr-shared-style shimless-rma-shared shimless-fonts">
  #logsDialog::part(dialog) {
    width: 512px;
  }

  #logsDialog::part(wrapper) {
    height: 480px;
  }

  #logsDialog [slot=button-container] {
    padding-bottom: 24px;
    padding-top: 24px;
  }

  #logsDialog [slot=title] {
    align-items: center;
    display: flex;
    font-size: 15px;
    height: 32px;
    justify-content: center;
    padding: 0 24px 0 24px;
  }

  #buttonContainer {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    height: 100%;
    justify-content: center;
    padding-inline-end: 24px;
    width: 100%;
  }

  .button-card {
    align-items: center;
    border-radius: 8px;
    box-shadow: var(--cros-elevation-1-shadow);
    height: 120px;
    justify-content: start;
    padding: 0;
    width: 100%;
  }

  .button-card:focus {
    outline: rgba(var(--google-blue-600-rgb), .4) solid 2px;
  }

  #diagnosticsButton {
    margin-bottom: 16px;
  }

  #rmaLogButton {
    margin-bottom: 16px;
  }

  :host([plugged-in]) #batteryCutButton {
    opacity: 0.38;
  }

  :host([plugged-in]) #batteryCutButton .button-label {
    color: var(--cros-text-color-disabled);
  }

  :host([plugged-in]) #batteryCutButton .button-description {
    color: var(--cros-text-color-disabled);
  }

  :host([plugged-in]) #batteryCutoffInfoIcon {
    color: var(--cros-icon-color-secondary);
  }

  :host([all-buttons-disabled]) .button-card {
    color: var(--cros-icon-color-secondary);
    opacity: 0.38;
  }

  :host([all-buttons-disabled]) .button-label,
  :host([all-buttons-disabled]) .button-description {
    color: var(--cros-text-color-disabled);
  }

  .button-label {
    color: var(--shimless-button-label-text-color);
    font-family: var(--shimless-button-label-font-family);
    font-size: var(--shimless-button-label-font-size);
    font-weight: var(--shimless-medium-font-weight);
    line-height: var(--shimless-button-label-line-height);
    margin-bottom: 5px;
  }

  .button-description {
    color: var(--shimless-instructions-text-color);
    font-family: var(--shimless-instructions-font-family);
    font-size: var(--shimless-instructions-font-size);
    font-weight: var(--shimless-regular-font-weight);
    line-height: var(--shimless-instructions-line-height);
    padding-inline-end: 24px;
  }

  .button-icon {
    height: 48px;
    min-width: 48px;
    padding-inline-end: 24px;
    padding-inline-start: 24px;
  }

  #batteryCutoffInfoIcon {
    margin-inline-end: 8px;
  }

  .log {
    white-space: pre-line;
  }

  #navigationButtonWrapper {
    bottom: 6px;
    left: 6px;
    position: fixed;
  }

  .navigation-button {
    background-color: rgba(0, 0, 0, 0.06);
    border: 0;
    box-sizing: content-box;
    color: var(--shimless-shutdown-buttons-text-color);
    font-weight: var(--shimless-regular-font-weight);
    height: 20px;
    padding-inline-end: 8px;
    padding-inline-start: 8px;
  }

  #shutDownButton {
    margin-inline-end: 8px;
  }

  #rebootButton[disabled],
  #shutDownButton[disabled] {
    opacity: var(--cr-disabled-opacity);
  }

  .navigation-icon {
    fill: var(--cros-icon-color-primary);
    height: 20px;
    padding-inline-end: 8px;
    width: 20px;
  }

  #logSaveAttemptButtonContainer {
    align-items: center;
    display: flex;
    justify-content: space-between;
  }

  #logSavedIconText {
    align-items: center;
    display: flex;
  }

  #connectUsbIcon {
    color: var(--cros-icon-color-secondary);
  }

  #connectUsbInstructions {
    color: var(--cros-text-color-primary);
  }
</style>

<base-page>
  <div slot="left-pane">
    <h1 tabindex="-1">[[i18n('repairCompletedTitleText')]]</h1>
    <div class="instructions">
      [[i18n('repairCompletedDescriptionText')]]
    </div>
    <div id="navigationButtonWrapper">
      <cr-button id="shutDownButton" class="navigation-button"
          on-click="onShutDownButtonClick"
          disabled="[[disableShutdownButtons(shutdownButtonsDisabled,
              allButtonsDisabled)]]">
        <iron-icon icon="shimless-icon:shutdown" class="navigation-icon">
        </iron-icon>
        [[i18n('repairCompleteShutDownButtonText')]]
      </cr-button>
      <cr-button id="rebootButton" class="navigation-button"
          on-click="onRebootButtonClick"
          disabled="[[disableShutdownButtons(shutdownButtonsDisabled,
              allButtonsDisabled)]]">
        <iron-icon icon="shimless-icon:reboot" class="navigation-icon">
        </iron-icon>
        [[i18n('repairCompleteRebootButtonText')]]
      </cr-button>
    </div>
  </div>
  <div slot="right-pane">
    <div id="buttonContainer">
      <cr-button id="diagnosticsButton" class="button-card"
        on-click="onDiagnosticsButtonClick" disabled="[[allButtonsDisabled]]">
        <iron-icon icon$="[[getDiagnosticsIcon(allButtonsDisabled)]]"
            class="button-icon">
        </iron-icon>
        <div class="button-text">
          <div class="button-label">
            <span>[[i18n('repairCompletedDiagnosticsButtonText')]]</span>
          </div>
          <div class="button-description">
            [[i18n('repairCompletedDiagnosticsDescriptionText')]]
          </div>
        </div>
      </cr-button>
      <cr-button id="rmaLogButton" class="button-card"
          on-click="onRmaLogButtonClick" disabled="[[allButtonsDisabled]]">
        <iron-icon icon$="[[getRmaLogIcon(allButtonsDisabled)]]"
            class="button-icon">
        </iron-icon>
        <div class="button-text">
          <div class="button-label">
            <span>[[i18n('repairCompletedLogsButtonText')]]</span>
          </div>
          <div class="button-description">
            [[i18n('repairCompletedLogsDescriptionText')]]
          </div>
        </div>
      </cr-button>
      <div id="batteryCutoffTooltipWrapper">
        <cr-button id="batteryCutButton" class="button-card"
            on-click="onBatteryCutButtonClick"
            disabled$="[[disableBatteryCutButton(pluggedIn,
            allButtonsDisabled)]]">
          <iron-icon id="batteryCutoffIcon"
              icon$="[[getBatteryCutoffIcon(allButtonsDisabled)]]"
              class="button-icon">
          </iron-icon>
          <div class="button-text">
            <div class="button-label">
              <span>[[i18n('repairCompletedShutoffButtonText')]]</span>
            </div>
            <div class="button-description">
              [[getRepairCompletedShutoffText(pluggedIn)]]
            </div>
          </div>
        </cr-button>
      </div>
    </div>
  </div>
</base-page>

<cr-dialog id="logsDialog" close-text="close" on-cancel="closeLogsDialog">
  <div slot="title">
    [[i18n('rmaLogsTitleText')]]
  </div>
  <div slot="body">
    <div class="log">[[log]]</div>
  </div>
  <div id="saveLogButtonContainer" class="dialog-footer" slot="button-container"
      hidden="[[!shouldShowSaveToUsbButton(usbLogState)]]">
    <cr-button id="closeLogDialogButton" on-click="closeLogsDialog">
      [[i18n('rmaLogsCancelButtonText')]]
    </cr-button>
    <cr-button id="saveLogDialogButton" class="text-button action-button"
        on-click="onSaveLogClick">
      [[i18n('rmaLogsSaveToUsbButtonText')]]
    </cr-button>
  </div>
  <div id="logSaveAttemptButtonContainer" class="dialog-footer"
        slot="button-container"
        hidden="[[!shouldShowLogSaveAttemptContainer(usbLogState)]]">
    <div id="logSavedIconText">
      <iron-icon id="verificationIcon" class="small-icon"
          icon="[[getSaveLogResultIcon(usbLogState)]]">
      </iron-icon>
      <span id="logSavedStatusText">[[logSavedStatusText]]</span>
    </div>
    <cr-button id="logRetryDialogButton" class="text-button action-button"
        on-click="retrySaveLogs"
        hidden="[[!shouldShowRetryButton(usbLogState)]]">
        [[i18n('retryButtonLabel')]]
    </cr-button>
    <cr-button id="logSaveDoneDialogButton" class="text-button action-button"
        on-click="closeLogsDialog">
      [[i18n('doneButtonLabel')]]
    </cr-button>
  </div>
  <div id="logConnectUsbMessageContainer" class="dialog-footer"
        slot="button-container"
        hidden="[[!shouldShowLogUsbMessageContainer(usbLogState)]]">
    <div id="logSavedIconText">
      <iron-icon id="connectUsbIcon" class="small-icon"
          icon="shimless-icon:warning"></iron-icon>
      <span id="connectUsbInstructions" class="instructions">
        [[i18n('rmaLogsMissingUsbMessageText')]]
      </span>
    </div>
  </div>
</cr-dialog>

<cr-dialog id="powerwashDialog" close-text="close">
  <div slot="title">
    [[i18n('powerwashDialogTitle')]]
  </div>
  <div slot="body">
    [[getPowerwashDescriptionString(selectedFinishRmaOption)]]
  </div>
  <div class="dialog-footer" slot="button-container">
    <cr-button id="closePowerwashDialogButton" on-click="closePowerwashDialog">
      [[i18n('cancelButtonLabel')]]
    </cr-button>
    <cr-button id="powerwashButton" class="text-button action-button"
        on-click="onPowerwashButtonClick">
      [[i18n('powerwashDialogPowerwashButton')]]
    </cr-button>
  </div>
</cr-dialog>

<cr-dialog id="batteryCutoffDialog" close-text="close" no-cancel>
  <div slot="body">
    [[i18n('repairCompletedBatteryCutoffCountdownDescription')]]
  </div>
  <div class="dialog-footer" slot="button-container">
    <cr-button id="closeBatteryCutoffDialogButton"
        on-click="onCutoffCancelClick">
      [[i18n('cancelButtonLabel')]]
    </cr-button>
    <cr-button id="batteryCutoffShutdownButton"
        class="text-button action-button"
        on-click="onCutoffShutdownButtonClick">
      [[i18n('repairCompletedBatteryCutoffShutdownButton')]]
    </cr-button>
  </div>
</cr-dialog>
