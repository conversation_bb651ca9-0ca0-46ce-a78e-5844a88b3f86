<style include="cr-shared-style shimless-rma-shared">
  #shimlessRMAContainer {
    align-items: stretch;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    height: 100%;
    padding-bottom: var(--container-vertical-padding);
    padding-inline: var(--container-horizontal-padding);
    padding-top: var(--container-vertical-padding);
    width: 100%;
  }

  #header,
  #contentContainer,
  #footer {
    padding-inline: var(--content-container-padding);
  }

  #contentContainer {
    min-height: var(--content-container-height);
  }

  .shimless-content {
    height: 100%;
    width: 100%;
  }

  #footer {
    min-height: var(--header-footer-height);
    text-align: end;
  }

  #header {
    min-height: var(--header-footer-height);
  }

  #back {
    border: 0;
    border-radius: 16px;
    height: 32px;
    padding-inline-start: 16px;
  }

  #next {
    margin-inline-end: -13px;
    top: 24px;
  }

  #exit {
    margin-inline-end: 8px;
    top: 24px;
  }

  .busy-icon {
    height: 20px;
    width: 20px;
  }

  .button-icon {
    height: var(--cr-icon-size);
    width: var(--cr-icon-size);
  }

  #exitButtonSpinner,
  #nextButtonCaret,
  #nextButtonSpinner {
    margin-inline-start: 8px;
  }

  #backButtonCaret,
  #backButtonSpinner {
    margin-inline-end: 8px;
  }

  #busyStateOverlay {
    background-color: white;
    display: none;
    height: 100vh;
    opacity: .4;
    position: fixed;
    width: 100%;
    z-index: 1;
  }

  :host([show-busy-state-overlay]) #busyStateOverlay {
    display: block;
  }

  #logsDialog::part(dialog) {
    height: 640px;
    width: 864px;
  }

  #logsDialog::part(wrapper) {
    max-height: 100%;
  }

  #logsDialog [slot=button-container] {
    height: 40px;
    padding-bottom: 12px;
    padding-top: 12px;
  }

  #logsDialog [slot=title] {
    align-items: center;
    display: flex;
    font-size: 15px;
    height: 32px;
    justify-content: center;
    padding: 0 24px 0 24px;
  }

  .logs-dialog-footer-text {
    color: var(--shimless-dialog-body-text-color);
    font-family: var(--shimless-dialog-body-font-family);
    font-size: var(--shimless-dialog-body-font-size);
    font-weight: var(--shimless-regular-font-weight);
    line-height: var(--shimless-dialog-body-line-height);
  }

  .logs-dialog-icon {
    align-self: flex-start;
    padding-inline-end: 6px;
    padding-top: 2px;
  }

  #logText {
    white-space: pre-line;
  }

  #logSaveAttemptButtonContainer {
    align-items: center;
    display: flex;
    justify-content: space-between;
  }

  #logSavedIconText {
    align-items: center;
    display: flex;
  }

  #connectUsbIcon {
    color: var(--cros-icon-color-secondary);
  }

  #connectUsbInstructions {
    color: var(--cros-text-color-primary);
  }
</style>

<div id="shimlessRMAContainer">
  <div id="busyStateOverlay"></div>
  <div id="header">
    <cr-button
      id="back" on-click="onBackButtonClicked"
      disabled="[[isButtonDisabled(currentPage.buttonBack, allButtonsDisabled)]]"
      hidden$="[[isButtonHidden(currentPage.buttonBack)]]">
      <paper-spinner-lite id="backButtonSpinner" class="busy-icon"
          hidden$="[[!backButtonClicked]]" active>
      </paper-spinner-lite>
      <iron-icon id="backButtonCaret" icon="cr:chevron-left"
          class="button-icon" hidden$="[[backButtonClicked]]">
      </iron-icon>
      <span id="backButtonLabel">
        [[i18n('backButtonLabel')]]
      </span>
    </cr-button>
  </div>
  <div id="contentContainer"></div>
  <div id="footer">
    <cr-button
      id="exit" class="pill" on-click="onExitButtonClicked"
      disabled="[[isButtonDisabled(currentPage.buttonExit, allButtonsDisabled)]]"
      hidden$="[[isButtonHidden(currentPage.buttonExit)]]">
      <span id="exitButtonLabel">
        [[getExitButtonLabel(currentPage.buttonExitLabelKey)]]
      </span>
      <paper-spinner-lite id="exitButtonSpinner" class="busy-icon"
          hidden$="[[!confirmExitButtonClicked]]" active>
      </paper-spinner-lite>
    </cr-button>
    <cr-button
      id="next" class="action-button" on-click="onNextButtonClicked"
      disabled="[[isButtonDisabled(currentPage.buttonNext, allButtonsDisabled)]]"
      hidden$="[[isButtonHidden(currentPage.buttonNext)]]">
      <span id="nextButtonLabel">
        [[getNextButtonLabel(currentPage.buttonNextLabelKey)]]
      </span>
      <paper-spinner-lite id="nextButtonSpinner" class="busy-icon"
          hidden$="[[!nextButtonClicked]]" active>
      </paper-spinner-lite>
      <iron-icon id="nextButtonCaret" icon="cr:chevron-right"
          class="button-icon" hidden$="[[nextButtonClicked]]">
      </iron-icon>
    </cr-button>
  </div>
</div>
<cr-dialog id="exitDialog">
  <div slot="title">
    [[i18n('exitDialogTitleText')]]
  </div>
  <div slot="body">
    [[i18n('exitDialogDescriptionText')]]
  </div>
  <div class="dialog-footer" slot="button-container">
    <cr-button id="cancelExitDialogButton" class="pill"
        on-click="closeDialog">
      [[i18n('exitDialogCancelButtonLabel')]]
    </cr-button>
    <cr-button id="confirmExitDialogButton" class="action-button"
        on-click="onConfirmExitButtonClicked">
      [[i18n('exitButtonLabel')]]
    </cr-button>
  </div>
</cr-dialog>
<cr-dialog id="logsDialog" close-text="close" on-cancel="closeLogsDialog">
  <div slot="title">
    [[i18n('rmaLogsTitleText')]]
  </div>
  <div slot="body">
    <div id="logText">[[log]]</div>
  </div>
  <div id="saveLogButtonContainer" class="dialog-footer" slot="button-container"
      hidden="[[!shouldShowSaveToUsbButton(usbLogState)]]">
    <cr-button id="closeLogDialogButton" on-click="closeLogsDialog">
      [[i18n('rmaLogsCancelButtonText')]]
    </cr-button>
    <cr-button id="saveLogDialogButton" class="text-button action-button"
        on-click="onSaveLogClick">
      [[i18n('rmaLogsSaveToUsbButtonText')]]
    </cr-button>
  </div>
  <div id="logSaveAttemptButtonContainer" class="dialog-footer"
        slot="button-container"
        hidden="[[!shouldShowLogSaveAttemptContainer(usbLogState)]]">
    <div id="logSavedIconText">
      <iron-icon id="verificationIcon" class="small-icon logs-dialog-icon"
          icon="[[getSaveLogResultIcon(usbLogState)]]">
      </iron-icon>
      <span id="logSavedStatusText" class="logs-dialog-footer-text">
        [[logSavedStatusText]]
      </span>
    </div>
    <div>
      <cr-button id="logRetryDialogButton" class="text-button action-button"
          on-click="retrySaveLogs"
          hidden="[[!shouldShowRetryButton(usbLogState)]]">
          [[i18n('retryButtonLabel')]]
      </cr-button>
      <cr-button id="logSaveDoneDialogButton" class="text-button action-button"
          on-click="closeLogsDialog">
        [[i18n('doneButtonLabel')]]
      </cr-button>
    </div>
  </div>
  <div id="logConnectUsbMessageContainer" class="dialog-footer"
        slot="button-container"
        hidden="[[!shouldShowLogUsbMessageContainer(usbLogState)]]">
    <div id="logSavedIconText">
      <iron-icon id="connectUsbIcon" class="small-icon logs-dialog-icon"
          icon="shimless-icon:warning"></iron-icon>
      <span id="connectUsbInstructions" class="logs-dialog-footer-text">
        [[i18n('rmaLogsMissingUsbMessageText')]]
      </span>
      <cr-button id="closeLogDialogButton" class="action-button"
          on-click="closeLogsDialog">
        [[i18n('rmaLogsCancelButtonText')]]
      </cr-button>
    </div>
  </div>
</cr-dialog>
<shimless-3p-diagnostics id="shimless3pDiagnostics"></shimless-3p-diagnostics>
