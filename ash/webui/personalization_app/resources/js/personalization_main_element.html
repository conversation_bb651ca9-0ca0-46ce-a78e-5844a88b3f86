<style include="common cros-button-style">
  #container {
    display: grid;
    grid-template-areas:
      '. . userpreview        . .'
      '. . time-of-day-banner . .'
      '. . middle             . .'
      '. . rbg-keyboard       . .'
      '. . .                  . .';
    grid-template-columns: 1fr 24px minmax(500px, 1020px) 24px 1fr;
    grid-template-rows: 132px auto 1fr auto 24px;
    height: 100%;
    position: relative;
    width: 100%;
  }

  user-preview {
    grid-area: userpreview;
  }

  time-of-day-banner {
    grid-area: time-of-day-banner;
    margin-bottom: 23px;
    min-height: 72px;
  }

  #middle {
    display: grid;
    gap: 20px;
    grid-area: middle;
    grid-template-columns: repeat(auto-fit, minmax(318px, 1fr));
  }

  keyboard-backlight {
    grid-area: rbg-keyboard;
    margin-top: 20px;
  }

  @media (max-width: 700px) {
    #container {
      grid-template-columns: 1fr 24px 500px 24px 1fr;
    }
    #middle {
      grid-template-columns: repeat(1, 500px);
    }
  }
</style>
<div id="container">
  <user-preview path="[[path]]">
  </user-preview>
  <template is="dom-if" if="[[shouldShowTimeOfDayBanner_]]">
    <time-of-day-banner></time-of-day-banner>
  </template>
  <div id="middle">
    <wallpaper-preview>
      <personalization-theme></personalization-theme>
    </wallpaper-preview>
    <ambient-preview-large>
    </ambient-preview-large>
  </div>
  <template is="dom-if" if="[[isRgbKeyboardSupported_]]">
    <keyboard-backlight></keyboard-backlight>
  </template>
</div>
