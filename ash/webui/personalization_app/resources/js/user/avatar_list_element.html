<style>
  :host {
    --personalization-app-avatar-image-size: 72px;
    --personalization-app-avatar-image-padding: 10px;
    --personalization-app-avatar-ripple-color: var(--cros-sys-ripple_primary);
    --personalization-app-avatar-ripple-opacity: 100%;
  }

  iron-list {
    height: 100%;
    width: 100%;
  }

  .hidden {
    display: none;
  }

  .option-container {
    padding: var(--personalization-app-avatar-image-padding);
  }

  .image-container {
    background-color: var(--personalization-app-grid-item-background-color);
    border-radius: 50%;
    cursor: pointer;
    position: relative;
  }

  .image-container:hover {
    filter: brightness(0.94);
  }

  .image-border-container {
    border-radius: 50%;
    height: var(--personalization-app-avatar-image-size);
    outline: 1px solid rgba(0, 0, 0, 0.08);
    width: var(--personalization-app-avatar-image-size);
  }

  .image-container img {
    background-size: var(--personalization-app-avatar-image-size) var(--personalization-app-avatar-image-size);
    border-radius: 50%;
    height: var(--personalization-app-avatar-image-size);
    width: var(--personalization-app-avatar-image-size);
  }

  paper-ripple {
    color: rgba(var(--cros-ripple-color-rgb), 1);
    --paper-ripple-opacity: var(--cros-button-primary-ripple-opacity);
  }

  avatar-camera {
    bottom: 0;
    left: 0;
    position: fixed;
    right: 0;
    top: 0;
  }

  .avatar-button-container {
    background-color: var(--cros-sys-primary_container, var(--cros-highlight-color));
    border-radius: 100%;
    cursor: pointer;
    display: inline-block;
    line-height: var(--personalization-app-avatar-image-size);
    position: relative;
    text-align: center;
    vertical-align: middle;
  }

  .avatar-button-container:hover {
    background-color: var(--cros-sys-highlight_text, var(--cros-text-highlight-color));
  }

  .avatar-button-container:focus-visible {
    outline: 2px solid var(--cros-focus-ring-color);
  }

  .avatar-button-container paper-ripple {
    color: var(--personalization-app-avatar-ripple-color);
    --paper-ripple-opacity: var(--personalization-app-avatar-ripple-opacity);
  }

  .avatar-button-container iron-icon {
    line-height: inherit;
    --iron-icon-height: 20px;
    --iron-icon-width: 20px;
    --iron-icon-fill-color: var(--cros-link-color);
  }

  .image-container:focus-visible {
    border-radius: 50%;
    outline: 2px solid var(--cros-focus-ring-color);
  }

  .image-container iron-icon[icon='personalization:checkmark'],
  .image-container iron-icon[icon='personalization-shared:circle-checkmark'] {
    --iron-icon-height: 20px;
    --iron-icon-width: 20px;
    bottom: 0;
    position: absolute;
    right: 0;
  }

  .image-container:not([aria-selected='true'])
      iron-icon[icon='personalization:checkmark'],

  .image-container:not([aria-selected='true'])
      iron-icon[icon='personalization-shared:circle-checkmark'] {
    display: none;
  }
</style>
<iron-list items="[[options_]]" role="listbox"
    aria-setsize$="[[options_.length]]" grid>
  <template>
    <div class="option-container">
      <div id$="[[item.id]]"
          class$="[[getOptionInnerContainerClass_(item, image_)]]"
          role="option"
          on-click="onOptionSelected_" on-keypress="onOptionSelected_"
          on-error="onAvatarNetworkError_"
          tabindex$="[[tabIndex]]"
          aria-posinset$="[[getAriaIndex_(index)]]"
          aria-selected$="[[getAriaSelected_(item, image_)]]"
          aria-label$="[[item.title]]"
          title$="[[item.title]]"
          data-id$="[[item.defaultImageIndex]]">
        <paper-ripple class="circle"></paper-ripple>
        <div class="image-border-container">
          <img class$="[[getImageClassForOption_(item)]]"
              src$="[[getAvatarUrl_(item.imgSrc)]]"
              style$="[[getImgBackgroundStyle_(item.imgSrc, item.defaultImageIndex)]]"
              on-error="onImgError_">
          <iron-icon icon$="[[item.icon]]"></iron-icon>
        </div>
      </div>
  </div>
  </template>
</iron-list>
<template is="dom-if" if="[[cameraMode_]]" restamp>
  <avatar-camera on-close="onCameraClosed_" mode="[[cameraMode_]]">
  </avatar-camera>
</template>
