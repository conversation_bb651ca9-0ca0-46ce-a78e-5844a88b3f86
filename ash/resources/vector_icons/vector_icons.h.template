// Copyright 2016 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// vector_icons.h.template is used to generate vector_icons.h. Edit the former
// rather than the latter.

#ifndef ASH_RESOURCES_VECTOR_ICONS_VECTOR_ICONS_H_
#define ASH_RESOURCES_VECTOR_ICONS_VECTOR_ICONS_H_

#include "ash/ash_export.h"

namespace gfx {
struct VectorIcon;
}

#define VECTOR_ICON_TEMPLATE_H(icon_name) \
ASH_EXPORT extern const gfx::VectorIcon icon_name;

namespace ash {

TEMPLATE_PLACEHOLDER

}  // namespace ash

#undef VECTOR_ICON_TEMPLATE_H

#endif  // ASH_RESOURCES_VECTOR_ICONS_VECTOR_ICONS_H_
