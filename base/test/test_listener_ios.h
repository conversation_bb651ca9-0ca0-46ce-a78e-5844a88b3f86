// Copyright 2012 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#ifndef BASE_TEST_TEST_LISTENER_IOS_H_
#define BASE_TEST_TEST_LISTENER_IOS_H_

namespace base::test_listener_ios {

// Register an IOSRunLoopListener.
void RegisterTestEndListener();

}  // namespace base::test_listener_ios

#endif  // BASE_TEST_TEST_LISTENER_IOS_H_
