// Copyright 2024 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// Flags: --allow-natives-syntax --turbolev --turbofan

function alloc() {
  return [1, {x: 42.27}, 3, 'abc'];
}

%PrepareFunctionForOptimization(alloc);
assertEquals([1, {x : 42.27}, 3, "abc"], alloc());
%OptimizeMaglevOnNextCall(alloc);
assertEquals([1, {x : 42.27}, 3, "abc"], alloc());
%OptimizeFunctionOnNextCall(alloc);
assertEquals([1, {x : 42.27}, 3, "abc"], alloc());
assertOptimized(alloc);
