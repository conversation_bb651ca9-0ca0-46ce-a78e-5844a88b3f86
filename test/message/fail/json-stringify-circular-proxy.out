*%(basename)s:28: TypeError: Converting circular structure to JSON
    --> starting at object with constructor 'Object'
    |     property 'first' -> object with constructor 'Object'
    |     property 'x' -> object with constructor 'ArrayHolder'
    |     ...
    |     index 1 -> object with constructor 'Inner'
    --- property 'y' closes the circle
JSON.stringify(root);
     ^
TypeError: Converting circular structure to JSON
    --> starting at object with constructor 'Object'
    |     property 'first' -> object with constructor 'Object'
    |     property 'x' -> object with constructor 'ArrayHolder'
    |     ...
    |     index 1 -> object with constructor 'Inner'
    --- property 'y' closes the circle
    at JSON.stringify (<anonymous>)
    at *%(basename)s:28:6

