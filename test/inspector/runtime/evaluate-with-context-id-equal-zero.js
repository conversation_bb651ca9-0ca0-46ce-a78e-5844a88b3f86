// Copyright 2016 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

let {session, contextGroup, Protocol} = InspectorTest.start("Tests that DevTools doesn't crash on Runtime.evaluate with contextId equals 0.");

Protocol.Runtime.evaluate({ "contextId": 0, "expression": "" })
  .then(message => InspectorTest.logMessage(message))
  .then(() => InspectorTest.completeTest());
