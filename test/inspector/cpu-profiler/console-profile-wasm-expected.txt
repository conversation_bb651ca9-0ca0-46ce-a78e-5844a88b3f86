Test that console profiles contain wasm function names.

Running test: testEnableProfilerEarly
Compiling wasm.
Building wasm module with sentinel 1.
Running fib with increasing input until it shows up in the profile.
Found expected functions in profile.
Wasm script id is set.
Wasm position: wasm://wasm/ed5627ce@0:47

Running test: testEnableProfilerLate
Compiling wasm.
Building wasm module with sentinel 2.
Running fib with increasing input until it shows up in the profile.
Found expected functions in profile.
Wasm script id is set.
Wasm position: wasm://wasm/def0f3f2@0:47

Running test: testEnableProfilerAfterDebugger
Compiling wasm.
Building wasm module with sentinel 3.
Running fib with increasing input until it shows up in the profile.
Found expected functions in profile.
Wasm script id is set.
Wasm position: wasm://wasm/e950bb4a@0:47

Running test: testEnableProfilerBeforeDebugger
Compiling wasm.
Building wasm module with sentinel 4.
Running fib with increasing input until it shows up in the profile.
Found expected functions in profile.
Wasm script id is set.
Wasm position: wasm://wasm/d8d4f97a@0:47

Running test: testRunningCodeInDifferentIsolate
Building wasm module with sentinel 5.
Instantiating in inspector isolate.
Instantiating in the debugged isolate.
Compiling wasm.
Enabling profiler in the debugged isolate.
Running in the inspector isolate.
Running in the debugged isolate.
Running fib with increasing input until it shows up in the profile.
Found expected functions in profile.
Wasm script id is set.
Wasm position: wasm://wasm/8ffda0ce@0:47
Disabling profiler.
