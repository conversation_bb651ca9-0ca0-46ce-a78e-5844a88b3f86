Check that setScriptSource doesn't affect debug-evaluate block listing
{
    className : ReferenceError
    description : ReferenceError: a is not defined     at globalThis.foo (eval at i (:1:1), <anonymous>:1:27)     at <anonymous>:1:12
    objectId : <objectId>
    subtype : error
    type : object
}
Debugger.setScriptSource: Ok
{
    className : ReferenceError
    description : ReferenceError: a is not defined     at globalThis.foo (eval at i (:1:1), <anonymous>:1:27)     at <anonymous>:1:12
    objectId : <objectId>
    subtype : error
    type : object
}
