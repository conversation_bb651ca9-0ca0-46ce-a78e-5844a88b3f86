#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: no
test function name: f

---
snippet: "
  var a = {x:13, y:14};
  function f() {
    return delete a.x;
  };
  f();
"
frame size: 1
parameter count: 1
bytecode array length: 9
bytecodes: [
  /*   39 S> */ B(LdaGlobal), U8(0), U8(0),
                B(Star0),
                B(LdaConstant), U8(1),
                B(DeletePropertySloppy), R(0),
  /*   57 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["a"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["x"],
]
handlers: [
]

---
snippet: "
  a = {1:13, 2:14};
  function f() {
    'use strict';
    return delete a[1];
  };
  f();
"
frame size: 1
parameter count: 1
bytecode array length: 9
bytecodes: [
  /*   51 S> */ B(LdaGlobal), U8(0), U8(0),
                B(Star0),
                B(LdaSmi), I8(1),
                B(DeletePropertyStrict), R(0),
  /*   70 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["a"],
]
handlers: [
]

---
snippet: "
  var a = {x:13, y:14};
  function f() {
    return delete a;
  };
  f();
"
frame size: 1
parameter count: 1
bytecode array length: 9
bytecodes: [
  /*   39 S> */ B(LdaConstant), U8(0),
                B(Star0),
                B(CallRuntime), U16(Runtime::kDeleteLookupSlot), R(0), U8(1),
  /*   55 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["a"],
]
handlers: [
]

---
snippet: "
  b = 30;
  function f() {
    return delete b;
  };
  f();
"
frame size: 1
parameter count: 1
bytecode array length: 9
bytecodes: [
  /*   25 S> */ B(LdaConstant), U8(0),
                B(Star0),
                B(CallRuntime), U16(Runtime::kDeleteLookupSlot), R(0), U8(1),
  /*   41 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["b"],
]
handlers: [
]

