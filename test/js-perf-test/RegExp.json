{"owners": ["<EMAIL>", "<EMAIL>"], "name": "RegExp", "run_count": 3, "run_count_arm": 1, "run_count_arm64": 1, "timeout": 240, "units": "score", "total": true, "resources": ["base.js"], "flags": ["--no-regexp-results-cache"], "tests": [{"name": "RegExp", "path": ["RegExp"], "main": "run.js", "resources": ["base_ctor.js", "base_exec.js", "base_flags.js", "base.js", "base_match.js", "base_replaceall_emoji_g.js", "base_replaceall_emoji_gu.js", "base_replaceall_emoji_gv.js", "base_replace_emoji_g.js", "base_replace_emoji_gu.js", "base_replace_emoji_gv.js", "base_replace.js", "base_search.js", "base_split.js", "base_test.js", "case_test.js", "complex_case_test.js", "ctor.js", "exec.js", "flags.js", "inline_test.js", "match.js", "replaceall_emoji_g.js", "replaceall_emoji_gu.js", "replaceall_emoji_gv.js", "replace_emoji_g.js", "replace_emoji_gu.js", "replace_emoji_gv.js", "replace.js", "search.js", "slow_exec.js", "slow_flags.js", "slow_match.js", "slow_replaceall_emoji_g.js", "slow_replaceall_emoji_gu.js", "slow_replaceall_emoji_gv.js", "slow_replace_emoji_g.js", "slow_replace_emoji_gu.js", "slow_replace_emoji_gv.js", "slow_replace.js", "slow_search.js", "slow_split.js", "slow_test.js", "split.js", "test.js"], "results_regexp": "^%s\\-RegExp\\(Score\\): (.+)$", "tests": [{"name": "CaseInsensitiveTest"}, {"name": "ComplexCaseInsensitiveTest"}, {"name": "Ctor"}, {"name": "Exec"}, {"name": "Flags"}, {"name": "InlineTest"}, {"name": "Match"}, {"name": "Replace"}, {"name": "ReplaceAllEmoji_g"}, {"name": "ReplaceAllEmoji_gu"}, {"name": "ReplaceAllEmoji_gv"}, {"name": "ReplaceEmoji_g"}, {"name": "ReplaceEmoji_gu"}, {"name": "ReplaceEmoji_gv"}, {"name": "Search"}, {"name": "SlowExec"}, {"name": "SlowFlags"}, {"name": "SlowMatch"}, {"name": "SlowReplace"}, {"name": "SlowReplaceAllEmoji_g"}, {"name": "SlowReplaceAllEmoji_gu"}, {"name": "SlowReplaceAllEmoji_gv"}, {"name": "SlowReplaceEmoji_g"}, {"name": "SlowReplaceEmoji_gu"}, {"name": "SlowReplaceEmoji_gv"}, {"name": "SlowSearch"}, {"name": "SlowSplit"}, {"name": "SlowTest"}, {"name": "Split"}, {"name": "Test"}]}]}