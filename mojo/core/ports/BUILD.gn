# Copyright 2016 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/compiler/compiler.gni")
import("//mojo/features.gni")
import("//testing/test.gni")

# Only legacy Mojo Core and its tests can depend on targets here.
assert(mojo_support_legacy_core)

component("ports") {
  output_name = "mojo_core_ports"

  sources = [
    "event.cc",
    "event.h",
    "message_filter.h",
    "message_queue.cc",
    "message_queue.h",
    "name.cc",
    "name.h",
    "node.cc",
    "node.h",
    "node_delegate.h",
    "port.cc",
    "port.h",
    "port_locker.cc",
    "port_locker.h",
    "port_ref.cc",
    "port_ref.h",
    "user_data.h",
    "user_message.cc",
    "user_message.h",
  ]

  configs += [ "//build/config/compiler:wexit_time_destructors" ]

  defines = [ "IS_MOJO_CORE_PORTS_IMPL" ]

  if (target_os == "chromeos") {
    defines += [ "MOJO_BACKWARDS_COMPAT" ]
  }

  public_deps = [ "//base" ]

  if (!is_nacl) {
    deps = [ "//crypto" ]
  }

  if (!is_debug && !optimize_for_size) {
    configs -= [ "//build/config/compiler:default_optimization" ]
    configs += [ "//build/config/compiler:optimize_max" ]
  }
}

source_set("tests") {
  testonly = true

  sources = [
    "name_unittest.cc",
    "ports_unittest.cc",
  ]

  deps = [
    ":ports",
    "//base",
    "//base/test:test_support",
    "//testing/gtest",
  ]
}
