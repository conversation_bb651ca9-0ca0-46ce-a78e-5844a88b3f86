// typemap.test-mojom.h is auto generated by mojom_bindings_generator.py, do not edit

// Copyright 2013 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#ifndef TYPEMAP_TEST_MOJOM_H_
#define TYPEMAP_TEST_MOJOM_H_

#include <stdint.h>

#include <limits>
#include <optional>
#include <type_traits>
#include <utility>
#include "mojo/public/cpp/bindings/clone_traits.h"
#include "mojo/public/cpp/bindings/equals_traits.h"
#include "mojo/public/cpp/bindings/struct_ptr.h"
#include "mojo/public/cpp/bindings/struct_traits.h"
#include "mojo/public/cpp/bindings/union_traits.h"

#include "third_party/perfetto/include/perfetto/tracing/traced_value_forward.h"

#include "typemap.test-mojom-features.h"  // IWYU pragma: export
#include "typemap.test-mojom-shared.h"  // IWYU pragma: export
#include "typemap.test-mojom-forward.h"  // IWYU pragma: export
#include <string>
#include <vector>

#include "mojo/public/cpp/bindings/lib/control_message_handler.h"
#include "mojo/public/cpp/bindings/lib/message_size_estimator.h"
#include "mojo/public/cpp/bindings/raw_ptr_impl_ref_traits.h"






namespace golden {

class IFaceWithTypemapProxy;

template <typename ImplRefTraits>
class IFaceWithTypemapStub;

class IFaceWithTypemapRequestValidator;
class IFaceWithTypemapResponseValidator;


class IFaceWithTypemap
    : public IFaceWithTypemapInterfaceBase {
 public:
  using IPCStableHashFunction = uint32_t(*)();

  static const char Name_[];
  static IPCStableHashFunction MessageToMethodInfo_(mojo::Message& message);
  static const char* MessageToMethodName_(mojo::Message& message);
  static constexpr uint32_t Version_ = 0;
  static constexpr bool PassesAssociatedKinds_ = false;
  static constexpr bool HasUninterruptableMethods_ = false;

  using Base_ = IFaceWithTypemapInterfaceBase;
  using Proxy_ = IFaceWithTypemapProxy;

  template <typename ImplRefTraits>
  using Stub_ = IFaceWithTypemapStub<ImplRefTraits>;

  using RequestValidator_ = IFaceWithTypemapRequestValidator;
  using ResponseValidator_ = IFaceWithTypemapResponseValidator;
  enum MethodMinVersions : uint32_t {
    kEchoMinVersion = 0,
  };

// crbug.com/1340245 - this causes binary size bloat on Fuchsia, and we're OK
// with not having this data in traces there.
#if !BUILDFLAG(IS_FUCHSIA)
  struct Echo_Sym {
    NOINLINE static uint32_t IPCStableHash();
  };
#endif // !BUILDFLAG(IS_FUCHSIA)
  virtual ~IFaceWithTypemap() = default;

  using EchoCallback = base::OnceCallback<void(TypemappedPtr)>;
  using EchoMojoCallback = base::OnceCallback<void(TypemappedPtr)>;

  virtual void Echo(TypemappedPtr param, EchoCallback callback) = 0;
};



class  IFaceWithTypemapProxy
    : public IFaceWithTypemap {
 public:
  using InterfaceType = IFaceWithTypemap;

  explicit IFaceWithTypemapProxy(mojo::MessageReceiverWithResponder* receiver);
  
  void Echo(TypemappedPtr param, EchoCallback callback) final;

 private:
  mojo::MessageReceiverWithResponder* receiver_;
};
class  IFaceWithTypemapStubDispatch {
 public:
  static bool Accept(IFaceWithTypemap* impl, mojo::Message* message);
  static bool AcceptWithResponder(
      IFaceWithTypemap* impl,
      mojo::Message* message,
      std::unique_ptr<mojo::MessageReceiverWithStatus> responder);
};

template <typename ImplRefTraits =
              mojo::RawPtrImplRefTraits<IFaceWithTypemap>>
class IFaceWithTypemapStub
    : public mojo::MessageReceiverWithResponderStatus {
 public:
  using ImplPointerType = typename ImplRefTraits::PointerType;

  IFaceWithTypemapStub() = default;
  ~IFaceWithTypemapStub() override = default;

  void set_sink(ImplPointerType sink) { sink_ = std::move(sink); }
  ImplPointerType& sink() { return sink_; }

  bool Accept(mojo::Message* message) override {
    if (ImplRefTraits::IsNull(sink_))
      return false;
    return IFaceWithTypemapStubDispatch::Accept(
        ImplRefTraits::GetRawPointer(&sink_), message);
  }

  bool AcceptWithResponder(
      mojo::Message* message,
      std::unique_ptr<mojo::MessageReceiverWithStatus> responder) override {
    if (ImplRefTraits::IsNull(sink_))
      return false;
    return IFaceWithTypemapStubDispatch::AcceptWithResponder(
        ImplRefTraits::GetRawPointer(&sink_), message, std::move(responder));
  }

 private:
  ImplPointerType sink_;
};
class  IFaceWithTypemapRequestValidator : public mojo::MessageReceiver {
 public:
  bool Accept(mojo::Message* message) override;
};
class  IFaceWithTypemapResponseValidator : public mojo::MessageReceiver {
 public:
  bool Accept(mojo::Message* message) override;
};








class  Typemapped {
 public:
  template <typename T>
  using EnableIfSame = std::enable_if_t<std::is_same<Typemapped, T>::value>;
  using DataView = TypemappedDataView;
  using Data_ = internal::Typemapped_Data;

  template <typename... Args>
  static TypemappedPtr New(Args&&... args) {
    return TypemappedPtr(
        std::in_place, std::forward<Args>(args)...);
  }

  template <typename U>
  static TypemappedPtr From(const U& u) {
    return mojo::TypeConverter<TypemappedPtr, U>::Convert(u);
  }

  template <typename U>
  U To() const {
    return mojo::TypeConverter<U, Typemapped>::Convert(*this);
  }


  Typemapped();

  Typemapped(
      uint8_t field,
      std::optional<int32_t> optional,
      std::vector<std::optional<bool>> optional_container);


  ~Typemapped();

  // Clone() is a template so it is only instantiated if it is used. Thus, the
  // bindings generator does not need to know whether Clone() or copy
  // constructor/assignment are available for members.
  template <typename StructPtrType = TypemappedPtr>
  TypemappedPtr Clone() const;

  // Equals() is a template so it is only instantiated if it is used. Thus, the
  // bindings generator does not need to know whether Equals() or == operator
  // are available for members.
  template <typename T, Typemapped::EnableIfSame<T>* = nullptr>
  bool Equals(const T& other) const;

  template <typename T, Typemapped::EnableIfSame<T>* = nullptr>
  bool operator==(const T& rhs) const { return Equals(rhs); }

  template <typename T, Typemapped::EnableIfSame<T>* = nullptr>
  bool operator!=(const T& rhs) const { return !operator==(rhs); }

  template <mojo::internal::SendValidation send_validation, typename UserType>
  static std::vector<uint8_t> Serialize(UserType* input) {
    return mojo::internal::SerializeImpl<
        Typemapped::DataView, std::vector<uint8_t>, send_validation>(input);
  }

  template <typename UserType>
  static std::vector<uint8_t> Serialize(UserType* input) {
    return mojo::internal::SerializeImpl<
        Typemapped::DataView, std::vector<uint8_t>>(input);
  }

  template <typename UserType>
  static mojo::Message SerializeAsMessage(UserType* input) {
    return mojo::internal::SerializeAsMessageImpl<
        Typemapped::DataView>(input);
  }

  // The returned Message is serialized only if the message is moved
  // cross-process or cross-language. Otherwise if the message is Deserialized
  // as the same UserType |input| will just be moved to |output| in
  // DeserializeFromMessage.
  template <typename UserType>
  static mojo::Message WrapAsMessage(UserType input) {
    return mojo::Message(std::make_unique<
        internal::Typemapped_UnserializedMessageContext<
            UserType, Typemapped::DataView>>(0, 0, std::move(input)),
        MOJO_CREATE_MESSAGE_FLAG_NONE);
  }

  template <typename UserType>
  static bool Deserialize(const void* data,
                          size_t data_num_bytes,
                          UserType* output) {
    mojo::Message message;
    return mojo::internal::DeserializeImpl<Typemapped::DataView>(
        message, data, data_num_bytes, output, Validate);
  }

  template <typename UserType>
  static bool Deserialize(base::span<const uint8_t> input,
                          UserType* output) {
    return Typemapped::Deserialize(
        input.empty() ? nullptr : input.data(), input.size(), output);
  }

  template <typename UserType>
  static bool DeserializeFromMessage(mojo::Message input,
                                     UserType* output) {
    auto context = input.TakeUnserializedContext<
        internal::Typemapped_UnserializedMessageContext<
            UserType, Typemapped::DataView>>();
    if (context) {
      *output = std::move(context->TakeData());
      return true;
    }
    input.SerializeIfNecessary();
    return mojo::internal::DeserializeImpl<Typemapped::DataView>(
        input, input.payload(), input.payload_num_bytes(), output, Validate);
  }

  
  uint8_t field;
  
  std::optional<int32_t> optional;
  
  std::vector<std::optional<bool>> optional_container;

  // Serialise this struct into a trace.
  void WriteIntoTrace(perfetto::TracedValue traced_context) const;

 private:
  static bool Validate(const void* data,
                       mojo::internal::ValidationContext* validation_context);
};

// The comparison operators are templates, so they are only instantiated if they
// are used. Thus, the bindings generator does not need to know whether
// comparison operators are available for members.
template <typename T, Typemapped::EnableIfSame<T>* = nullptr>
bool operator<(const T& lhs, const T& rhs);

template <typename T, Typemapped::EnableIfSame<T>* = nullptr>
bool operator<=(const T& lhs, const T& rhs) {
  return !(rhs < lhs);
}

template <typename T, Typemapped::EnableIfSame<T>* = nullptr>
bool operator>(const T& lhs, const T& rhs) {
  return rhs < lhs;
}

template <typename T, Typemapped::EnableIfSame<T>* = nullptr>
bool operator>=(const T& lhs, const T& rhs) {
  return !(lhs < rhs);
}

template <typename StructPtrType>
TypemappedPtr Typemapped::Clone() const {
  return New(
      mojo::Clone(field),
      mojo::Clone(optional),
      mojo::Clone(optional_container)
  );
}

template <typename T, Typemapped::EnableIfSame<T>*>
bool Typemapped::Equals(const T& other_struct) const {
  if (!mojo::Equals(this->field, other_struct.field))
    return false;
  if (!mojo::Equals(this->optional, other_struct.optional))
    return false;
  if (!mojo::Equals(this->optional_container, other_struct.optional_container))
    return false;
  return true;
}

template <typename T, Typemapped::EnableIfSame<T>*>
bool operator<(const T& lhs, const T& rhs) {
  if (lhs.field < rhs.field)
    return true;
  if (rhs.field < lhs.field)
    return false;
  if (lhs.optional < rhs.optional)
    return true;
  if (rhs.optional < lhs.optional)
    return false;
  if (lhs.optional_container < rhs.optional_container)
    return true;
  if (rhs.optional_container < lhs.optional_container)
    return false;
  return false;
}


}  // golden

namespace mojo {


template <>
struct  StructTraits<::golden::Typemapped::DataView,
                                         ::golden::TypemappedPtr> {
  static bool IsNull(const ::golden::TypemappedPtr& input) { return !input; }
  static void SetToNull(::golden::TypemappedPtr* output) { output->reset(); }

  static decltype(::golden::Typemapped::field) field(
      const ::golden::TypemappedPtr& input) {
    return input->field;
  }

  static decltype(::golden::Typemapped::optional) optional(
      const ::golden::TypemappedPtr& input) {
    return input->optional;
  }

  static const decltype(::golden::Typemapped::optional_container)& optional_container(
      const ::golden::TypemappedPtr& input) {
    return input->optional_container;
  }

  static bool Read(::golden::Typemapped::DataView input, ::golden::TypemappedPtr* output);
};

}  // namespace mojo

#endif  // TYPEMAP_TEST_MOJOM_H_