// Copyright 2016 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

module mojo.test;

// TODO(yzshen): Rename *WithTraits* types to something more readable.

struct NestedStructWithTraits {
  int32 value;
};

enum EnumWithTraits {
  VALUE_0,
  VALUE_1
};

struct StructWithTraits {
  EnumWithTraits f_enum;
  bool f_bool;
  uint32 f_uint32;
  uint64 f_uint64;
  string f_string;
  string f_string2;
  array<string> f_string_array;
  array<string> f_string_set;
  NestedStructWithTraits f_struct;
  array<NestedStructWithTraits> f_struct_array;
  map<string, NestedStructWithTraits> f_struct_map;
};

struct StructWithUnreachableTraits {
  bool ignore_me;
};

// Test that this container can be cloned.
struct StructWithTraitsContainer {
  StructWithTraits f_struct;
};

// Maps to a pass-by-value trivial struct.
struct TrivialStructWithTraits {
  int32 value;
};

// Maps to a move-only struct.
struct MoveOnlyStructWithTraits {
  handle f_handle;
};

// The custom type for MoveOnlyStructWithTraits is not clonable. Test that
// this container can compile as long as Clone() is not used.
struct MoveOnlyStructWithTraitsContainer {
  MoveOnlyStructWithTraits f_struct;
};

struct StructWithTraitsForUniquePtr {
  int32 f_int32;
};

union UnionWithTraits {
  int32 f_int32;
  NestedStructWithTraits f_struct;
};

interface TraitsTestService {
  EchoStructWithTraits(StructWithTraits s) => (StructWithTraits passed);

  EchoTrivialStructWithTraits(TrivialStructWithTraits s) =>
      (TrivialStructWithTraits passed);

  EchoMoveOnlyStructWithTraits(MoveOnlyStructWithTraits s) =>
      (MoveOnlyStructWithTraits passed);

  EchoNullableMoveOnlyStructWithTraits(MoveOnlyStructWithTraits? s) =>
      (MoveOnlyStructWithTraits? passed);

  EchoEnumWithTraits(EnumWithTraits e) => (EnumWithTraits passed);

  EchoStructWithTraitsForUniquePtr(StructWithTraitsForUniquePtr e) => (
      StructWithTraitsForUniquePtr passed);

  EchoNullableStructWithTraitsForUniquePtr(StructWithTraitsForUniquePtr? e) => (
      StructWithTraitsForUniquePtr? passed);

  EchoUnionWithTraits(UnionWithTraits u) => (UnionWithTraits passed);
};

interface TestUnserializedStruct {
  PassUnserializedStruct(StructWithUnreachableTraits s)
      => (StructWithUnreachableTraits passed);
};

// Test that specifying default value for a typemapped enum field works.
struct EnumWithTraitsContainer {
  EnumWithTraits f_field = EnumWithTraits.VALUE_1;
};

struct StructForceSerialize {
  int32 value;
};

struct StructNestedForceSerialize {
  StructForceSerialize force;
};

interface ForceSerializeTester {
  SendForceSerializedStruct(StructForceSerialize s)
      => (StructForceSerialize passed);
  SendNestedForceSerializedStruct(StructNestedForceSerialize s)
      => (StructNestedForceSerialize passed);
};
