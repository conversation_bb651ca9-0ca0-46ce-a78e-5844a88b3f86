[handles]10

[dist4]message_header  // num_bytes
[u4]0                  // version
[u4]0                  // interface ID
[u4]5                  // name
[u4]0                  // flags
[u4]0                  // padding
[anchr]message_header

[dist4]method5_params  // num_bytes
[u4]0                  // version
[dist8]param0_ptr      // param0
[u4]9                  // param1
[u4]0                  // padding
[anchr]method5_params

[anchr]param0_ptr
[dist4]struct_e      // num_bytes
[u4]0                // version
[dist8]struct_d_ptr  // struct_d
[u4]1                // data_pipe_consumer: It is smaller than those handles
                     // in |message_pipe_array|, which is wrong.
[u4]0                // padding
[anchr]struct_e

[anchr]struct_d_ptr
[dist4]struct_d           // num_bytes
[u4]0                     // version
[dist8]message_pipes_ptr  // message_pipes
[anchr]struct_d

[anchr]message_pipes_ptr
[dist4]message_pipe_array  // num_bytes
[u4]2                      // num_elements
[u4]3
[u4]4
[anchr]message_pipe_array
