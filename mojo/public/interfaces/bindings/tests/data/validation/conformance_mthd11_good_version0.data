[dist4]message_header  // num_bytes
[u4]0                  // version
[u4]0                  // interface ID
[u4]11                 // name
[u4]0                  // flags
[u4]0                  // padding
[anchr]message_header

[dist4]method11_params  // num_bytes
[u4]0                   // version
[dist8]param0_ptr       // param0
[anchr]method11_params

[anchr]param0_ptr
[dist4]struct_g         // num_bytes
[u4]0                   // version
[s4]123                 // i
[u4]0                   // padding
[anchr]struct_g
