[dist4]message_header  // num_bytes
[u4]2                  // version
[u4]0                  // interface ID
[u4]3                  // name
[u4]0                  // flags
[u4]0                  // padding
[u8]0                  // request_id
[dist8]payload
[dist8]payload_interface_ids
[anchr]message_header

[anchr]payload
[dist4]method3_params  // num_bytes
[u4]0                  // version
[dist8]param0_ptr      // param0
[anchr]method3_params

[anchr]param0_ptr
[dist4]associated_interface_array  // num_bytes
[u4]3                              // num_elements
[u4]2                              // interface ID index
[u4]14                             // version
[u4]3                              // interface ID index
[u4]0                              // version
[u4]0                              // interface ID index : It is smaller than
                                   // the first element above, which is wrong.
[u4]18                             // version
[anchr]associated_interface_array

[anchr]payload_interface_ids
[dist4]interface_id_array  // num_bytes
[u4]4                      // num_elements : It is okay to have IDs that are not
                           // referred to.
[u4]4
[u4]5
[u4]8
[u4]19
[anchr]interface_id_array
