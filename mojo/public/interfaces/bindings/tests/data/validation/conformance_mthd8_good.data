[dist4]message_header  // num_bytes
[u4]0                  // version
[u4]0                  // interface ID
[u4]8                  // name
[u4]0                  // flags
[u4]0                  // padding
[anchr]message_header

[dist4]method8_params  // num_bytes
[u4]0                  // version
[dist8]param0_ptr      // param0
[anchr]method8_params

[anchr]param0_ptr
[dist4]array_param       // num_bytes
[u4]3                    // num_elements
[u8]0                    // A null pointer, which is okay.
[dist8]nested_array_ptr
[u8]0                    // A null pointer, which is okay.
[anchr]array_param

[anchr]nested_array_ptr
[dist4]nested_array  // num_bytes
[u4]1                // num_elements
[dist8]string_ptr
[anchr]nested_array

[anchr]string_ptr
[dist4]string  // num_bytes
[u4]5          // num_elements
0 1 2 3 4
[anchr]string
