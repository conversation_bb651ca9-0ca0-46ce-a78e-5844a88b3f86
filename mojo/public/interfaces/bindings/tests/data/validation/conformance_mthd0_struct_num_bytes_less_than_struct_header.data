[dist4]message_header  // num_bytes
[u4]0                  // version
[u4]0                  // interface ID
[u4]0                  // name
[u4]0                  // flags
[u4]0                  // padding
[anchr]message_header

[u4]4                  // num_bytes: Less than the size of struct header.
[u4]0                  // version
[f]-1                  // param0
[u4]0                  // padding
