// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

[JavaPackage="org.chromium.mojo.bindings.test.mojom.test_structs"]
module mojo.test;

struct Rect {
  int32 x;
  int32 y;
  int32 width;
  int32 height;
};

// A copy of Rect that is typemapped differently in the chromium and blink
// variants.
struct TypemappedRect {
  int32 x;
  int32 y;
  int32 width;
  int32 height;
};

// A copy of Rect that is typemapped to the same custom type in the chromium and
// blink variants.
struct SharedTypemappedRect {
  int32 x;
  int32 y;
  int32 width;
  int32 height;
};