mojo.internal.Union(
    {{union.name}}Spec.$, '{{union.name}}',
    {
{%- for field in union.fields %}
      '{{field.name}}': {
        'ordinal': {{field.ordinal}},
        'type': {{field.kind|spec_type}},
{%-    if field.kind.is_nullable %}
        'nullable': true,
{%-    endif %}
      },
{%- endfor %}
    });

export interface {{union.name}} {
{%- for field in union.fields %}
  {{field.name}}?: {{field.kind|ts_type_maybe_nullable}},
{%- endfor %}
}
