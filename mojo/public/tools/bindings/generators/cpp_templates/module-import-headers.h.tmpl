// Copyright 2019 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

{%- from 'cpp_macros.tmpl' import include_guard, variant_path %}

{%- set header_guard = include_guard('IMPORT_HEADERS', module.path, variant) %}

#ifndef {{header_guard}}
#define {{header_guard}}

{%- for import in imports %}
#include "{{variant_path(import.path, variant)}}.h"
#include "{{variant_path(import.path, variant)}}-import-headers.h"
{%- endfor %}

#endif  // {{header_guard}}
