// Copyright 2018 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

{%- from 'cpp_macros.tmpl' import
  include_guard, namespace_begin, namespace_end %}

{%- set header_guard = include_guard("SHARED_MESSAGE_IDS", module.path) %}

#ifndef {{header_guard}}
#define {{header_guard}}

#include <stdint.h>

{{namespace_begin(namespaces_as_array)}}
namespace messages {

{% for interface in interfaces %}
enum class {{interface.name}} : uint32_t {
{%-   for method in interface.methods -%}
{%-     set method_name = "k%s"|format(method.name) -%}
{%-     if method.ordinal_comment %}
// {{method.ordinal_comment}}
{%-     endif %}
  {{method_name}} = {{method.ordinal}},
{%-  endfor %}
};
{%- endfor %}

}  // namespace messages
{{namespace_end(namespaces_as_array)}}

#endif  // {{header_guard}}
