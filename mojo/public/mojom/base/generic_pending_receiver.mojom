// Copyright 2019 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

module mojo_base.mojom;

// Convenience helper to wrap the pairing of a receiving pipe endpoint and the
// name of the interface expected by the remote endpoint.
//
// This should be used sparingly, in cases where APIs need to dynamically pass
// different types of receivers that cannot or should not be known at compile
// time.
//
// NOTE: This type may be exposed to external binaries, so all changes MUST
// preserve backward-compatibility.
[Stable]
struct GenericPendingReceiver {
  // The name of the interface which defines the messages to be received by
  // |receiving_pipe|.
  string interface_name@0;

  // A message pipe endpoint which is expected to receive messages defined by
  // the interface named by |interface_name| above. This should be bound to
  // an implementation of the named interface.
  handle<message_pipe> receiving_pipe@1;
};
