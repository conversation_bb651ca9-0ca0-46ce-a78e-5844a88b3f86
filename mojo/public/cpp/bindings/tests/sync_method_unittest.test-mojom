// Copyright 2019 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

module mojo.test.sync_method_unittest.mojom;

enum PongSendMode {
  // Pongs sent by a Ponger will use the PongAsync message only.
  kAsync,

  // Pongs sent by a Ponger will use the sync Pong message, and the
  // corresponding Ping/PingNoInterrupt will not be sent until the Pong
  // messages are sent and acknowledged. Must only be used in cases where we
  // expect the Pong messages to be dispatchable even if the destination thread
  // is waiting in a Ping/PingNoInterrupt.
  kSyncBlockReply,

  // Pongs sent by a Ponger will use the sync Pong message, but the reply to
  // the corresponding Ping/PingNoInterrupt will be sent immediately after the
  // Pong sends are initiated, without waiting. This is only used in cases where
  // we don't intend for the Pong messages to be dispatchable during the
  // corresponding Ping/PingNoInterrupt sync wait.
  kSyncDoNotBlockReply,
};

interface Pinger {
  [Sync] BindAssociated(pending_associated_receiver<Pinger> receiver) => ();

  [Sync] SetPonger(PongSendMode send_mode,
                   pending_remote<Ponger> ponger) => ();
  [Sync] SetSamePipePonger(
      PongSendMode send_mode,
      pending_associated_remote<Ponger> same_pipe_ponger) => ();
  [Sync] Ping() => ();
  [Sync, NoInterrupt] PingNoInterrupt() => ();
};

// An Pinger with no associated interface support.
interface SimplePinger {
  [Sync] SimplePing() => ();
  [Sync, NoInterrupt] SimplePingNoInterrupt() => ();
};

interface Ponger {
  [Sync] Pong() => ();
  PongAsync();
};

interface SyncService {
  [Sync] SyncCall() => ();
};

interface NoSync {
  Method() => ();

  BindNoSync(pending_associated_receiver<NoSync> no_sync);
  BindOneSync(pending_associated_receiver<OneSync> one_sync);
};

interface OneSync {
  Method() => ();

  [Sync]
  SyncMethod() => ();

  BindNoSync(pending_associated_receiver<NoSync> no_sync);
  BindOneSync(pending_associated_receiver<OneSync> one_sync);
};
