# Mojo Java System API
This document is a subset of the [Mojo documentation](/mojo/README.md).

[TOC]

## Overview

This document provides a brief guide to Java Mojo bindings usage with example
code snippets.

For a detailed API references please consult the class definitions in
[this directory](https://cs.chromium.org/chromium/src/mojo/public/java/system/src/org/chromium/mojo/system/).

*TODO: Make the contents of this document less non-existent.*

## Message Pipes

## Data Pipes

## Shared Buffers

## Native Platform Handles (File Descriptors, Windows Handles, *etc.*)

## Watchers

